import { Router } from 'express';
import { z } from 'zod';
import { createClient } from '@supabase/supabase-js';
import { requireSupabaseAuth } from './routes';

// Initialize Supabase Admin Client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceRoleKey) {
  throw new Error("Supabase environment variables VITE_SUPABASE_URL and SUPABASE_SERVICE_ROLE_KEY must be set.");
}
const supabase = createClient(supabaseUrl, supabaseServiceRoleKey);


// Define a new middleware to check for 'super_admin' role
const requireSuperAdmin = (req: any, res: any, next: any) => {
  if (!req.user || req.user.role !== 'super_admin') {
    return res.status(403).json({ message: 'Access denied. Super admin privileges required.' });
  }
  next();
};

export function registerDeploymentRoutes(): Router {
  const router = Router();

  /**
   * @openapi
   * tags:
   *   - name: Deployments
   *     description: Manage customer application deployments
   */

  // Zod schemas for validation
  const createDeploymentSchema = z.object({
    // customer_user_id will be taken from the authenticated user
    app_name: z.string().min(3, "App name must be at least 3 characters"),
    subdomain: z.string().min(3, "Subdomain must be at least 3 characters").regex(/^[a-z0-9-]+$/, "Subdomain can only contain lowercase letters, numbers, and hyphens."),
    template_type: z.enum(['agent_basic', 'agent_advanced', 'tool_calculator', 'tool_analytics', 'tool_custom', 'saas_starter', 'saas_pro']),
  });

  const updateDeploymentSchema = z.object({
    app_name: z.string().min(3).optional(),
    status: z.enum(['pending', 'provisioning', 'deploying', 'active', 'maintenance', 'suspended', 'failed', 'archived']).optional(),
    replit_url: z.string().url().nullish(),
    deployment_url: z.string().url().nullish(),
    github_repo_url: z.string().url().nullish(),
    custom_config: z.record(z.any()).optional(),
  });


  /**
   * @openapi
   * /api/admin/deployments:
   *   get:
   *     summary: List all customer deployments (super admin only)
   *     tags: [Deployments]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: A list of all deployments.
   *       403:
   *         description: Access denied.
   */
  router.get('/admin/deployments', requireSupabaseAuth, requireSuperAdmin, async (req, res) => {
    try {
      const { data, error } = await supabase
        .from('deployment_summary')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      res.status(200).json(data);
    } catch (error: any) {
      console.error('Error fetching all deployments:', error);
      res.status(500).json({ message: error.message });
    }
  });

  /**
   * @openapi
   * /api/deployments:
   *   get:
   *     summary: List deployments for the authenticated user
   *     tags: [Deployments]
   *     security:
   *       - bearerAuth: []
   *     responses:
   *       200:
   *         description: A list of the user's deployments.
   */
  router.get('/deployments', requireSupabaseAuth, async (req: any, res) => {
    try {
      // The 'my_deployments' view is protected by RLS, so it automatically
      // filters for the currently authenticated user.
      const { data, error } = await supabase
        .from('my_deployments')
        .select('*')
        .order('created_at', { ascending: false });

      if (error) throw error;

      res.status(200).json(data || []);
    } catch (error: any) {
      console.error(`Error fetching deployments for user ${req.user.id}:`, error);
      res.status(500).json({ message: error.message });
    }
  });
  
  /**
   * @openapi
   * /api/deployments/{id}:
   *   get:
   *     summary: Get a single deployment by ID
   *     tags: [Deployments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *      - in: path
   *        name: id
   *        required: true
   *        schema:
   *          type: string
   *          format: uuid
   *     responses:
   *       200:
   *         description: Deployment details.
   *       404:
   *         description: Deployment not found.
   */
  router.get('/deployments/:id', requireSupabaseAuth, async (req: any, res) => {
    const { id } = req.params;
    try {
        // RLS on 'my_deployments' view ensures users can only access their own.
        const { data, error } = await supabase
            .from('my_deployments')
            .select('*')
            .eq('id', id)
            .single();

        if (error && error.code === 'PGRST116') {
             return res.status(404).json({ message: 'Deployment not found or access denied.' });
        }
        if (error) throw error;

        res.status(200).json(data);
    } catch (error: any) {
        console.error(`Error fetching deployment ${id}:`, error);
        res.status(500).json({ message: error.message });
    }
  });


  /**
   * @openapi
   * /api/deployments:
   *   post:
   *     summary: Create a new deployment record
   *     tags: [Deployments]
   *     security:
   *       - bearerAuth: []
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/CreateDeployment'
   *     responses:
   *       201:
   *         description: Deployment created successfully.
   *       400:
   *         description: Invalid input.
   */
  router.post('/deployments', requireSupabaseAuth, async (req: any, res) => {
    try {
      const parsedBody = createDeploymentSchema.safeParse(req.body);
      if (!parsedBody.success) {
        return res.status(400).json({ message: 'Invalid input.', errors: parsedBody.error.errors });
      }

      const deploymentData = {
        ...parsedBody.data,
        customer_user_id: req.user.id, // Set the owner to the authenticated user
      };

      const { data, error } = await supabase
        .from('customer_app_deployments')
        .insert(deploymentData)
        .select()
        .single();

      if (error) throw error;

      res.status(201).json(data);
    } catch (error: any) {
      // Handle unique constraint violation for subdomain
      if (error.code === '23505') {
        return res.status(409).json({ message: 'This subdomain is already taken.' });
      }
      console.error('Error creating deployment:', error);
      res.status(500).json({ message: error.message });
    }
  });
  
  /**
   * @openapi
   * /api/deployments/{id}:
   *   put:
   *     summary: Update a deployment
   *     tags: [Deployments]
   *     security:
   *       - bearerAuth: []
   *     parameters:
   *      - in: path
   *        name: id
   *        required: true
   *        schema:
   *          type: string
   *          format: uuid
   *     requestBody:
   *       required: true
   *       content:
   *         application/json:
   *           schema:
   *             $ref: '#/components/schemas/UpdateDeployment'
   *     responses:
   *       200:
   *         description: Deployment updated successfully.
   *       400:
   *         description: Invalid input.
   *       404:
   *         description: Deployment not found.
   */
  router.put('/deployments/:id', requireSupabaseAuth, async (req, res) => {
    const { id } = req.params;
    try {
      const parsedBody = updateDeploymentSchema.safeParse(req.body);
      if (!parsedBody.success) {
        return res.status(400).json({ message: 'Invalid input.', errors: parsedBody.error.errors });
      }

      // RLS policies ensure only an owner/admin or super_admin can update.
      const { data, error } = await supabase
        .from('customer_app_deployments')
        .update(parsedBody.data)
        .eq('id', id)
        .select()
        .single();

      if (error) throw error;

      res.status(200).json(data);
    } catch (error: any) {
      console.error(`Error updating deployment ${id}:`, error);
      res.status(500).json({ message: error.message });
    }
  });
  
  /**
   * @openapi
   * /api/deployment-templates:
   *   get:
   *     summary: List all available deployment templates
   *     tags: [Deployments]
   *     responses:
   *       200:
   *         description: A list of active deployment templates.
   */
  router.get('/deployment-templates', async (req, res) => {
    try {
      const { data, error } = await supabase
        .from('deployment_templates')
        .select('*')
        .eq('is_active', true)
        .order('name');
        
      if (error) throw error;

      res.status(200).json(data || []);
    } catch (error: any) {
      console.error('Error fetching deployment templates:', error);
      res.status(500).json({ message: error.message });
    }
  });

  return router;
} 