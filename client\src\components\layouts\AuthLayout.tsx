import { ReactNode } from 'react';
import { Logo } from '@/components/ui/logo';
import { Link } from 'wouter';

interface AuthLayoutProps {
  children: ReactNode;
  title?: string;
  subtitle?: string;
  showBackToHome?: boolean;
  className?: string;
}

/**
 * AuthLayout - Layout for authentication pages
 * 
 * Provides minimal, focused structure for auth pages including:
 * - Centered card design with branding
 * - Optional title and subtitle
 * - Link back to home page
 * - Accessible form structure
 * - Responsive design
 * 
 * @param children - Auth form content to display
 * @param title - Main heading for the auth page
 * @param subtitle - Optional subtitle text
 * @param showBackToHome - Whether to show link back to homepage (default: true)
 * @param className - Optional CSS classes for the content card
 */
export function AuthLayout({ 
  children, 
  title, 
  subtitle, 
  showBackToHome = true,
  className = '' 
}: AuthLayoutProps) {
  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 flex items-center justify-center px-4 py-8">
      {/* Skip link for accessibility */}
      <a
        href="#auth-main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-white text-black px-4 py-2 rounded z-50"
      >
        Skip to main content
      </a>

      <div className="w-full max-w-md">
        {/* Back to home link */}
        {showBackToHome && (
          <div className="text-center mb-8">
            <Link href="/">
              <a className="inline-flex items-center text-cyan-400 hover:text-cyan-300 transition-colors text-sm">
                ← Back to Agent Factory
              </a>
            </Link>
          </div>
        )}

        {/* Auth card */}
        <main
          id="auth-main-content"
          role="main"
          className={`bg-white/10 backdrop-blur-md border border-white/20 rounded-2xl shadow-2xl p-8 ${className}`}
          tabIndex={-1}
        >
          {/* Logo and branding */}
          <div className="text-center mb-8">
            <div className="inline-block mb-4">
              <Logo size="lg" showText={false} />
            </div>
            <div className="text-white">
              <h1 className="text-2xl font-bold mb-2">
                {title || 'Agent Factory'}
              </h1>
              {subtitle && (
                <p className="text-gray-300 text-sm">
                  {subtitle}
                </p>
              )}
            </div>
          </div>

          {/* Auth form content */}
          <div className="space-y-6">
            {children}
          </div>
        </main>

        {/* Additional branding/links */}
        <div className="text-center mt-6 text-sm text-gray-400">
          <p>© 2024 Agent Factory. All rights reserved.</p>
        </div>
      </div>
    </div>
  );
}