import type { Express } from "express";
import { createServer, type Server } from "http";
import { type IStorage } from "./storage";
import { 
  generateSitemap, 
  generateRobotsTxt, 
  generateNewsSitemap, 
  generateImageSitemap, 
  validateSitemap,
  type SitemapStats 
} from "./sitemap";
import { 
  insertBlogPostSchema,
  updateBlogPostSchema,
  type User,
  type BlogPost
} from "@shared/schema";
import { z } from "zod";
import rateLimit from 'express-rate-limit';
import { createClient } from '@supabase/supabase-js';

// Initialize Supabase Admin Client
const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabase: any = null;

if (!supabaseUrl || !supabaseServiceRoleKey) {
    console.warn("Supabase server environment variables not set. Server-side auth will not work.");
} else {
    try {
        supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
    } catch (error) {
        console.error("Failed to initialize Supabase client:", error);
    }
}

declare module 'express' {
  interface Request {
    user?: User;
    tenantId?: number;
    tenantSlug?: string;
    tenantRole?: string;
  }
}

// New JWT Authentication Middleware
export const requireSupabaseAuth = async (req: any, res: any, next: any) => {
    if (!supabase) {
        console.error('Supabase not configured. Missing environment variables:', {
            supabaseUrl: !!supabaseUrl,
            supabaseServiceRoleKey: !!supabaseServiceRoleKey
        });
        return res.status(500).json({ 
            message: 'Authentication service not available. Please configure Supabase environment variables.',
            details: 'Missing VITE_SUPABASE_URL or SUPABASE_SERVICE_ROLE_KEY'
        });
    }

    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return res.status(401).json({ message: 'Authentication required: No token provided.' });
    }

    const jwt = authHeader.split(' ')[1];
    
    try {
        const { data: { user: authUser }, error } = await supabase.auth.getUser(jwt);

        if (error) {
            console.error('Supabase auth error:', error);
            return res.status(401).json({ message: `Authentication failed: ${error.message}` });
        }

        if (!authUser) {
             return res.status(401).json({ message: 'Authentication failed: Invalid token.' });
        }

         // Fetch user profile from database
         console.log('Looking up user profile for external_id:', authUser.id);
         const { data: userProfile, error: profileError } = await supabase
             .from('users')
             .select('id, email, first_name, last_name, business_name, role, is_active, created_at, updated_at, external_id')
             .eq('external_id', authUser.id)
             .single();
         
         console.log('User profile query result:', { userProfile, profileError });
 
         if (profileError || !userProfile) {
             console.error('Error fetching user profile for auth user:', { authUserId: authUser.id, error: profileError });
             return res.status(403).json({ message: 'User profile not found or inaccessible.' });
         }
  
          // Create user object from the trusted database record
          const user: User = {
             id: userProfile.id,
             externalId: userProfile.external_id,
             email: userProfile.email || '',
             firstName: userProfile.first_name || '',
             lastName: userProfile.last_name || '',
             businessName: userProfile.business_name || '',
             role: userProfile.role as any,
             isActive: userProfile.is_active,
             createdAt: new Date(userProfile.created_at),
             updatedAt: new Date(userProfile.updated_at || userProfile.created_at),
          };
  
        console.log('Authenticated user:', { email: user.email, role: user.role });
        req.user = user; // Attach user to request
        next();
    } catch (error: any) {
        console.error('Auth middleware error:', error);
        res.status(500).json({ message: `Internal server error: ${error.message}` });
    }
};

// Tenant Context Middleware
const requireTenantContext = async (req: any, res: any, next: any) => {
    if (!supabase) {
        return res.status(500).json({ message: 'Tenant service not available. Please configure Supabase environment variables.' });
    }

    // This middleware must run AFTER requireSupabaseAuth
    if (!req.user) {
        return res.status(401).json({ message: "Authentication required for tenant access" });
    }

    try {
        // Extract tenant ID from header
        let tenantId = req.headers['x-tenant-id'];
        let tenantSlug = null;

        // If no tenant ID in header, try to extract from subdomain
        if (!tenantId) {
            const hostname = req.hostname || req.get('host') || '';
            console.log('Processing hostname for tenant:', hostname);
            
            // Extract subdomain (first part before first dot)
            const subdomain = hostname.split('.')[0];
            
            // Skip main domains and API domains
            if (subdomain && subdomain !== 'www' && subdomain !== 'api' && subdomain !== 'localhost') {
                console.log('Extracted subdomain:', subdomain);
                
                // Look up tenant ID from subdomain slug
                const { data, error } = await supabase
                    .from('tenants')
                    .select('id, slug, status')
                    .eq('slug', subdomain)
                    .eq('status', 'active')
                    .single();
                
                if (data && !error) {
                    tenantId = data.id;
                    tenantSlug = data.slug;
                    console.log('Found tenant from subdomain:', { id: tenantId, slug: tenantSlug });
                } else {
                    console.log('No active tenant found for subdomain:', subdomain, error);
                }
            }
        } else {
            // If tenant ID provided in header, validate it and get slug
            const { data, error } = await supabase
                .from('tenants')
                .select('id, slug, status')
                .eq('id', tenantId)
                .eq('status', 'active')
                .single();
            
            if (data && !error) {
                tenantSlug = data.slug;
                console.log('Validated tenant from header:', { id: tenantId, slug: tenantSlug });
            } else {
                console.log('Invalid tenant ID in header:', tenantId, error);
                return res.status(400).json({ message: 'Invalid tenant ID provided' });
            }
        }

        // For super_admin, tenant context is optional (they can access platform-wide resources)
        if (req.user.role === 'super_admin') {
            req.tenantId = tenantId ? parseInt(tenantId) : null;
            req.tenantSlug = tenantSlug;
            req.tenantRole = 'owner'; // Super admins have owner-level access to all tenants
            console.log('Super admin access granted, tenant context:', { tenantId: req.tenantId, tenantSlug: req.tenantSlug });
            return next();
        }

        // For non-super_admin users, tenant context is required
        if (!tenantId) {
            return res.status(400).json({ 
                message: 'Tenant context required. Please provide X-Tenant-ID header or access via tenant subdomain.' 
            });
        }

        // Verify user has access to this tenant
        const { data: membershipData, error: membershipError } = await supabase
            .from('tenant_memberships')
            .select('role, is_active')
            .eq('tenant_id', tenantId)
            .eq('user_id', req.user.externalId) // Use Supabase user ID
            .eq('is_active', true)
            .single();

        if (membershipError || !membershipData) {
            console.log('Access denied to tenant:', { tenantId, userId: req.user.externalId, error: membershipError });
            return res.status(403).json({ 
                message: 'Access denied to this tenant. You are not a member of this organization.' 
            });
        }

        // Set tenant context
        req.tenantId = parseInt(tenantId);
        req.tenantSlug = tenantSlug;
        req.tenantRole = membershipData.role;

        console.log('Tenant context set:', { 
            tenantId: req.tenantId, 
            tenantSlug: req.tenantSlug, 
            tenantRole: req.tenantRole,
            userEmail: req.user.email 
        });

        next();
    } catch (error: any) {
        console.error('Tenant middleware error:', error);
        res.status(500).json({ message: `Tenant processing error: ${error.message}` });
    }
};

// Convenience function to require both auth and tenant context
export const requireTenantAuth = [requireSupabaseAuth, requireTenantContext];

// Middleware to check specific permissions (now uses req.user)
export function requirePermission(permission: string) {
  return (req: any, res: any, next: any) => {
    // This middleware must run AFTER requireSupabaseAuth
    if (!req.user) {
      console.error('Permission check failed: No user attached to request');
      return res.status(401).json({ message: "Authentication required" });
    }

    console.log(`Checking permission "${permission}" for user role "${req.user.role}"`);
    const hasPermission = checkUserPermission(req.user.role, permission);
    console.log(`Permission result: ${hasPermission}`);
    
    if (!hasPermission) {
      console.error(`Access denied: User role "${req.user.role}" lacks permission "${permission}"`);
      return res.status(403).json({ message: `Insufficient permissions. Required: ${permission}, User role: ${req.user.role}` });
    }
    
    next();
  };
}

function checkUserPermission(userRole: string, permission: string): boolean {
  const roles: { [key: string]: string[] } = {
    lead: ['read_public'],
    customer: ["read_profile", "update_profile"],
    support: ["read_profile", "update_profile", "view_users", "view_invoices"],
    billing_admin: ["read_profile", "update_profile", "manage_invoices", "view_users"],
    sales: ["read_profile", "update_profile", "view_leads", "view_customers"],
    blog_admin: ["read_profile", "update_profile", "manage_blog"],
    super_admin: ['manage_users', 'manage_tenants', 'manage_billing', 'manage_settings', 'manage_content', 'impersonate_users', 'manage_workspaces', 'view_admin_stats'],
  };
  
  const userPermissions = roles[userRole] || [];
  
  // Super admins have all permissions
  if (userRole === 'super_admin') {
    return true;
  }
  
  return userPermissions.includes(permission);
}

export async function registerRoutes(app: Express, storage: IStorage): Promise<Server> {
  app.put("/api/auth/profile", requireSupabaseAuth, async (req: any, res: any) => {
    try {
      const updateData = z.object({
        firstName: z.string().min(1),
        lastName: z.string().min(1),
        email: z.string().email(),
      }).parse(req.body);

      // req.user is attached by requireSupabaseAuth
      const user = await storage.updateUser(req.user.id, updateData);
      res.json(user);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors[0].message });
      }
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Add a simple health check endpoint
  app.get("/api/health", (req, res) => {
    res.json({ 
      status: "ok", 
      timestamp: new Date().toISOString(),
      environment: app.get("env")
    });
  });

  // Tenant context test endpoint
  app.get("/api/tenant/context", requireTenantAuth, async (req: any, res: any) => {
    res.json({
      message: "Tenant context successfully retrieved",
      user: {
        id: req.user.id,
        email: req.user.email,
        role: req.user.role
      },
      tenant: {
        id: req.tenantId,
        slug: req.tenantSlug,
        userRole: req.tenantRole
      }
    });
  });

  // Get tenant by subdomain (public endpoint for tenant lookup)
  app.get("/api/tenants/by-subdomain/:subdomain", async (req, res) => {
    if (!supabase) {
      return res.status(500).json({ message: 'Tenant service not available. Please configure Supabase environment variables.' });
    }

    try {
      const { subdomain } = req.params;
      
      if (!subdomain || subdomain.trim() === '') {
        return res.status(400).json({ message: 'Subdomain parameter is required' });
      }

      // Look up tenant by subdomain slug
      const { data, error } = await supabase
        .from('tenants')
        .select('id, name, slug, status, plan, settings')
        .eq('slug', subdomain)
        .eq('status', 'active')
        .single();

      if (error) {
        console.error('Error fetching tenant by subdomain:', error);
        if (error.code === 'PGRST116') {
          // No rows returned
          return res.status(404).json({ 
            message: `Tenant not found for subdomain: ${subdomain}`,
            subdomain: subdomain
          });
        }
        return res.status(500).json({ message: 'Error fetching tenant information' });
      }

      if (!data) {
        return res.status(404).json({ 
          message: `Tenant not found for subdomain: ${subdomain}`,
          subdomain: subdomain
        });
      }

      // Return tenant information (excluding sensitive data)
      res.json({
        id: data.id,
        name: data.name,
        slug: data.slug,
        status: data.status,
        plan: data.plan,
        settings: data.settings || {}
      });

    } catch (error: any) {
      console.error('Tenant lookup error:', error);
      res.status(500).json({ message: `Internal server error: ${error.message}` });
    }
  });

  // Blog routes (public)
  app.get("/api/blog/posts", async (req, res) => {
    try {
      const posts = await storage.getBlogPosts(true); // Only published posts
      res.json(posts);
    } catch (error) {
      console.error("Error fetching published blog posts:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.get("/api/blog/posts/:slug", async (req, res) => {
    try {
      const post = await storage.getBlogPostBySlug(req.params.slug);
      if (post) {
        res.json(post);
      } else {
        res.status(404).json({ message: "Post not found" });
      }
    } catch (error) {
      console.error(`Error fetching blog post by slug ${req.params.slug}:`, error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Admin Blog Routes (protected)
  app.post("/api/admin/blog/posts", requireSupabaseAuth, requirePermission("manage_blog"), async (req: any, res: any) => {
    try {
      const postData = insertBlogPostSchema.parse(req.body);
      
      // Check if slug already exists
      const existingPost = await storage.getBlogPostBySlug(postData.slug);
      if (existingPost) {
        return res.status(400).json({ message: "A post with this slug already exists" });
      }

      const newPost = await storage.createBlogPost({ ...postData, authorId: req.user.id });
      res.status(201).json(newPost);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors[0].message });
      }
      console.error("Error creating blog post:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.put("/api/admin/blog/posts/:id", requireSupabaseAuth, requirePermission("manage_blog"), async (req, res) => {
    try {
      const id = parseInt(req.params.id, 10);
      const postData = updateBlogPostSchema.parse(req.body);
      const updatedPost = await storage.updateBlogPost(id, postData);
      res.json(updatedPost);
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({ message: error.errors[0].message });
      }
      console.error(`Error updating blog post ${req.params.id}:`, error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  app.delete("/api/admin/blog/posts/:id", requireSupabaseAuth, requirePermission("manage_blog"), async (req, res) => {
    try {
      const id = parseInt(req.params.id, 10);
      await storage.deleteBlogPost(id);
      res.status(204).send();
    } catch (error) {
      console.error(`Error deleting blog post ${req.params.id}:`, error);
      res.status(500).json({ message: "Internal server error" });
    }
  });
  
  app.get("/api/admin/blog/posts", requireSupabaseAuth, requirePermission("manage_blog"), async (req, res) => {
    try {
      const posts = await storage.getBlogPosts(false); // Get all posts (published and drafts)
      res.json(posts);
    } catch (error) {
      console.error("Error fetching all admin blog posts:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Admin Stats Route
  app.get('/api/admin/stats', requireSupabaseAuth, requirePermission('manage_system'), async (req, res) => {
    try {
      const stats = await storage.getAdminStats();
      res.json(stats);
    } catch (error) {
      console.error("Error fetching admin stats:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // User Admin Routes
  app.get('/api/admin/users', requireTenantAuth, requirePermission('manage_users'), async (req: any, res: any) => {
    try {
      const users = await storage.getAllUsers(req.tenantId);
      res.json(users);
    } catch (error) {
      console.error("Error fetching users:", error);
      res.status(500).send("Error fetching users");
    }
  });

  app.put('/api/admin/users/:id/role', requireTenantAuth, requirePermission('manage_users'), async (req: any, res: any) => {
    const { id } = req.params;
    const { role } = req.body;
    try {
      // Pass tenantId to ensure user is within the current tenant's scope
      const updatedUser = await storage.updateUser(parseInt(id), { role } as any, req.tenantId);
      res.json(updatedUser);
    } catch (error) {
      console.error(`Error updating role for user ${id}:`, error);
      res.status(500).send("Error updating user role");
    }
  });

  app.put('/api/admin/users/:id/status', requireTenantAuth, requirePermission('manage_users'), async (req: any, res: any) => {
    const { id } = req.params;
    const { isActive } = req.body;
    try {
      // Pass tenantId to ensure user is within the current tenant's scope
      const updatedUser = await storage.updateUser(parseInt(id), { isActive } as any, req.tenantId);
      res.json(updatedUser);
    } catch (error) {
      console.error(`Error updating status for user ${id}:`, error);
      res.status(500).send("Error updating user status");
    }
  });

  // Sitemap routes (public)
  const sitemapLimiter = rateLimit({
    windowMs: 15 * 60 * 1000, // 15 minutes
    max: 10, // max 10 requests per windowMs
  });

  const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 5173}`;

  app.get("/sitemap.xml", sitemapLimiter, async (req, res) => {
    const { xml } = await generateSitemap(baseUrl);
    res.header("Content-Type", "application/xml");
    res.send(xml);
  });
  
  app.get("/robots.txt", (req, res) => {
    const robots = generateRobotsTxt(baseUrl);
    res.header("Content-Type", "text/plain");
    res.send(robots);
  });

  app.get("/sitemap-news.xml", sitemapLimiter, async (req, res) => {
    const sitemap = await generateNewsSitemap(baseUrl);
    res.header("Content-Type", "application/xml");
    res.send(sitemap);
  });

  app.get("/sitemap-images.xml", sitemapLimiter, async (req, res) => {
    const sitemap = await generateImageSitemap(baseUrl);
    res.header("Content-Type", "application/xml");
    res.send(sitemap);
  });
  
  app.get("/api/admin/seo/validate-sitemap", requireSupabaseAuth, requirePermission("manage_system"), async (req, res) => {
    const { xml } = await generateSitemap(baseUrl);
    const sitemapStats = await validateSitemap(xml);
    res.json({
      message: "Sitemap validation complete",
      sitemapStats,
    });
  });

  // Workspace management endpoints
  app.get('/api/admin/workspaces', requireSupabaseAuth, requirePermission('manage_workspaces'), async (req: any, res: any) => {
    try {
      const workspaces = await storage.listAllWorkspaces();
      res.json(workspaces);
    } catch (error: any) {
      console.error('[error] Failed to fetch workspaces:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.get('/api/workspaces', requireSupabaseAuth, async (req: any, res: any) => {
    try {
      const workspaces = await storage.listUserWorkspaces(req.user.id);
      res.json(workspaces);
    } catch (error: any) {
      console.error('[error] Failed to fetch user workspaces:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.post('/api/workspaces', requireSupabaseAuth, async (req: any, res: any) => {
    try {
      const workspaceData = {
        ...req.body,
        userId: req.user.id,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      const workspace = await storage.createWorkspace(workspaceData);
      res.status(201).json(workspace);
    } catch (error: any) {
      console.error('[error] Failed to create workspace:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.get('/api/workspaces/:id', requireSupabaseAuth, async (req: any, res: any) => {
    try {
      const workspace = await storage.getWorkspaceById(req.params.id, req.user.id);
      if (!workspace) {
        return res.status(404).json({ message: 'Workspace not found' });
      }
      res.json(workspace);
    } catch (error: any) {
      console.error('[error] Failed to fetch workspace:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.put('/api/workspaces/:id', requireSupabaseAuth, async (req: any, res: any) => {
    try {
      const updateData = {
        ...req.body,
        updatedAt: new Date().toISOString()
      };
      const workspace = await storage.updateWorkspace(req.params.id, updateData);
      res.json(workspace);
    } catch (error: any) {
      console.error('[error] Failed to update workspace:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.delete('/api/workspaces/:id', requireSupabaseAuth, requirePermission('manage_workspaces'), async (req: any, res: any) => {
    try {
      await storage.deleteWorkspace(req.params.id);
      res.json({ message: 'Workspace deleted successfully' });
    } catch (error: any) {
      console.error('[error] Failed to delete workspace:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.post('/api/workspaces/:id/archive', requireSupabaseAuth, requirePermission('manage_workspaces'), async (req: any, res: any) => {
    try {
      const workspace = await storage.archiveWorkspace(req.params.id);
      res.json(workspace);
    } catch (error: any) {
      console.error('[error] Failed to archive workspace:', error);
      res.status(500).json({ message: error.message });
    }
  });

  app.get('/api/workspace-templates', async (req: any, res: any) => {
    try {
      const templates = await storage.listWorkspaceTemplates();
      res.json(templates);
    } catch (error: any) {
      console.error('[error] Failed to fetch workspace templates:', error);
      res.status(500).json({ message: error.message });
    }
  });

  const httpServer = createServer(app);

  return httpServer;
}
