import { supabase } from './supabaseClient';
import type { User, LoginCredentials, InsertUserWithPassword } from "@shared/schema";

export async function loginUser(credentials: LoginCredentials): Promise<User> {
  if (!supabase) {
    throw new Error('Authentication service not available. Please check your configuration.');
  }

  const { data, error } = await supabase.auth.signInWithPassword({
    email: credentials.email,
    password: credentials.password,
  });

  if (error) throw new Error(error.message || 'Login failed');
  if (!data.user) throw new Error('Login succeeded but no user data returned.');

  // Convert Supabase user to our User type
  const user: User = {
    id: parseInt(data.user.id.slice(-8), 16), // Convert UUID to number for compatibility
    externalId: data.user.id,
    email: data.user.email || '',
    firstName: data.user.user_metadata?.first_name || '',
    lastName: data.user.user_metadata?.last_name || '',
    businessName: data.user.user_metadata?.business_name || '',
    role: data.user.user_metadata?.role || 'lead',
    isActive: true,
    createdAt: new Date(data.user.created_at),
    updatedAt: new Date(data.user.updated_at || data.user.created_at),
  };

  return user;
}

export async function registerUser(userData: InsertUserWithPassword): Promise<User> {
  if (!supabase) {
    throw new Error('Authentication service not available. Please check your configuration.');
  }

  const { data, error } = await supabase.auth.signUp({
    email: userData.email,
    password: userData.password,
    options: {
      data: { // This data will be in the auth.users.raw_user_meta_data column
        first_name: userData.firstName,
        last_name: userData.lastName,
        business_name: userData.businessName,
        role: userData.role // Pass role during signup
      },
    },
  });

  if (error) throw new Error(error.message || 'Registration failed');
  if (!data.user) throw new Error('Registration succeeded but no user data returned.');

  // Convert Supabase user to our User type
  const user: User = {
    id: parseInt(data.user.id.slice(-8), 16), // Convert UUID to number for compatibility
    externalId: data.user.id,
    email: data.user.email || '',
    firstName: data.user.user_metadata?.first_name || '',
    lastName: data.user.user_metadata?.last_name || '',
    businessName: data.user.user_metadata?.business_name || '',
    role: data.user.user_metadata?.role || 'lead',
    isActive: true,
    createdAt: new Date(data.user.created_at),
    updatedAt: new Date(data.user.updated_at || data.user.created_at),
  };

  return user;
}

export async function logoutUser(): Promise<void> {
  const { error } = await supabase.auth.signOut();
  
  if (error) {
    throw new Error(error.message || 'Logout failed');
  }
}

export async function getCurrentUser(): Promise<User | null> {
  const { data: { session }, error: sessionError } = await supabase.auth.getSession();

  if (sessionError) {
    console.error('Error getting session:', sessionError.message);
    return null;
  }

  if (!session) {
    return null;
  }

  // Use Supabase auth user data directly
  const user: User = {
    id: parseInt(session.user.id.slice(-8), 16),
    externalId: session.user.id,
    email: session.user.email || '',
    firstName: session.user.user_metadata?.first_name || '',
    lastName: session.user.user_metadata?.last_name || '',
    businessName: session.user.user_metadata?.business_name || '',
    role: session.user.user_metadata?.role || 'lead',
    isActive: true,
    createdAt: new Date(session.user.created_at),
    updatedAt: new Date(session.user.updated_at || session.user.created_at),
  };
  
  return user;
}

export function hasPermission(userRole: string, permission: string): boolean {
  const permissions = {
    lead: ["read_profile", "update_profile"],
    customer: ["read_profile", "update_profile"],
    support: ["read_profile", "update_profile", "view_users", "view_invoices"],
    billing_admin: ["read_profile", "update_profile", "manage_invoices", "view_users"],
    sales: ["read_profile", "update_profile", "view_leads", "view_customers"],
    blog_admin: ["read_profile", "update_profile", "manage_blog"],
    super_admin: ["read_profile", "update_profile", "manage_users", "manage_blog", "manage_system", "manage_invoices"]
  };
  
  return permissions[userRole as keyof typeof permissions]?.includes(permission) || false;
}
