import express, { type Request, Response, NextFunction } from "express";
import cors from "cors";
import { registerRoutes } from "./routes";
import { registerDeploymentRoutes } from "./deploymentRoutes";
import { SupabaseStorage } from "./storage-supabase";
import { createServer } from "http";
import { setupVite, serveStatic, log } from "./vite";

const app = express();
const server = createServer(app);

// Create storage instance using Supabase
const storage = new SupabaseStorage();

const port = process.env.PORT || 5173;

// Canonical domain redirect middleware for SEO
// Redirects all requests to www.agent-factory.io
// Reference: Express.js redirect docs and Context7 canonicalization best practices
app.use((req: Request, res: Response, next: NextFunction) => {
  const host = req.headers.host;
  
  // Redirect all .co and .io domains to www.agent-factory.io
  const shouldRedirect = host === 'agent-factory.co' || 
                        host === 'agent-factory.co:80' || 
                        host === 'agent-factory.co:443' ||
                        host === 'www.agent-factory.co' || 
                        host === 'www.agent-factory.co:80' || 
                        host === 'www.agent-factory.co:443' ||
                        host === 'agent-factory.io' || 
                        host === 'agent-factory.io:80' || 
                        host === 'agent-factory.io:443';
  
  if (shouldRedirect) {
    const protocol = req.secure || req.headers['x-forwarded-proto'] === 'https' ? 'https' : 'http';
    const redirectUrl = `${protocol}://www.agent-factory.io${req.originalUrl}`;
    
    // 301 permanent redirect to preserve SEO value
    return res.redirect(301, redirectUrl);
  }
  
  next();
});

app.use(express.json());
app.use(express.urlencoded({ extended: false }));

// Configure CORS with comprehensive domain support
const allowedOrigins = [
    // Local development
    'http://localhost:5173',
    'http://localhost:3000',
    'http://localhost:5000',
    'http://127.0.0.1:5173',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5000',
    
    // Production domains
    'https://www.agent-factory.io',
    'https://agent-factory.io',
    'https://api.agent-factory.io',
    
    // Regex patterns for dynamic domains
    /^https?:\/\/.*\.agent-factory\.app$/, // Customer subdomains
    /^https?:\/\/.*\.replit\.dev$/, // Replit development domains
    /^https?:\/\/.*\.replit\.app$/, // Replit app domains  
    /^https?:\/\/.*\.repl\.co$/, // Legacy Replit domains
    /^https?:\/\/.*\.replit\.com$/, // Additional Replit domains
];

app.use(cors({
    origin: function(origin, callback) {
        // Log the origin for debugging
        console.log('CORS check for origin:', origin);
        
        // Allow requests with no origin (mobile apps, Postman, etc.)
        if (!origin) {
            console.log('No origin - allowing request');
            return callback(null, true);
        }
        
        // In development, be more permissive
        if (process.env.NODE_ENV === 'development' || app.get('env') === 'development') {
            console.log('Development mode - allowing origin:', origin);
            return callback(null, true);
        }
        
        // Check against allowed origins
        const isAllowed = allowedOrigins.some(allowedOrigin => {
            if (typeof allowedOrigin === 'string') {
                return allowedOrigin === origin;
            } else {
                return allowedOrigin.test(origin);
            }
        });
        
        if (isAllowed) {
            console.log('Origin allowed:', origin);
            callback(null, true);
        } else {
            console.error('Origin BLOCKED by CORS:', origin);
            console.log('Allowed origins:', allowedOrigins);
            callback(new Error(`Not allowed by CORS: ${origin}`));
        }
    },
    credentials: true,
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Tenant-ID'],
}));

app.use((req, res, next) => {
  const start = Date.now();
  const path = req.path;
  let capturedJsonResponse: Record<string, any> | undefined = undefined;

  const originalResJson = res.json;
  res.json = function (bodyJson, ...args) {
    capturedJsonResponse = bodyJson;
    return originalResJson.apply(res, [bodyJson, ...args]);
  };

  res.on("finish", () => {
    const duration = Date.now() - start;
    if (path.startsWith("/api")) {
      let logLine = `${req.method} ${path} ${res.statusCode} in ${duration}ms`;
      if (capturedJsonResponse) {
        logLine += ` :: ${JSON.stringify(capturedJsonResponse)}`;
      }

      if (logLine.length > 80) {
        logLine = logLine.slice(0, 79) + "…";
      }

      log(logLine);
      
      // Log errors for debugging
      if (res.statusCode >= 400) {
        log(`Error details: ${JSON.stringify(capturedJsonResponse)}`, "error");
      }
    }
  });

  next();
});

// Register the main API routes
registerRoutes(app, storage);

// Register the new deployment API routes
app.use('/api', registerDeploymentRoutes());

// WebSocket connection handling removed

(async () => {
  // importantly only setup vite in development and after
  // setting up all the other routes so the catch-all route
  // doesn't interfere with the other routes
  if (app.get("env") === "development") {
    await setupVite(app, server);
  } else {
    serveStatic(app);
  }

  // ALWAYS serve the app on port 5000
  // this serves both the API and the client.
  // It is the only port that is not firewalled.
  const port = 5000;
  server.listen({
    port,
    host: "0.0.0.0",
    reusePort: true,
  }, () => {
    log(`serving on port ${port}`);
  });
})();
