import { useForm } from "react-hook-form";
import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { <PERSON><PERSON> } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { useLocation } from "wouter";
import { 
  User, 
  Mail, 
  Shield, 
  Calendar,
  Save,
  Lock,
  Settings
} from "lucide-react";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useEffect } from "react";
import { z } from "zod";

const profileUpdateSchema = z.object({
  firstName: z.string().min(1, "First name is required"),
  lastName: z.string().min(1, "Last name is required"),
  email: z.string().email("Invalid email address"),
});

const passwordUpdateSchema = z.object({
  currentPassword: z.string().min(1, "Current password is required"),
  newPassword: z.string().min(8, "Password must be at least 8 characters"),
  confirmPassword: z.string().min(1, "Please confirm your password"),
}).refine((data) => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type ProfileUpdateData = z.infer<typeof profileUpdateSchema>;
type PasswordUpdateData = z.infer<typeof passwordUpdateSchema>;

export default function ProfilePage() {
  const { user, isLoading } = useAuthContext();
  const [, setLocation] = useLocation();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!isLoading && !user) {
      setLocation("/login");
    }
  }, [user, isLoading, setLocation]);

  const profileForm = useForm<ProfileUpdateData>({
    resolver: zodResolver(profileUpdateSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
    },
  });

  const passwordForm = useForm<PasswordUpdateData>({
    resolver: zodResolver(passwordUpdateSchema),
    defaultValues: {
      currentPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  useEffect(() => {
    if (user) {
      profileForm.reset({
        firstName: user.firstName,
        lastName: user.lastName,
        email: user.email,
      });
    }
  }, [user, profileForm]);

  const updateProfileMutation = useMutation({
    mutationFn: async (data: ProfileUpdateData) => {
      const response = await apiRequest("PUT", "/api/auth/profile", data);
      return response.json();
    },
    onSuccess: (updatedUser) => {
      queryClient.setQueryData(["/api/auth/me"], updatedUser);
      toast({
        title: "Success",
        description: "Profile updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updatePasswordMutation = useMutation({
    mutationFn: async (data: PasswordUpdateData) => {
      const response = await apiRequest("PUT", "/api/auth/password", {
        currentPassword: data.currentPassword,
        newPassword: data.newPassword,
      });
      return response.json();
    },
    onSuccess: () => {
      passwordForm.reset();
      toast({
        title: "Success",
        description: "Password updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const onProfileSubmit = (data: ProfileUpdateData) => {
    updateProfileMutation.mutate(data);
  };

  const onPasswordSubmit = (data: PasswordUpdateData) => {
    updatePasswordMutation.mutate(data);
  };

  const getRoleDescription = (role: string) => {
    switch (role) {
      case "super_admin":
        return "Full system access and administration";
      case "blog_admin":
        return "Blog content creation and management";
      case "user_admin":
        return "User account management and permissions";
      default:
        return "Basic platform access";
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "bg-red-600";
      case "blog_admin":
        return "bg-purple-600";
      case "user_admin":
        return "bg-blue-600";
      default:
        return "bg-gray-600";
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="animate-pulse text-[hsl(var(--text-light))]">Loading...</div>
      </div>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <div className="min-h-screen gradient-bg flex flex-col">
      <Header />
      <main className="flex-1 px-6 py-12">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="space-y-2">
            <h1 className="text-3xl font-bold text-white flex items-center space-x-3">
              <Settings className="w-8 h-8" />
              <span>Profile Settings</span>
            </h1>
            <p className="text-[hsl(var(--text-light))]">
              Manage your account information and security settings
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {/* Profile Overview */}
            <Card className="glass-card">
              <CardHeader>
                <CardTitle className="text-xl text-white flex items-center space-x-2">
                  <User className="w-5 h-5" />
                  <span>Account Overview</span>
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="text-center space-y-4">
                  <div className="w-20 h-20 mx-auto rounded-full bg-[hsl(var(--accent-cyan))] bg-opacity-20 flex items-center justify-center">
                    <User className="w-10 h-10 text-[hsl(var(--accent-cyan))]" />
                  </div>
                  <div>
                    <h3 className="text-lg font-semibold text-white">
                      {user.firstName} {user.lastName}
                    </h3>
                    <p className="text-sm text-[hsl(var(--text-light))] flex items-center justify-center space-x-1">
                      <Mail className="w-3 h-3" />
                      <span>{user.email}</span>
                    </p>
                  </div>
                </div>

                <Separator className="bg-[hsl(var(--accent-cyan))] opacity-20" />

                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[hsl(var(--text-light))]">Role:</span>
                    <Badge className={`${getRoleBadgeColor(user.role)} text-white`}>
                      {user.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[hsl(var(--text-light))]">Status:</span>
                    <Badge variant={user.isActive ? "default" : "secondary"}>
                      {user.isActive ? "Active" : "Inactive"}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-[hsl(var(--text-light))]">Member since:</span>
                    <span className="text-sm text-white">
                      {new Date(user.createdAt).toLocaleDateString()}
                    </span>
                  </div>
                </div>

                <div className="p-4 rounded-lg bg-[hsl(var(--primary-dark))] bg-opacity-50">
                  <div className="flex items-start space-x-3">
                    <Shield className="w-4 h-4 text-[hsl(var(--accent-cyan))] mt-0.5" />
                    <div>
                      <h4 className="text-sm font-medium text-white">Access Level</h4>
                      <p className="text-xs text-[hsl(var(--text-light))]">
                        {getRoleDescription(user.role)}
                      </p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Profile Edit Form & Password Change */}
            <div className="lg:col-span-2 space-y-8">
              {/* Profile Information */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white">Profile Information</CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...profileForm}>
                    <form onSubmit={profileForm.handleSubmit(onProfileSubmit)} className="space-y-6">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={profileForm.control}
                          name="firstName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[hsl(var(--text-light))]">First Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="John"
                                  className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={profileForm.control}
                          name="lastName"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[hsl(var(--text-light))]">Last Name</FormLabel>
                              <FormControl>
                                <Input
                                  placeholder="Doe"
                                  className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>
                      
                      <FormField
                        control={profileForm.control}
                        name="email"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Email Address</FormLabel>
                            <FormControl>
                              <Input
                                type="email"
                                placeholder="<EMAIL>"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="flex justify-end">
                        <Button 
                          type="submit" 
                          className="btn-primary"
                          disabled={updateProfileMutation.isPending}
                        >
                          <Save className="w-4 h-4 mr-2" />
                          {updateProfileMutation.isPending ? "Updating..." : "Update Profile"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>

              {/* Password Change */}
              <Card className="glass-card">
                <CardHeader>
                  <CardTitle className="text-xl text-white flex items-center space-x-2">
                    <Lock className="w-5 h-5" />
                    <span>Change Password</span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <Form {...passwordForm}>
                    <form onSubmit={passwordForm.handleSubmit(onPasswordSubmit)} className="space-y-6">
                      <FormField
                        control={passwordForm.control}
                        name="currentPassword"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Current Password</FormLabel>
                            <FormControl>
                              <Input
                                type="password"
                                placeholder="Enter your current password"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <FormField
                          control={passwordForm.control}
                          name="newPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[hsl(var(--text-light))]">New Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="Enter new password"
                                  className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={passwordForm.control}
                          name="confirmPassword"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-[hsl(var(--text-light))]">Confirm New Password</FormLabel>
                              <FormControl>
                                <Input
                                  type="password"
                                  placeholder="Confirm new password"
                                  className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                  {...field}
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      </div>

                      <div className="p-4 rounded-lg bg-yellow-600 bg-opacity-20 border border-yellow-600 border-opacity-30">
                        <p className="text-sm text-yellow-200">
                          <strong>Password Requirements:</strong> Must be at least 8 characters long and contain a mix of letters, numbers, and special characters.
                        </p>
                      </div>

                      <div className="flex justify-end">
                        <Button 
                          type="submit" 
                          className="btn-primary"
                          disabled={updatePasswordMutation.isPending}
                        >
                          <Lock className="w-4 h-4 mr-2" />
                          {updatePasswordMutation.isPending ? "Updating..." : "Update Password"}
                        </Button>
                      </div>
                    </form>
                  </Form>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </main>
      <Footer />
    </div>
  );
}
