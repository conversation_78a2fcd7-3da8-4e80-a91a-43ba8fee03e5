import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { SEOHead } from "@/components/SEOHead";
import { Mail, Phone, MapPin, Clock } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

export default function ContactPage() {
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    company: "",
    subject: "",
    message: ""
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { toast } = useToast();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      // Here you would typically send the form data to your backend
      // For now, we'll just show a success message
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API call
      
      toast({
        title: "Message sent successfully!",
        description: "We'll get back to you within 24 hours.",
      });
      
      // Reset form
      setFormData({
        name: "",
        email: "",
        company: "",
        subject: "",
        message: ""
      });
    } catch (error) {
      toast({
        title: "Error sending message",
        description: "Please try again or contact us directly via email.",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  return (
    <>
      <SEOHead
        title="Contact Us - Agent Factory Pro"
        description="Get in touch with Agent Factory Pro for business automation solutions. Contact our team for demos, support, and consultation."
        keywords="contact, support, demo, consultation, business automation, agent factory"
      />
      
      <div className="min-h-screen py-20 px-6">
        <div className="max-w-6xl mx-auto">
          <div className="space-y-12">
            <div className="text-center space-y-4">
              <h1 className="text-4xl md:text-5xl font-bold text-white">
                Contact Us
              </h1>
              <p className="text-lg text-[hsl(var(--text-light))] max-w-2xl mx-auto">
                Ready to transform your business with AI automation? Get in touch with our team for a personalized consultation.
              </p>
            </div>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
              {/* Contact Form */}
              <div className="glass-card p-8 rounded-xl">
                <h2 className="text-2xl font-semibold text-white mb-6">Send us a message</h2>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="name" className="text-white">Name *</Label>
                      <Input
                        id="name"
                        name="name"
                        type="text"
                        value={formData.name}
                        onChange={handleChange}
                        required
                        className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="email" className="text-white">Email *</Label>
                      <Input
                        id="email"
                        name="email"
                        type="email"
                        value={formData.email}
                        onChange={handleChange}
                        required
                        className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                      />
                    </div>
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="company" className="text-white">Company</Label>
                    <Input
                      id="company"
                      name="company"
                      type="text"
                      value={formData.company}
                      onChange={handleChange}
                      className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="subject" className="text-white">Subject *</Label>
                    <Input
                      id="subject"
                      name="subject"
                      type="text"
                      value={formData.subject}
                      onChange={handleChange}
                      required
                      className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="message" className="text-white">Message *</Label>
                    <Textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={5}
                      className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white resize-none"
                    />
                  </div>

                  <Button 
                    type="submit" 
                    className="btn-primary w-full"
                    disabled={isSubmitting}
                  >
                    {isSubmitting ? "Sending..." : "Send Message"}
                  </Button>
                </form>
              </div>

              {/* Contact Information */}
              <div className="space-y-8">
                <div className="glass-card p-6 rounded-xl">
                  <h2 className="text-2xl font-semibold text-white mb-6">Get in touch</h2>
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <Mail className="w-6 h-6 text-[hsl(var(--accent-cyan))] mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Email</h3>
                        <p className="text-[hsl(var(--text-light))]"><EMAIL></p>
                        <p className="text-[hsl(var(--text-light))] text-sm">We'll respond within 24 hours</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <Phone className="w-6 h-6 text-[hsl(var(--accent-cyan))] mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Phone</h3>
                        <p className="text-[hsl(var(--text-light))]">+****************</p>
                        <p className="text-[hsl(var(--text-light))] text-sm">Mon-Fri, 9AM-6PM EST</p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <MapPin className="w-6 h-6 text-[hsl(var(--accent-cyan))] mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Address</h3>
                        <p className="text-[hsl(var(--text-light))]">
                          123 Innovation Drive<br />
                          Tech Valley, CA 94105<br />
                          United States
                        </p>
                      </div>
                    </div>

                    <div className="flex items-start space-x-4">
                      <Clock className="w-6 h-6 text-[hsl(var(--accent-cyan))] mt-1" />
                      <div>
                        <h3 className="text-white font-medium">Business Hours</h3>
                        <p className="text-[hsl(var(--text-light))]">
                          Monday - Friday: 9:00 AM - 6:00 PM EST<br />
                          Saturday: 10:00 AM - 4:00 PM EST<br />
                          Sunday: Closed
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div className="glass-card p-6 rounded-xl">
                  <h3 className="text-xl font-semibold text-white mb-4">Quick Links</h3>
                  <div className="space-y-3">
                    <div>
                      <h4 className="text-white font-medium">Sales Inquiries</h4>
                      <p className="text-[hsl(var(--text-light))] text-sm"><EMAIL></p>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">Technical Support</h4>
                      <p className="text-[hsl(var(--text-light))] text-sm"><EMAIL></p>
                    </div>
                    <div>
                      <h4 className="text-white font-medium">Partnership</h4>
                      <p className="text-[hsl(var(--text-light))] text-sm"><EMAIL></p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}