// Tests for schema validation and data structures
import { describe, it, expect } from '@jest/globals';
import type { User, BlogPost, Tenant } from '../shared/schema';

// Mock data that should be valid
const validUser: User = {
  id: 1,
  externalId: 'test-external-id',
  email: '<EMAIL>',
  firstName: 'John',
  lastName: 'Doe',
  businessName: 'Test Business',
  role: 'customer',
  isActive: true,
  createdAt: new Date(),
  updatedAt: new Date(),
};

const validBlogPost: BlogPost = {
  id: 1,
  title: 'Test Post',
  slug: 'test-post',
  excerpt: 'Test excerpt',
  content: 'Test content',
  featuredImageUrl: null,
  metaTitle: 'Test Meta Title',
  metaDescription: 'Test meta description',
  isPublished: true,
  authorId: 1,
  createdAt: new Date(),
  updatedAt: new Date(),
  publishedAt: new Date(),
  tenantId: 1,
};

const validTenant: Tenant = {
  id: 1,
  name: 'Test Tenant',
  slug: 'test-tenant',
  status: 'active',
  domain: null,
  planConfigId: 1,
  customMaxUsers: null,
  settings: null,
  createdAt: new Date(),
  updatedAt: new Date(),
};

// Validation functions
function validateUserRole(role: string): boolean {
  const validRoles = ['lead', 'customer', 'support', 'billing_admin', 'sales', 'blog_admin', 'super_admin'];
  return validRoles.includes(role);
}

function validateTenantStatus(status: string): boolean {
  const validStatuses = ['active', 'inactive', 'suspended'];
  return validStatuses.includes(status);
}

function validateSlugFormat(slug: string): boolean {
  const slugRegex = /^[a-z0-9]+(?:-[a-z0-9]+)*$/;
  return slugRegex.test(slug);
}

describe('Schema Validation Tests', () => {
  describe('User Schema', () => {
    it('should validate user roles', () => {
      expect(validateUserRole('super_admin')).toBe(true);
      expect(validateUserRole('customer')).toBe(true);
      expect(validateUserRole('invalid_role')).toBe(false);
      expect(validateUserRole('')).toBe(false);
    });

    it('should have required fields', () => {
      expect(validUser.id).toBeDefined();
      expect(validUser.email).toBeDefined();
      expect(validUser.role).toBeDefined();
      expect(validUser.isActive).toBeDefined();
    });

    it('should validate email format in user object', () => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      expect(emailRegex.test(validUser.email)).toBe(true);
    });
  });

  describe('Blog Post Schema', () => {
    it('should have required fields', () => {
      expect(validBlogPost.title).toBeDefined();
      expect(validBlogPost.slug).toBeDefined();
      expect(validBlogPost.content).toBeDefined();
      expect(typeof validBlogPost.isPublished).toBe('boolean');
    });

    it('should validate slug format', () => {
      expect(validateSlugFormat('valid-slug')).toBe(true);
      expect(validateSlugFormat('valid-slug-123')).toBe(true);
      expect(validateSlugFormat('Invalid Slug')).toBe(false);
      expect(validateSlugFormat('invalid_slug')).toBe(false);
      expect(validateSlugFormat('-invalid-')).toBe(false);
    });

    it('should require tenant association', () => {
      expect(validBlogPost.tenantId).toBeDefined();
      expect(typeof validBlogPost.tenantId).toBe('number');
    });
  });

  describe('Tenant Schema', () => {
    it('should validate tenant status', () => {
      expect(validateTenantStatus('active')).toBe(true);
      expect(validateTenantStatus('inactive')).toBe(true);
      expect(validateTenantStatus('suspended')).toBe(true);
      expect(validateTenantStatus('invalid')).toBe(false);
    });

    it('should have required fields', () => {
      expect(validTenant.name).toBeDefined();
      expect(validTenant.slug).toBeDefined();
      expect(validTenant.status).toBeDefined();
      expect(validTenant.planConfigId).toBeDefined();
    });

    it('should validate tenant slug format', () => {
      expect(validateSlugFormat(validTenant.slug)).toBe(true);
    });
  });

  describe('Data Relationships', () => {
    it('should maintain referential integrity concepts', () => {
      // Blog post should reference valid author
      expect(validBlogPost.authorId).toBe(validUser.id);
      
      // Blog post should reference valid tenant
      expect(validBlogPost.tenantId).toBe(validTenant.id);
    });

    it('should handle nullable fields correctly', () => {
      // These fields can be null
      expect(validUser.firstName).toBeDefined(); // Can be null but defined in our test
      expect(validUser.lastName).toBeDefined();
      expect(validBlogPost.featuredImageUrl).toBeNull(); // Explicitly null in our test
    });
  });
}); 