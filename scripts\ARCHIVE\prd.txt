# Agent Factory Website - Comprehensive PRD

## Executive Summary

### Project Overview
Using the files in the reference folder as a guide, build a **modern, simplified React web application** with full user management, RBAC, and blog administration capabilities. This is a complete rebuild designed to be maintainable, cost-effective, and professionally polished while avoiding over-engineering.

### Architecture Strategy
- **Frontend**: React 18 + Tailwind CSS (minimal dependencies)
- **Backend**: Supabase (PostgreSQL + Auth + APIs)
- **Images**: Cloudinary (blog images + site assets)
- **Deployment**: Linode (static React build + Nginx)
- **Domain**: www.agent-factory.co

### Success Criteria
- Professional, modern appearance with excellent UX
- Full 4-role RBAC system (Super Admin, Blog Admin, User Admin, User)
- Complete blog management with Cloudinary integration
- User management and authentication
- Microsoft booking integration
- Fast, reliable deployment on Linode
- Maintainable codebase with minimal complexity

---

## 1. Product Requirements

### 1.1 Functional Requirements

#### **P0 (Critical) - Core Business Features**

##### **Public Pages (No Authentication Required)**
- [ ] **Homepage**
  - **Visual Design:**  
    - Implement the homepage to closely match the attached reference image (see “ModernHomePageMockup.jpg”).
    - Use a dark, deep blue/black background with a soft, subtle gradient.
    - Neon blue highlights for borders, icons, and CTAs.
    - Large, bold headline:  
      - "Empowering Your Business with Intelligent Automation"
    - Subheadline:  
      - "AI-driven solutions that streamline operations and boost productivity."
    - Central CTA button:  
      - "See Solutions" with a glowing/neon border effect.
    - "Trusted by" bar with prominent logo (e.g., "Globex").
    - Four horizontally aligned solution cards:
      - AI Agents
      - Process Automation
      - Integration Services
      - Custom Solutions
      - Each card features a minimalist neon line icon and glowing border.
    - Minimalistic header:
      - Left: Agent Factory logo
      - Center/Right: Solutions, Blog, Free Agents, Book a Demo
      - Far right: User icon/login state
      - Header has a subtle neon border.
    - Simple footer:
      - Left: Privacy, Terms, Contact
      - Right: Social icons (Twitter, LinkedIn)
    - All elements must be fully responsive and accessible (contrast, keyboard navigation, alt text).
    - **Note:** The image is a reference for style and layout; adapt as needed for responsiveness and accessibility[2].
  - Value proposition and company overview
  - Clear call-to-action for booking consultation
  - Professional branding and visual design

- [ ] **Blog Page**
  - Public blog post listing with excerpts
  - Responsive grid layout with Cloudinary-optimized images
  - "Read More" functionality for full posts
  - SEO-friendly URLs and meta tags

##### **Microsoft Booking Integration**
- [ ] **Appointment Booking Page**
  - Embedded Microsoft booking iframe
  - No custom calendar or booking logic required
  - Professional integration with site design

##### **Authentication System**
- [ ] **User Registration/Login**
  - Email/password authentication via Supabase Auth
  - Password reset functionality
  - Account verification
  - Session management

- [ ] **User Profile Management**
  - Self-service profile editing
  - Password change capability
  - Account settings

#### **P0 (Critical) - RBAC System**

##### **Role-Based Access Control (4 Roles)**
- [ ] **Super Admin**
  - Access to all features and admin panels
  - User management (create, edit, delete, role assignment)
  - Blog management (full CRUD operations)
  - System configuration and settings

- [ ] **Blog Admin**
  - Full blog management capabilities
  - Upload images via Cloudinary integration
  - Create, edit, publish, delete blog posts
  - SEO management (meta tags, slugs)

- [ ] **User Admin**
  - User account management
  - Password reset triggers
  - User role assignment (except Super Admin)
  - User activity monitoring

- [ ] **User (Regular)**
  - Access to client portal
  - View invoices and account information
  - Update own profile
  - Access premium content (if applicable)

#### **P0 (Critical) - Admin Interfaces**

##### **Blog Administration**
- [ ] **Blog Management Dashboard**
  - List all blog posts with status indicators
  - Quick actions (edit, delete, publish/unpublish)
  - Search and filter functionality

- [ ] **Blog Post Editor**
  - Rich text editor with formatting options
  - Cloudinary integration for image uploads
  - Image optimization and responsive sizing
  - SEO fields (title, meta description, slug)
  - Draft/publish status management
  - Featured image selection

- [ ] **Image Management**
  - Cloudinary browser integration
  - Image transformation and optimization
  - Alt text and caption management
  - Image library for reusable assets

##### **User Management Dashboard**
- [ ] **User Administration Panel**
  - List all users with role indicators
  - User creation and invitation system
  - Bulk user operations
  - User activity logs

- [ ] **User Profile Management**
  - Edit user information
  - Role assignment interface
  - Password reset functionality
  - Account status management (active/inactive)

#### **P1 (High Priority) - Enhanced Features**

##### **Client Portal**
- [ ] **Invoice Management**
  - View current and past invoices
  - Download invoice PDFs
  - Payment status indicators
  - Payment integration placeholder (Stripe ready)

- [ ] **Account Dashboard**
  - Account overview and status
  - Service usage summary
  - Quick access to common actions

##### **SEO & Performance**
- [ ] **Search Engine Optimization**
  - Dynamic meta tags for all pages
  - Structured data markup
  - XML sitemap generation
  - Open Graph and Twitter Card meta tags

- [ ] **Performance Optimization**
  - Lighthouse score > 90
  - Mobile-first responsive design
  - Image optimization via Cloudinary
  - Code splitting and lazy loading

#### **P2 (Medium Priority) - Future Enhancements**
- [ ] **Enhanced Blog Features**
  - Comment system
  - Blog categories and tags
  - Related posts recommendations
  - Search functionality

- [ ] **Analytics Integration**
  - Google Analytics setup
  - User behavior tracking
  - Performance monitoring

- [ ] **Email Notifications**
  - New user registration alerts
  - Password reset emails
  - Blog publication notifications

### 1.2 Technical Requirements

#### **Frontend Technology Stack**

- React 18 (latest stable)
- TypeScript (for type safety)
- Vite (build tool)
- Tailwind CSS 3.4 (utility-first CSS)
- Headless UI (accessible components)
- Lucide React (icon library)
- React Router v6 (client-side routing)
- React Context (state management)
- React Hook Form (form handling)
- @supabase/supabase-js (main client)
- @supabase/auth-ui-react (auth components)
- @cloudinary/react (React components)
- @cloudinary/url-gen (URL generation)
- Supabase (PostgreSQL + Auth + Real-time + APIs)
- Row Level Security (RLS) for RBAC enforcement
- Cloudinary (image hosting, optimization, transformations)
- Supabase Auth (built-in email handling)
- SendGrid (for custom notifications)
- ESLint + Prettier (code formatting)
- Husky (git hooks)
- TypeScript strict mode
- Vitest (unit testing)
- React Testing Library (component testing)
- Playwright (e2e testing - optional)

#### **User Experience Design**

- All public-facing pages must visually align with the attached homepage reference image for layout, color palette, and UI elements.
- Use Tailwind CSS for rapid dark mode and neon accent styling.
- Create reusable components for:
  - HeroSection
  - SolutionCard (with icon prop)
  - TrustedByBar (logo array)
  - Header and Footer (with slot for social icons)
- All icons should use Lucide or similar minimalist line icon set.
- Ensure all text is easily editable via config or CMS for future marketing updates.
- All elements must stack/resize gracefully for mobile and tablet.
- Sufficient contrast, keyboard navigation, and alt text for icons/logos.

---

## 2. Navigation Structure

### Public Navigation

#### Header:
- Logo/Brand: Agent Factory (left)
- Solutions, Blog, Free Agents, Book a Demo (center/right)
- User icon/login state (far right)

#### Footer:
- Privacy, Terms, Contact (left)
- Twitter, LinkedIn (right)

---

## 3. Homepage Wireframe (Textual)

| Agent Factory | Solutions | Blog | Free Agents | Book a Demo | [User]
| HERO SECTION (centered headline/subheadline) |
| "Empowering Your Business with Intelligent Automation" |
| "AI-driven solutions that streamline operations..." |

[ See Solutions ] (CTA)
Trusted by: [Globex logo]
------------------------------------------------------------
[AI Agents] [Process Automation] [Integration] [Custom]
(icon+title) (icon+title) (icon+title) (icon+title)
| Privacy | Terms | Contact | Twitter | LinkedIn |


---

## 4. Key Implementation Details

- Use Tailwind theme config and sample components to match the reference style.
- Implement updated homepage layout and reusable components per new PRD.
- Review for visual fidelity, accessibility, and responsiveness.
- Circulate updated PRD to all stakeholders and confirm alignment.

---

## 5. Security & Compliance

(See original PRD for all security, privacy, and compliance requirements.)

---

## 6. Testing, Monitoring, Maintenance

(See original PRD for all test strategy, monitoring, maintenance, and support documentation.)

---

## 7. Risk Assessment & KPIs

(See original PRD for all risk, mitigation, and success metrics.)

---