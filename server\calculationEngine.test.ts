// server/calculationEngine.test.ts
// Unit tests for calculationEngine.ts using Jest

import { calculateROI, ROIInput, ROIResult } from './calculationEngine';

describe('calculateROI', () => {
  it('calculates correct results for typical input', () => {
    const input: ROIInput = {
      employees: 10,
      hoursPerWeek: 20,
      hourlyCost: 30,
      automationPercentage: 0.5,
      monthlyVolume: 1000,
      errorRate: 0.05,
      costPerError: 50,
      implementationCost: 10000,
    };
    const result = calculateROI(input);
    expect(result.monthlyHoursSaved).toBeCloseTo(10 * 20 * 4.33 * 0.5, 2);
    expect(result.monthlyCostSavings).toBeCloseTo(result.monthlyHoursSaved * 30, 2);
    expect(result.annualSavings).toBeCloseTo(result.monthlyCostSavings * 12, 2);
    expect(result.paybackPeriod).toBeCloseTo(10000 / result.monthlyCostSavings, 2);
    expect(result.additionalCapacity).toBeGreaterThan(0);
    expect(result.errorReductionSavings).toBeGreaterThan(0);
  });

  it('handles zero automation gracefully', () => {
    const input: ROIInput = {
      employees: 5,
      hoursPerWeek: 10,
      hourlyCost: 25,
      automationPercentage: 0,
      monthlyVolume: 500,
      errorRate: 0.1,
      costPerError: 100,
      implementationCost: 5000,
    };
    const result = calculateROI(input);
    expect(result.monthlyHoursSaved).toBe(0);
    expect(result.monthlyCostSavings).toBe(0);
    expect(result.annualSavings).toBe(0);
    expect(result.paybackPeriod).toBeUndefined();
    expect(result.additionalCapacity).toBe(0);
    expect(result.errorReductionSavings).toBe(0);
  });

  it('throws on invalid input (negative employees)', () => {
    const input: ROIInput = {
      employees: -1,
      hoursPerWeek: 10,
      hourlyCost: 25,
      automationPercentage: 0.5,
      monthlyVolume: 500,
      errorRate: 0.1,
      costPerError: 100,
      implementationCost: 5000,
    };
    expect(() => calculateROI(input)).toThrow();
  });

  it('handles missing optional fields', () => {
    const input: ROIInput = {
      employees: 3,
      hoursPerWeek: 15,
      hourlyCost: 40,
      automationPercentage: 0.3,
      monthlyVolume: 200,
      errorRate: 0.02,
      costPerError: 0,
    };
    const result = calculateROI(input);
    expect(result.paybackPeriod).toBeUndefined();
    expect(result.errorReductionSavings).toBe(0);
  });

  it('handles edge case: 100% automation', () => {
    const input: ROIInput = {
      employees: 2,
      hoursPerWeek: 40,
      hourlyCost: 50,
      automationPercentage: 1,
      monthlyVolume: 100,
      errorRate: 0.1,
      costPerError: 200,
      implementationCost: 1000,
    };
    const result = calculateROI(input);
    expect(result.monthlyHoursSaved).toBeCloseTo(2 * 40 * 4.33 * 1, 2);
    expect(result.additionalCapacity).toBeGreaterThan(0);
  });
}); 