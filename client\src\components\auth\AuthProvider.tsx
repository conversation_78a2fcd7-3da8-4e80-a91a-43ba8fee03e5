import { createContext, useContext, useEffect, useState, useRef } from "react";
import { supabase, crossDomainAuth } from "@/lib/supabaseClient";
import { Session } from '@supabase/auth-js';
import type { User as AppUser, LoginCredentials, InsertUserWithPassword } from "@shared/schema";
import { loginUser, registerUser } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";

interface AuthContextType {
  user: AppUser | null;
  session: Session | null;
  isLoading: boolean;
  login: (credentials: LoginCredentials, redirectToSubdomain?: string) => Promise<void>;
  register: (userData: InsertUserWithPassword) => Promise<void>;
  logout: () => Promise<void>;
  isLoggingIn: boolean;
  isRegistering: boolean;
  // Cross-domain authentication methods
  loginWithRedirect: (credentials: LoginCredentials, targetSubdomain: string) => Promise<void>;
  handleAuthCallback: () => Promise<void>;
  getCurrentDomain: () => any;
}

const AuthContext = createContext<AuthContextType>({ 
    user: null, 
    session: null, 
    isLoading: true,
    login: async () => {},
    register: async () => {},
    logout: async () => {},
    isLoggingIn: false,
    isRegistering: false,
    loginWithRedirect: async () => {},
    handleAuthCallback: async () => {},
    getCurrentDomain: () => null
});

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<AppUser | null>(null);
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isLoggingIn, setIsLoggingIn] = useState(false);
  const [isRegistering, setIsRegistering] = useState(false);
  const { toast } = useToast();
  const authStateChangePromiseRef = useRef<Promise<void> | null>(null);
  const authStateChangeResolveRef = useRef<(() => void) | null>(null);

  useEffect(() => {
    if (!supabase) {
      console.warn("Supabase not available, authentication disabled");
      setIsLoading(false);
      return;
    }

    // Check for transferred auth state on page load (cross-domain)
    const transferredAuth = crossDomainAuth.retrieveAuthState();
    if (transferredAuth) {
      console.log('Found transferred auth state, restoring session...');
      
      // Restore the session from transferred data
      supabase.auth.setSession({
        access_token: transferredAuth.access_token,
        refresh_token: transferredAuth.refresh_token
      }).then(() => {
        crossDomainAuth.cleanupAuthState();
        console.log('Cross-domain auth session restored successfully');
      }).catch((error: any) => {
        console.error('Failed to restore transferred session:', error);
        crossDomainAuth.cleanupAuthState();
      });
    }

    // Set up the real-time auth listener
    const { data: authListener } = supabase.auth.onAuthStateChange(
      async (_event: any, session: any) => {
        console.log('Auth state change:', _event, session?.user?.email);
        setIsLoading(true);
        setSession(session);
        
        if (session?.user) {
          // Get role from JWT claims (set by auth hook)
          let userRole = 'lead';
          
          if (session?.access_token) {
            try {
              const payload = JSON.parse(atob(session.access_token.split('.')[1]));
              userRole = payload.user_role || 'lead';
              console.log('JWT payload user_role:', payload.user_role);
            } catch (e) {
              console.warn('Could not decode JWT:', e);
            }
          }

          const userProfile: AppUser = {
            id: parseInt(session.user.id.slice(-8), 16),
            externalId: session.user.id,
            email: session.user.email || '',
            firstName: session.user.user_metadata?.first_name || '',
            lastName: session.user.user_metadata?.last_name || '',
            businessName: session.user.user_metadata?.business_name || '',
            role: userRole as any,
            isActive: true,
            createdAt: new Date(session.user.created_at),
            updatedAt: new Date(session.user.updated_at || session.user.created_at),
          };
          setUser(userProfile);
        } else {
          setUser(null);
        }
        setIsLoading(false);
        
        // Resolve any pending auth state change promise
        if (authStateChangeResolveRef.current) {
          authStateChangeResolveRef.current();
          authStateChangeResolveRef.current = null;
          authStateChangePromiseRef.current = null;
        }
      }
    );

    return () => {
      authListener?.subscription.unsubscribe();
    };
  }, []);

  const login = async (credentials: LoginCredentials, redirectToSubdomain?: string) => {
    setIsLoggingIn(true);
    try {
      // Create a promise that resolves when auth state change completes
      authStateChangePromiseRef.current = new Promise<void>((resolve) => {
        authStateChangeResolveRef.current = resolve;
      });

      // Sign in with Supabase
      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) throw new Error(error.message || 'Login failed');
      
      // Wait for the auth state change to complete
      await authStateChangePromiseRef.current;
      
      // Handle cross-domain redirect if specified
      if (redirectToSubdomain && data.session) {
        crossDomainAuth.storeAuthState(data.session, redirectToSubdomain);
        const redirectUrl = crossDomainAuth.getRedirectUrl(redirectToSubdomain);
        if (redirectUrl) {
          window.location.href = redirectUrl;
          return; // Don't show toast since we're redirecting
        }
      }
      
      toast({
        title: "Welcome back!",
        description: "You have successfully signed in.",
      });
    } catch (error) {
      // Clean up promise refs on error
      authStateChangeResolveRef.current = null;
      authStateChangePromiseRef.current = null;
      
      toast({
        title: "Login failed",
        description: error instanceof Error ? error.message : "An error occurred during login",
        variant: "destructive",
      });
    } finally {
      setIsLoggingIn(false);
    }
  };

  const loginWithRedirect = async (credentials: LoginCredentials, targetSubdomain: string) => {
    await login(credentials, targetSubdomain);
  };

  const handleAuthCallback = async () => {
    if (!supabase) return;
    
    try {
      // Handle URL-based auth callback (for OAuth flows)
      const { data, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('Auth callback error:', error);
        return;
      }
      
      if (data.session) {
        console.log('Auth callback successful, session established');
        
        // Check if we need to redirect to a different domain
        const targetDomain = window.localStorage.getItem('supabase_auth_target');
        if (targetDomain) {
          crossDomainAuth.storeAuthState(data.session, targetDomain);
          const redirectUrl = crossDomainAuth.getRedirectUrl(targetDomain);
          if (redirectUrl) {
            window.location.href = redirectUrl;
            return;
          }
        }
      }
    } catch (error) {
      console.error('Error handling auth callback:', error);
    }
  };

  const register = async (userData: InsertUserWithPassword) => {
    setIsRegistering(true);
    try {
      const user = await registerUser(userData);
      setUser(user);
      toast({
        title: "Welcome to Agent Factory!",
        description: "Your account has been created successfully.",
      });
    } catch (error) {
      toast({
        title: "Registration failed",
        description: error instanceof Error ? error.message : "An error occurred during registration",
        variant: "destructive",
      });
    } finally {
      setIsRegistering(false);
    }
  };

  const logout = async () => {
    if (!supabase) return;
    
    try {
      // Sign out from Supabase (this will work across all domains)
      await supabase.auth.signOut();
      
      // Clean up any cross-domain auth state
      crossDomainAuth.cleanupAuthState();
      
      setUser(null);
      setSession(null);
      
      toast({
        title: "Goodbye!",
        description: "You have been signed out successfully.",
      });
    } catch (error) {
      toast({
        title: "Logout failed",
        description: error instanceof Error ? error.message : "An error occurred during logout",
        variant: "destructive",
      });
    }
  };

  const getCurrentDomain = () => {
    return crossDomainAuth.getCurrentDomain();
  };

  const value = {
    session,
    user,
    isLoading,
    login,
    register,
    logout,
    isLoggingIn,
    isRegistering,
    loginWithRedirect,
    handleAuthCallback,
    getCurrentDomain,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

// This hook is for components to get the current user and session state.
export function useAuthContext() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuthContext must be used within an AuthProvider");
  }
  return context;
}
