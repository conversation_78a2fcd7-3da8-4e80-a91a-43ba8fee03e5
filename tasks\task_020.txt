# Task ID: 20
# Title: Implement End-to-End Testing for Multi-Tenant Functionality
# Status: pending
# Dependencies: 11, 12, 13, 14
# Priority: medium
# Description: Create comprehensive end-to-end tests for the multi-tenant architecture
# Details:
1. Set up end-to-end testing framework (<PERSON><PERSON>, Playwright, etc.)
2. Create test scenarios for multi-tenant functionality
3. Implement tests for cross-domain authentication
4. Test tenant isolation and data security
5. Create performance tests for multi-tenant scenarios
6. Implement CI/CD pipeline for automated testing
7. Example Cypress test for multi-tenant authentication:
```javascript
// cypress/integration/multi-tenant-auth.spec.js
describe('Multi-Tenant Authentication', () => {
  const mainSite = 'https://www.agent-factory.io';
  const tenant1 = 'https://customer1.agent-factory.app';
  const tenant2 = 'https://customer2.agent-factory.app';
  const testUser = {
    email: '<EMAIL>',
    password: 'securePassword123'
  };
  
  beforeEach(() => {
    // Clear cookies and localStorage before each test
    cy.clearCookies();
    cy.clearLocalStorage();
  });
  
  it('should login on main site and maintain session on tenant subdomain', () => {
    // Login on main site
    cy.visit(`${mainSite}/login`);
    cy.get('#email').type(testUser.email);
    cy.get('#password').type(testUser.password);
    cy.get('button[type="submit"]').click();
    
    // Verify login successful
    cy.url().should('include', '/dashboard');
    cy.get('.user-profile').should('contain', testUser.email);
    
    // Visit tenant subdomain
    cy.visit(tenant1);
    
    // Should be automatically logged in
    cy.get('.loading-auth').should('not.exist');
    cy.get('.user-profile').should('contain', testUser.email);
    
    // Check tenant context
    cy.get('.tenant-name').should('contain', 'Customer 1');
  });
  
  it('should maintain tenant isolation', () => {
    // Login on main site
    cy.visit(`${mainSite}/login`);
    cy.get('#email').type(testUser.email);
    cy.get('#password').type(testUser.password);
    cy.get('button[type="submit"]').click();
    
    // Visit tenant 1 and create a project
    cy.visit(`${tenant1}/projects/new`);
    cy.get('#project-name').type('Tenant 1 Project');
    cy.get('button[type="submit"]').click();
    
    // Verify project created
    cy.get('.project-list').should('contain', 'Tenant 1 Project');
    
    // Visit tenant 2
    cy.visit(`${tenant2}/projects`);
    
    // Should not see tenant 1's project
    cy.get('.project-list').should('not.contain', 'Tenant 1 Project');
  });
  
  it('should logout from all domains when logging out', () => {
    // Login on main site
    cy.visit(`${mainSite}/login`);
    cy.get('#email').type(testUser.email);
    cy.get('#password').type(testUser.password);
    cy.get('button[type="submit"]').click();
    
    // Logout
    cy.get('.logout-button').click();
    
    // Verify logged out
    cy.url().should('include', '/login');
    
    // Visit tenant subdomain
    cy.visit(tenant1);
    
    // Should be logged out
    cy.url().should('include', '/login');
  });
});
```

# Test Strategy:
Run end-to-end tests across different environments (development, staging, production). Test with different user roles and tenant configurations. Verify all critical user flows work correctly in the multi-tenant architecture.
