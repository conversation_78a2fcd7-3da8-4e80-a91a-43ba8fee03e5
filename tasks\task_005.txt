# Task ID: 5
# Title: Implement JWT Authentication Middleware
# Status: done
# Dependencies: 1
# Priority: high
# Description: Create middleware to verify Supabase JWT tokens and extract user and tenant information
# Details:
1. Create authentication middleware for Express.js
2. Implement JWT verification using Supabase public key
3. Extract user claims from verified token
4. Extract and validate tenant information
5. Set tenant context for RLS
6. Handle authentication errors
7. Example middleware implementation:
```javascript
const { createClient } = require('@supabase/supabase-js');

const authMiddleware = async (req, res, next) => {
  const authHeader = req.headers.authorization;
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return res.status(401).json({ error: 'Unauthorized' });
  }
  
  const token = authHeader.split(' ')[1];
  const tenantId = req.headers['x-tenant-id'];
  
  try {
    // Verify JWT token
    const { data, error } = await supabase.auth.getUser(token);
    if (error) throw error;
    
    // Set user and tenant context
    req.user = data.user;
    req.tenantId = tenantId;
    
    // Set PostgreSQL RLS context
    await supabase.rpc('set_tenant_context', { tenant_id: tenantId });
    
    next();
  } catch (error) {
    return res.status(401).json({ error: 'Invalid token' });
  }
};
```

# Test Strategy:
Test middleware with valid and invalid tokens. Verify user information is correctly extracted. Test with missing or invalid tenant headers. Verify RLS context is correctly set.
