# AgentFactoryPro

A full-stack web application built with React, Express, TypeScript, and PostgreSQL.

## Features

- 🔐 User authentication with role-based access control
- 📝 Blog management system
- 👥 User management for admins
- 🎨 Modern UI with Tailwind CSS and Radix UI
- 🔄 Real-time data updates with TanStack Query

## Tech Stack

- **Frontend**: React 18, TypeScript, TanStack Query, Tailwind CSS
- **Backend**: Express.js, PostgreSQL, Drizzle ORM
- **Authentication**: Express Sessions with bcrypt
- **Development**: Vite, TSX

## Getting Started

### Prerequisites

- Node.js 18+
- PostgreSQL database
- npm or yarn

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install
   ```

3. Set up environment variables:
   - `DATABASE_URL` or `REPLIT_DB_URL` - PostgreSQL connection string

4. Run the development server:
   ```bash
   npm run dev
   ```

The app will be available at http://localhost:5000

## Available Scripts

- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Run production server
- `npm run check` - Type check with TypeScript
- `npm run create-admin` - Create an admin user (see Admin Setup section)

## User Roles

- **user** - Basic user access
- **user_admin** - Can manage users
- **blog_admin** - Can manage blog posts
- **super_admin** - Full system access

## Admin Setup

To create an admin user:

1. Edit `scripts/create-admin.ts` and update:
   - `adminEmail` - Your admin email address
   - `adminPassword` - A strong password (change after first login!)
   - `firstName` and `lastName` - Admin user's name

2. Run the script:
   ```bash
   npm run create-admin
   ```

3. Login with your credentials and immediately change the password

**Security Notes:**
- Always use a strong, unique password
- Change the default password immediately after first login
- Delete or secure the script after creating the admin
- Never commit credentials to version control

## API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/me` - Get current user

### Blog (Public)
- `GET /api/blog/posts` - Get published posts
- `GET /api/blog/posts/:slug` - Get post by slug

### Health Check
- `GET /api/health` - Check API status

## Using the ROI Calculation Engine (For Frontend Developers)

The backend provides a modular, extensible calculation engine for Business Process Automation ROI in `server/calculationEngine.ts`.

### How to Use

1. **Import the Calculation Function and Types**

```typescript
import { calculateROI, ROIInput, ROIResult } from '../server/calculationEngine';
```

2. **Prepare the Input Object**

```typescript
const input: ROIInput = {
  employees: 10,
  hoursPerWeek: 20,
  hourlyCost: 30,
  automationPercentage: 0.5, // 50% automation
  monthlyVolume: 1000,
  errorRate: 0.05, // 5%
  costPerError: 50, // optional
  implementationCost: 10000, // optional
};
```

3. **Call the Calculation Function**

```typescript
const result: ROIResult = calculateROI(input);
```

4. **Result Object**

The result will include:
- `monthlyHoursSaved`
- `monthlyCostSavings`
- `annualSavings`
- `paybackPeriod` (months, or null)
- `additionalCapacity` (percentage)
- `errorReductionSavings` (or null)

5. **Integration Notes**
- The calculation engine is pure and synchronous—no async/await needed.
- All input values must be numbers and within valid ranges (see JSDoc in the file).
- For what-if scenarios or visualizations, simply call `calculateROI` with different input values.
- If you need to expose this via an API endpoint, coordinate with the backend team.

6. **Example**

```typescript
const result = calculateROI({
  employees: 5,
  hoursPerWeek: 15,
  hourlyCost: 25,
  automationPercentage: 0.6,
  monthlyVolume: 500,
  errorRate: 0.03,
  costPerError: 40,
  implementationCost: 8000,
});
console.log(result);
```

## License

MIT 