Frontend Developer Task Prompt: Integrate ROI Calculation Engine
Objective:
Integrate the backend ROI calculation engine into the frontend calculator UI, enabling real-time ROI metric calculations and supporting “what if” scenario exploration.
Instructions
Read the Documentation
Review the “Using the ROI Calculation Engine (For Frontend Developers)” section in the README.md.
Review the JSDoc comments in server/calculationEngine.ts for input/output details and usage examples.
Import the Calculation Engine
Import calculateROI, ROIInput, and ROIResult from server/calculationEngine.ts into your frontend code.
Connect User Inputs
Map the calculator UI fields (employees, hours per week, hourly cost, automation %, etc.) to the ROIInput type.
Ensure all values are numbers and within valid ranges (see docs).
Call the Calculation Function
On user input or slider change, call calculateROI(input) and update the UI with the returned metrics.
For “what if” scenarios, call the function with different input values and display the comparison.
Display Results
Show all returned metrics: monthly hours saved, cost savings, annual savings, payback period, additional capacity, and error reduction savings.
Integrate pie charts or other visualizations as needed, using the calculated data.
Error Handling
Handle any errors thrown by the calculation function (e.g., invalid input) and display user-friendly messages.
(Optional) API Integration
If you need to call the calculation engine via an API endpoint instead of direct import, coordinate with the backend team.
Testing
Test with a variety of input scenarios, including edge cases.
Confirm that the UI updates in real time and all calculations are accurate.
Success Criteria
The calculator UI updates instantly as users change inputs or sliders.
All ROI metrics are displayed clearly and accurately.
“What if” scenarios and visualizations (e.g., pie charts) work as intended.
The integration is robust, user-friendly, and matches the requirements in the PRD and README.
If you have any questions or need clarification, consult the backend documentation or reach out to the backend team.