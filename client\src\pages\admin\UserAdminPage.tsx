import { useState } from "react";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { usePageSEO } from "@/components/SEOProvider";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Link, useLocation } from "wouter";
import { motion, AnimatePresence } from "framer-motion";
import { 
  UserPlus, 
  Edit, 
  Trash2, 
  ArrowLeft,
  Save,
  Shield,
  Mail,
  User as UserIcon,
  Search,
  Filter,
  ChevronDown,
  ChevronRight,
  Building2,
  X,
  Check,
  Users,
  MoreHorizontal
} from "lucide-react";
import { hasPermission } from "@/lib/auth";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertUserSchema, userRoles, type User, type InsertUser } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useEffect, useMemo } from "react";

// Constants for business identification
const AGENT_FACTORY_BUSINESS_NAME = "Agent Factory";
const UNAFFILIATED_GROUP_NAME = "Unaffiliated Users";

// Extended User type with businessName for UI development
interface UserWithBusiness extends User {
  businessName: string | null;
}

export default function UserAdminPage() {
  const { user, isLoading } = useAuthContext();
  const [, setLocation] = useLocation();

  // Dynamic SEO optimization for admin pages
  usePageSEO({
    title: 'User Management Dashboard | Agent Factory Pro',
    description: 'Comprehensive user management system with role-based access control, business grouping, and bulk operations. Manage your team efficiently with enterprise-grade tools.',
    keywords: 'user management, admin dashboard, role-based access, team management, enterprise administration',
    noindex: true, // Admin pages should not be indexed by search engines
    type: 'website'
  });
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingUser, setEditingUser] = useState<User | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [businessFilter, setBusinessFilter] = useState<string>("all");
  const [expandedBusinesses, setExpandedBusinesses] = useState<Set<string>>(new Set([AGENT_FACTORY_BUSINESS_NAME]));
  const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
  const [bulkAction, setBulkAction] = useState<string>('');
  const [showBulkConfirmDialog, setShowBulkConfirmDialog] = useState(false);
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!isLoading && !user) {
      setLocation("/login");
    } else if (!isLoading && user && !hasPermission(user.role, "manage_users")) {
      setLocation("/dashboard");
    }
  }, [user, isLoading, setLocation]);

  const { data: users, isLoading: usersLoading } = useQuery<User[]>({
    queryKey: ["/api/admin/users"],
    enabled: !!user && hasPermission(user.role, "manage_users"),
  });

  const createUserMutation = useMutation({
    mutationFn: async (data: InsertUser) => {
      const response = await apiRequest("POST", "/api/admin/users", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setIsCreateDialogOpen(false);
      toast({
        title: "Success",
        description: "User created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updateUserMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<InsertUser> }) => {
      const response = await apiRequest("PUT", `/api/admin/users/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      setEditingUser(null);
      toast({
        title: "Success",
        description: "User updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deleteUserMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/admin/users/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      toast({
        title: "Success",
        description: "User deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const createForm = useForm<InsertUser & { businessName: string }>({
    resolver: zodResolver(insertUserSchema),
    defaultValues: {
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      role: "lead",
      businessName: "",
    },
  });

  const editForm = useForm<Partial<InsertUser>>({
    defaultValues: {
      email: "",
      firstName: "",
      lastName: "",
      role: "user",
    },
  });

  useEffect(() => {
    if (editingUser) {
      editForm.reset({
        email: editingUser.email,
        firstName: editingUser.firstName,
        lastName: editingUser.lastName,
        role: editingUser.role,
      });
    }
  }, [editingUser, editForm]);

  const onCreateSubmit = (data: InsertUser) => {
    createUserMutation.mutate(data);
  };

  const onEditSubmit = (data: Partial<InsertUser>) => {
    if (editingUser) {
      updateUserMutation.mutate({ id: editingUser.id, data });
    }
  };

  const handleDeleteUser = (id: number) => {
    if (confirm("Are you sure you want to delete this user?")) {
      deleteUserMutation.mutate(id);
    }
  };

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case "super_admin":
        return "bg-red-600";
      case "blog_admin":
        return "bg-purple-600";
      case "user_admin":
        return "bg-blue-600";
      default:
        return "bg-gray-600";
    }
  };

  const getAvailableRoles = () => {
    if (user?.role === "super_admin") {
      return userRoles;
    } else if (user?.role === "user_admin") {
      return userRoles.filter(role => role !== "super_admin");
    }
    return ["user"];
  };

  // Mock function to assign businessName to users for UI development
  const getUsersWithBusiness = useMemo(() => {
    if (!users) return [];
    
    return users.map((user, index): UserWithBusiness => {
      // Mock businessName assignment based on user ID for consistent assignment
      const businessNames = [AGENT_FACTORY_BUSINESS_NAME, "Acme Corp", "Beta Solutions", null];
      const businessName = businessNames[user.id % businessNames.length] || null;
      
      return {
        ...user,
        businessName
      };
    });
  }, [users]);

  // Filter users based on search query and filters
  const filteredUsers = useMemo(() => {
    if (!getUsersWithBusiness.length) return [];
    
    return getUsersWithBusiness.filter(user => {
      // Search filter
      const matchesSearch = searchQuery === "" || 
        user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.firstName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.lastName?.toLowerCase().includes(searchQuery.toLowerCase()) ||
        `${user.firstName} ${user.lastName}`.toLowerCase().includes(searchQuery.toLowerCase()) ||
        user.businessName?.toLowerCase().includes(searchQuery.toLowerCase());
      
      // Role filter
      const matchesRole = roleFilter === "all" || user.role === roleFilter;
      
      // Status filter
      const matchesStatus = statusFilter === "all" ||
        (statusFilter === "active" && user.isActive) ||
        (statusFilter === "inactive" && !user.isActive);
      
      // Business filter
      const matchesBusiness = businessFilter === "all" || 
        (businessFilter === "unaffiliated" && !user.businessName) ||
        (businessFilter !== "unaffiliated" && user.businessName === businessFilter);
      
      return matchesSearch && matchesRole && matchesStatus && matchesBusiness;
    });
  }, [getUsersWithBusiness, searchQuery, roleFilter, statusFilter, businessFilter]);

  // Group filtered users by business name
  const usersByBusiness = useMemo(() => {
    const grouped = new Map<string, UserWithBusiness[]>();
    
    filteredUsers.forEach(user => {
      const businessKey = user.businessName || UNAFFILIATED_GROUP_NAME;
      if (!grouped.has(businessKey)) {
        grouped.set(businessKey, []);
      }
      grouped.get(businessKey)!.push(user);
    });
    
    return grouped;
  }, [filteredUsers]);

  const toggleBusinessExpansion = (businessName: string) => {
    setExpandedBusinesses(prev => {
      const newSet = new Set(prev);
      if (newSet.has(businessName)) {
        newSet.delete(businessName);
      } else {
        newSet.add(businessName);
      }
      return newSet;
    });
  };

  // Bulk selection handlers
  const toggleUserSelection = (userId: number) => {
    const newSelected = new Set(selectedUsers);
    if (newSelected.has(userId)) {
      newSelected.delete(userId);
    } else {
      newSelected.add(userId);
    }
    setSelectedUsers(newSelected);
  };

  const toggleBusinessSelection = (businessUsers: UserWithBusiness[]) => {
    const businessUserIds = businessUsers.map(u => u.id);
    const newSelected = new Set(selectedUsers);
    const allSelected = businessUserIds.every(id => newSelected.has(id));
    
    if (allSelected) {
      businessUserIds.forEach(id => newSelected.delete(id));
    } else {
      businessUserIds.forEach(id => newSelected.add(id));
    }
    setSelectedUsers(newSelected);
  };

  const clearSelection = () => {
    setSelectedUsers(new Set());
    setBulkAction('');
  };

  const handleBulkAction = () => {
    if (!bulkAction || selectedUsers.size === 0) return;
    setShowBulkConfirmDialog(true);
  };

  const executeBulkAction = async () => {
    const action = bulkAction;
    const userIds = Array.from(selectedUsers);
    
    try {
      // This will be connected to real API endpoints
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      toast({
        title: "Bulk Action Completed",
        description: `Successfully ${action}d ${userIds.length} user(s)`,
      });
      
      queryClient.invalidateQueries({ queryKey: ["/api/admin/users"] });
      clearSelection();
      setShowBulkConfirmDialog(false);
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to perform bulk action",
        variant: "destructive",
      });
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="animate-pulse text-[hsl(var(--text-light))]">Loading...</div>
      </div>
    );
  }

  if (!user || !hasPermission(user.role, "manage_users")) {
    return null;
  }

  return (
    <div className="min-h-screen gradient-bg flex flex-col">
      <Header />
      <main className="flex-1 px-6 py-12">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <Button variant="ghost" size="icon" className="text-[hsl(var(--accent-cyan))]">
                    <ArrowLeft className="w-4 h-4" />
                  </Button>
                </Link>
                <h1 className="text-3xl font-bold text-white">User Management</h1>
              </div>
              <p className="text-[hsl(var(--text-light))]">
                Create and manage user accounts and permissions
              </p>
            </div>

            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary">
                  <UserPlus className="w-4 h-4 mr-2" />
                  Create User
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                <DialogHeader>
                  <DialogTitle className="text-white">Create New User</DialogTitle>
                </DialogHeader>
                <Form {...createForm}>
                  <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={createForm.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">First Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="John"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createForm.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Last Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Doe"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={createForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={createForm.control}
                      name="password"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Password</FormLabel>
                          <FormControl>
                            <Input
                              type="password"
                              placeholder="Enter secure password"
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={createForm.control}
                        name="role"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Role</FormLabel>
                            <Select onValueChange={field.onChange} defaultValue={field.value}>
                              <FormControl>
                                <SelectTrigger className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white">
                                  <SelectValue placeholder="Select a role" />
                                </SelectTrigger>
                              </FormControl>
                              <SelectContent className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                                {getAvailableRoles().map((role) => (
                                  <SelectItem key={role} value={role} className="text-white hover:bg-[hsl(var(--secondary-dark))]">
                                    {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <FormMessage />
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={createForm.control}
                        name="businessName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Business Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter business name (e.g., Agent Factory)"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="flex justify-end">
                      <Button 
                        type="submit" 
                        className="btn-primary"
                        disabled={createUserMutation.isPending}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {createUserMutation.isPending ? "Creating..." : "Create User"}
                      </Button>
                    </div>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Search and Filter Bar */}
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[hsl(var(--text-light))] w-4 h-4" />
              <Input
                type="text"
                placeholder="Search by name or email..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white placeholder:text-[hsl(var(--text-light))]"
              />
            </div>
            
            <div className="flex gap-2">
              <Select value={roleFilter} onValueChange={setRoleFilter}>
                <SelectTrigger className="w-[180px] bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white">
                  <Filter className="w-4 h-4 mr-2" />
                  <SelectValue placeholder="Filter by role" />
                </SelectTrigger>
                <SelectContent className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                  <SelectItem value="all" className="text-white hover:bg-[hsl(var(--secondary-dark))]">All Roles</SelectItem>
                  {userRoles.map((role) => (
                    <SelectItem key={role} value={role} className="text-white hover:bg-[hsl(var(--secondary-dark))]">
                      {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger className="w-[180px] bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white">
                  <SelectValue placeholder="Filter by status" />
                </SelectTrigger>
                <SelectContent className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                  <SelectItem value="all" className="text-white hover:bg-[hsl(var(--secondary-dark))]">All Status</SelectItem>
                  <SelectItem value="active" className="text-white hover:bg-[hsl(var(--secondary-dark))]">Active</SelectItem>
                  <SelectItem value="inactive" className="text-white hover:bg-[hsl(var(--secondary-dark))]">Inactive</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Results count */}
          {filteredUsers.length > 0 && (
            <p className="text-sm text-[hsl(var(--text-light))]">
              Showing {filteredUsers.length} of {users?.length || 0} users
            </p>
          )}

          {/* Users Grouped by Business */}
          {usersLoading ? (
            <div className="space-y-6">
              {[...Array(3)].map((_, i) => (
                <Card key={i} className="glass-card animate-pulse">
                  <CardHeader>
                    <div className="h-6 bg-[hsl(var(--secondary-dark))] rounded w-1/3"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {[...Array(3)].map((_, j) => (
                        <div key={j} className="h-32 bg-[hsl(var(--secondary-dark))] rounded"></div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredUsers.length > 0 ? (
            <div className="space-y-6">
              {/* Agent Factory users first (highlighted) */}
              {usersByBusiness.has(AGENT_FACTORY_BUSINESS_NAME) && (
                <Card className="glass-card border-2 border-[hsl(var(--accent-cyan))] shadow-lg shadow-cyan-500/20">
                  <CardHeader 
                    className="cursor-pointer hover:bg-[hsl(var(--secondary-dark))]/50 transition-colors"
                    onClick={() => toggleBusinessExpansion(AGENT_FACTORY_BUSINESS_NAME)}
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-3">
                        <Checkbox
                          checked={usersByBusiness.get(AGENT_FACTORY_BUSINESS_NAME)?.every(u => selectedUsers.has(u.id)) || false}
                          onCheckedChange={() => toggleBusinessSelection(usersByBusiness.get(AGENT_FACTORY_BUSINESS_NAME) || [])}
                          className="border-[hsl(var(--accent-cyan))] data-[state=checked]:bg-[hsl(var(--accent-cyan))] data-[state=checked]:text-black"
                        />
                        <Building2 className="w-6 h-6 text-[hsl(var(--accent-cyan))]" />
                        <CardTitle className="text-xl text-[hsl(var(--accent-cyan))] font-bold">
                          {AGENT_FACTORY_BUSINESS_NAME}
                        </CardTitle>
                        <Badge className="bg-[hsl(var(--accent-cyan))] text-black font-semibold">
                          INTERNAL
                        </Badge>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className="text-sm text-[hsl(var(--text-light))]">
                          {usersByBusiness.get(AGENT_FACTORY_BUSINESS_NAME)?.length} users
                        </span>
                        {expandedBusinesses.has(AGENT_FACTORY_BUSINESS_NAME) ? (
                          <ChevronDown className="w-5 h-5 text-[hsl(var(--accent-cyan))]" />
                        ) : (
                          <ChevronRight className="w-5 h-5 text-[hsl(var(--accent-cyan))]" />
                        )}
                      </div>
                    </div>
                  </CardHeader>
                  {expandedBusinesses.has(AGENT_FACTORY_BUSINESS_NAME) && (
                    <CardContent>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        {usersByBusiness.get(AGENT_FACTORY_BUSINESS_NAME)?.map((userItem) => (
                <Card key={userItem.id} className="glass-card hover:shadow-2xl transition-all duration-300 relative">
                  <div className="absolute top-3 left-3 z-10">
                    <Checkbox
                      checked={selectedUsers.has(userItem.id)}
                      onCheckedChange={() => toggleUserSelection(userItem.id)}
                      className="border-[hsl(var(--accent-cyan))] data-[state=checked]:bg-[hsl(var(--accent-cyan))] data-[state=checked]:text-black"
                    />
                  </div>
                  <CardHeader className="pt-10">
                    <div className="flex items-start justify-between">
                      <div className="space-y-2">
                        <CardTitle className="text-lg text-white flex items-center space-x-2">
                          <UserIcon className="w-5 h-5" />
                          <span>{userItem.firstName} {userItem.lastName}</span>
                        </CardTitle>
                        <div className="flex items-center space-x-2 text-sm text-[hsl(var(--text-light))]">
                          <Mail className="w-4 h-4" />
                          <span>{userItem.email}</span>
                        </div>
                      </div>
                      <Badge 
                        className={`${getRoleBadgeColor(userItem.role)} text-white`}
                      >
                        {userItem.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-[hsl(var(--text-light))]">Status:</span>
                      <Badge variant={userItem.isActive ? "default" : "secondary"}>
                        {userItem.isActive ? "Active" : "Inactive"}
                      </Badge>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-[hsl(var(--text-light))]">Created:</span>
                      <span className="text-white">
                        {new Date(userItem.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="flex items-center justify-between pt-2">
                      <div className="flex space-x-2">
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-[hsl(var(--accent-cyan))]"
                          onClick={() => setEditingUser(userItem)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        {userItem.id !== user.id && (
                          <Button 
                            variant="ghost" 
                            size="sm" 
                            className="text-red-400"
                            onClick={() => handleDeleteUser(userItem.id)}
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        )}
                      </div>
                      <div className="flex items-center space-x-1 text-xs text-[hsl(var(--text-light))]">
                        <Shield className="w-3 h-3" />
                        <span>
                          {userItem.role === "super_admin" ? "Full Access" :
                           userItem.role === "blog_admin" ? "Blog Access" :
                           userItem.role === "user_admin" ? "User Access" : "Basic Access"}
                        </span>
                      </div>
                    </div>
                  </CardContent>
                </Card>
                        ))}
                      </div>
                    </CardContent>
                  )}
                </Card>
              )}

              {/* Other business groups */}
              {Array.from(usersByBusiness.entries())
                .filter(([businessName]) => businessName !== AGENT_FACTORY_BUSINESS_NAME)
                .sort(([a], [b]) => {
                  if (a === UNAFFILIATED_GROUP_NAME) return 1;
                  if (b === UNAFFILIATED_GROUP_NAME) return -1;
                  return a.localeCompare(b);
                })
                .map(([businessName, businessUsers]) => (
                  <Card key={businessName} className="glass-card">
                    <CardHeader 
                      className="cursor-pointer hover:bg-[hsl(var(--secondary-dark))]/50 transition-colors"
                      onClick={() => toggleBusinessExpansion(businessName)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <Checkbox
                            checked={businessUsers.every(u => selectedUsers.has(u.id))}
                            onCheckedChange={() => toggleBusinessSelection(businessUsers)}
                            className="border-white data-[state=checked]:bg-white data-[state=checked]:text-black"
                          />
                          <Building2 className="w-5 h-5 text-[hsl(var(--text-light))]" />
                          <CardTitle className="text-lg text-white">
                            {businessName}
                          </CardTitle>
                          {businessName === UNAFFILIATED_GROUP_NAME && (
                            <Badge variant="outline" className="text-[hsl(var(--text-light))] border-[hsl(var(--text-light))]">
                              NO BUSINESS
                            </Badge>
                          )}
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-[hsl(var(--text-light))]">
                            {businessUsers.length} users
                          </span>
                          {expandedBusinesses.has(businessName) ? (
                            <ChevronDown className="w-5 h-5 text-[hsl(var(--text-light))]" />
                          ) : (
                            <ChevronRight className="w-5 h-5 text-[hsl(var(--text-light))]" />
                          )}
                        </div>
                      </div>
                    </CardHeader>
                    {expandedBusinesses.has(businessName) && (
                      <CardContent>
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                          {businessUsers.map((userItem) => (
                            <Card key={userItem.id} className="glass-card hover:shadow-2xl transition-all duration-300 relative">
                              <div className="absolute top-3 left-3 z-10">
                                <Checkbox
                                  checked={selectedUsers.has(userItem.id)}
                                  onCheckedChange={() => toggleUserSelection(userItem.id)}
                                  className="border-white data-[state=checked]:bg-white data-[state=checked]:text-black"
                                />
                              </div>
                              <CardHeader className="pt-10">
                                <div className="flex items-start justify-between">
                                  <div className="space-y-2">
                                    <CardTitle className="text-lg text-white flex items-center space-x-2">
                                      <UserIcon className="w-5 h-5" />
                                      <span>{userItem.firstName} {userItem.lastName}</span>
                                    </CardTitle>
                                    <div className="flex items-center space-x-2 text-sm text-[hsl(var(--text-light))]">
                                      <Mail className="w-4 h-4" />
                                      <span>{userItem.email}</span>
                                    </div>
                                  </div>
                                  <Badge className={`${getRoleBadgeColor(userItem.role)} text-white`}>
                                    {userItem.role.replace('_', ' ').replace(/\b\w/g, (l: string) => l.toUpperCase())}
                                  </Badge>
                                </div>
                              </CardHeader>
                              <CardContent className="space-y-4">
                                <div className="flex items-center justify-between text-sm">
                                  <span className="text-[hsl(var(--text-light))]">Status:</span>
                                  <Badge variant={userItem.isActive ? "default" : "secondary"}>
                                    {userItem.isActive ? "Active" : "Inactive"}
                                  </Badge>
                                </div>
                                <div className="flex items-center justify-between text-sm">
                                  <span className="text-[hsl(var(--text-light))]">Business:</span>
                                  <span className="text-white text-xs">
                                    {userItem.businessName || "None"}
                                  </span>
                                </div>
                                <div className="flex items-center justify-between pt-2">
                                  <div className="flex space-x-2">
                                    <Button 
                                      variant="ghost" 
                                      size="sm" 
                                      className="text-[hsl(var(--accent-cyan))]"
                                      onClick={() => setEditingUser(userItem)}
                                    >
                                      <Edit className="w-4 h-4" />
                                    </Button>
                                    {userItem.id !== user?.id && (
                                      <Button 
                                        variant="ghost" 
                                        size="sm" 
                                        className="text-red-400"
                                        onClick={() => handleDeleteUser(userItem.id)}
                                      >
                                        <Trash2 className="w-4 h-4" />
                                      </Button>
                                    )}
                                  </div>
                                </div>
                              </CardContent>
                            </Card>
                          ))}
                        </div>
                      </CardContent>
                    )}
                  </Card>
                ))}
            </div>
          ) : (
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold text-white">
                {users && users.length > 0 ? "No matching users" : "No Users"}
              </h2>
              <p className="text-[hsl(var(--text-light))]">
                {users && users.length > 0 
                  ? "Try adjusting your search or filters" 
                  : "Create your first user to get started!"}
              </p>
            </div>
          )}

          {/* Edit Dialog */}
          <Dialog open={!!editingUser} onOpenChange={() => setEditingUser(null)}>
            <DialogContent className="max-w-2xl bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
              <DialogHeader>
                <DialogTitle className="text-white">Edit User</DialogTitle>
              </DialogHeader>
              {editingUser && (
                <Form {...editForm}>
                  <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={editForm.control}
                        name="firstName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">First Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="John"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={editForm.control}
                        name="lastName"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Last Name</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Doe"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={editForm.control}
                      name="email"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Email</FormLabel>
                          <FormControl>
                            <Input
                              type="email"
                              placeholder="<EMAIL>"
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={editForm.control}
                      name="role"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Role</FormLabel>
                          <Select onValueChange={field.onChange} value={field.value}>
                            <FormControl>
                              <SelectTrigger className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white">
                                <SelectValue placeholder="Select a role" />
                              </SelectTrigger>
                            </FormControl>
                            <SelectContent className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                              {getAvailableRoles().map((role) => (
                                <SelectItem key={role} value={role} className="text-white hover:bg-[hsl(var(--secondary-dark))]">
                                  {role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex justify-end">
                      <Button 
                        type="submit" 
                        className="btn-primary"
                        disabled={updateUserMutation.isPending}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {updateUserMutation.isPending ? "Updating..." : "Update User"}
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </DialogContent>
          </Dialog>
        </div>

        {/* Floating Bulk Actions Bar */}
        <AnimatePresence>
          {selectedUsers.size > 0 && (
            <motion.div
              initial={{ opacity: 0, y: 100 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 100 }}
              className="fixed bottom-6 left-1/2 transform -translate-x-1/2 z-50"
            >
              <Card className="glass-card border-[hsl(var(--accent-cyan))] shadow-2xl shadow-cyan-500/20">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                      <Users className="w-5 h-5 text-[hsl(var(--accent-cyan))]" />
                      <span className="text-white font-medium">
                        {selectedUsers.size} user{selectedUsers.size > 1 ? 's' : ''} selected
                      </span>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      <Select value={bulkAction} onValueChange={setBulkAction}>
                        <SelectTrigger className="w-40 bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] text-white">
                          <SelectValue placeholder="Choose action" />
                        </SelectTrigger>
                        <SelectContent className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))]">
                          <SelectItem value="activate" className="text-white hover:bg-[hsl(var(--secondary-dark))]">
                            <div className="flex items-center space-x-2">
                              <Check className="w-4 h-4 text-green-400" />
                              <span>Activate</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="deactivate" className="text-white hover:bg-[hsl(var(--secondary-dark))]">
                            <div className="flex items-center space-x-2">
                              <X className="w-4 h-4 text-red-400" />
                              <span>Deactivate</span>
                            </div>
                          </SelectItem>
                          <SelectItem value="delete" className="text-white hover:bg-[hsl(var(--secondary-dark))]">
                            <div className="flex items-center space-x-2">
                              <Trash2 className="w-4 h-4 text-red-400" />
                              <span>Delete</span>
                            </div>
                          </SelectItem>
                        </SelectContent>
                      </Select>
                      
                      <Button 
                        onClick={handleBulkAction}
                        disabled={!bulkAction}
                        className="bg-[hsl(var(--accent-cyan))] text-black hover:bg-[hsl(var(--accent-cyan))]/80"
                      >
                        Apply
                      </Button>
                      
                      <Button 
                        variant="ghost" 
                        onClick={clearSelection}
                        className="text-white hover:bg-[hsl(var(--secondary-dark))]"
                      >
                        Cancel
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Bulk Action Confirmation Dialog */}
        <AlertDialog open={showBulkConfirmDialog} onOpenChange={setShowBulkConfirmDialog}>
          <AlertDialogContent className="glass-card border-[hsl(var(--accent-cyan))]">
            <AlertDialogHeader>
              <AlertDialogTitle className="text-white">
                Confirm Bulk Action
              </AlertDialogTitle>
              <AlertDialogDescription className="text-[hsl(var(--text-light))]">
                Are you sure you want to {bulkAction} {selectedUsers.size} user{selectedUsers.size > 1 ? 's' : ''}? 
                This action cannot be undone.
              </AlertDialogDescription>
            </AlertDialogHeader>
            <AlertDialogFooter>
              <AlertDialogCancel className="bg-[hsl(var(--secondary-dark))] text-white border-[hsl(var(--accent-cyan))]">
                Cancel
              </AlertDialogCancel>
              <AlertDialogAction 
                onClick={executeBulkAction}
                className="bg-[hsl(var(--accent-cyan))] text-black hover:bg-[hsl(var(--accent-cyan))]/80"
              >
                Confirm
              </AlertDialogAction>
            </AlertDialogFooter>
          </AlertDialogContent>
        </AlertDialog>
      </main>
      <Footer />
    </div>
  );
}
