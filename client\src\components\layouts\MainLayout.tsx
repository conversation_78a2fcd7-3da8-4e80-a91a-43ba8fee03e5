import { ReactNode } from 'react';
import { Header } from '@/components/layout/Header';
import { Footer } from '@/components/layout/Footer';

interface MainLayoutProps {
  children: ReactNode;
  className?: string;
  showHeader?: boolean;
  showFooter?: boolean;
}

/**
 * MainLayout - Layout for public pages
 * 
 * Provides consistent structure for public-facing pages including:
 * - Site header with navigation
 * - Main content area with semantic HTML
 * - Site footer
 * - Skip link for accessibility
 * 
 * @param children - Main content to display
 * @param className - Optional CSS classes for the main content area
 * @param showHeader - Whether to display the header (default: true)
 * @param showFooter - Whether to display the footer (default: true)
 */
export function MainLayout({ 
  children, 
  className = '', 
  showHeader = true, 
  showFooter = true 
}: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* Skip link for accessibility */}
      <a
        href="#main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-white text-black px-4 py-2 rounded z-50"
      >
        Skip to main content
      </a>

      {/* Site Header */}
      {showHeader && (
        <header role="banner">
          <Header />
        </header>
      )}

      {/* Main Content Area */}
      <main 
        id="main-content" 
        role="main" 
        className={`flex-1 ${className}`}
        tabIndex={-1}
      >
        {children}
      </main>

      {/* Site Footer */}
      {showFooter && (
        <footer role="contentinfo">
          <Footer />
        </footer>
      )}
    </div>
  );
}