import { cn } from "@/lib/utils";
import agentFactoryLogo from "@assets/AgentFactoryLogo.png";
import agentFactoryLogoHorizontal from "@assets/AgentFactoryLogoHorizontal.png";

interface LogoProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  showText?: boolean;
  variant?: "vertical" | "horizontal";
}

export function Logo({ className, size = "md", showText = true, variant = "vertical" }: LogoProps) {
  const sizes = {
    sm: showText ? "h-8" : "w-8 h-8",
    md: showText ? "h-14" : "w-14 h-14", 
    lg: showText ? "h-16" : "w-16 h-16"
  };

  // Use the horizontal logo when showing text, vertical logo when icon only
  const logoSrc = (showText || variant === "horizontal") ? agentFactoryLogoHorizontal : agentFactoryLogo;

  return (
    <div className={cn("flex items-center", className)}>
      <img 
        src={logoSrc}
        alt="Agent Factory"
        className={cn(sizes[size], "object-contain")}
      />
    </div>
  );
}
