-- Migration to add the workspaces table and related objects.
-- This script should be run once on an existing database to add the new functionality.

-- 1. Create the enum for workspace status
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = 'workspace_status_enum') THEN
        CREATE TYPE public.workspace_status_enum AS ENUM ('active', 'inactive', 'archived', 'error');
    END IF;
END$$;

-- 2. Create the workspaces table
CREATE TABLE IF NOT EXISTS public.workspaces (
  id TEXT PRIMARY KEY, -- Should be a UUID generated by the application
  name TEXT NOT NULL,
  description TEXT,
  template_id TEXT NOT NULL,
  environment TEXT NOT NULL,
  status public.workspace_status_enum DEFAULT 'active' NOT NULL,
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  config JSONB DEFAULT '{}'::jsonb,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

COMMENT ON TABLE public.workspaces IS 'Stores workspace configurations for users, representing deployed or deployable agent instances.';
COMMENT ON COLUMN public.workspaces.id IS 'UUID generated by the application upon creation.';
COMMENT ON COLUMN public.workspaces.template_id IS 'Identifier for the workspace template used (e.g., ''web-app'').';
COMMENT ON COLUMN public.workspaces.config IS 'JSONB object storing workspace-specific configurations.';


-- 3. Add indexes for performance if they don't already exist
CREATE INDEX IF NOT EXISTS idx_workspaces_user_id ON public.workspaces(user_id);
CREATE INDEX IF NOT EXISTS idx_workspaces_status ON public.workspaces(status);

-- 4. Set up the trigger to automatically update the updated_at column
DO $$
BEGIN
    IF NOT EXISTS (SELECT 1 FROM pg_trigger WHERE tgname = 'update_workspaces_updated_at') THEN
        CREATE TRIGGER update_workspaces_updated_at 
          BEFORE UPDATE ON public.workspaces 
          FOR EACH ROW EXECUTE FUNCTION public.update_updated_at_column();
    END IF;
END$$;

-- 5. Enable Row Level Security (RLS)
ALTER TABLE public.workspaces ENABLE ROW LEVEL SECURITY;

-- 6. Create RLS policies and Helper Functions

-- Helper function to get user ID from Supabase auth UID
CREATE OR REPLACE FUNCTION public.get_user_id_from_auth()
RETURNS INT AS $$
DECLARE
  user_id_result INT;
BEGIN
  SELECT id INTO user_id_result
  FROM public.users
  WHERE external_id = auth.uid()::text;
  RETURN user_id_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Helper function to check a user's role against their stored role
-- This is needed for the 'super_admin' policy below.
CREATE OR REPLACE FUNCTION public.check_user_role(role_to_check public.user_role_enum)
RETURNS BOOLEAN AS $$
BEGIN
  -- Check if the current user's role in the users table matches the required role
  RETURN EXISTS (
    SELECT 1 FROM public.users
    WHERE id = (SELECT public.get_user_id_from_auth()) AND role = role_to_check
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Policies for workspaces table
DROP POLICY IF EXISTS "Users can view their own workspaces" ON public.workspaces;
CREATE POLICY "Users can view their own workspaces"
  ON public.workspaces FOR SELECT
  USING (user_id = get_user_id_from_auth());

DROP POLICY IF EXISTS "Users can create their own workspaces" ON public.workspaces;
CREATE POLICY "Users can create their own workspaces"
  ON public.workspaces FOR INSERT
  WITH CHECK (user_id = get_user_id_from_auth());

DROP POLICY IF EXISTS "Users can update their own workspaces" ON public.workspaces;
CREATE POLICY "Users can update their own workspaces"
  ON public.workspaces FOR UPDATE
  USING (user_id = get_user_id_from_auth());

DROP POLICY IF EXISTS "Users can delete their own workspaces" ON public.workspaces;
CREATE POLICY "Users can delete their own workspaces"
  ON public.workspaces FOR DELETE
  USING (user_id = get_user_id_from_auth());

DROP POLICY IF EXISTS "Super admins can manage all workspaces" ON public.workspaces;
CREATE POLICY "Super admins can manage all workspaces"
    ON public.workspaces FOR ALL
    USING (public.check_user_role('super_admin'))
    WITH CHECK (public.check_user_role('super_admin'));

-- Grant usage on the new enum and helper functions
GRANT USAGE ON TYPE public.workspace_status_enum TO anon, authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.get_user_id_from_auth() TO authenticated, service_role;
GRANT EXECUTE ON FUNCTION public.check_user_role(public.user_role_enum) TO authenticated, service_role; 