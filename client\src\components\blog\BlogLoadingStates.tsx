import { <PERSON>, <PERSON><PERSON>ontent, <PERSON>Header } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

// Blog list loading skeleton with enhanced accessibility
export const BlogListSkeleton = () => (
  <div 
    className="space-y-8"
    role="status" 
    aria-label="Loading blog posts"
  >
    {/* Search and filter skeleton */}
    <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
      <Skeleton className="h-10 w-full md:w-96" />
      <div className="flex items-center gap-2">
        <Skeleton className="h-4 w-4" />
        <Skeleton className="h-10 w-48" />
      </div>
    </div>

    {/* Results count skeleton */}
    <Skeleton className="h-4 w-48" />

    {/* Blog cards grid */}
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {[...Array(6)].map((_, i) => (
        <Card key={i} className="animate-pulse">
          <Skeleton className="h-48 rounded-t-lg" />
          <CardContent className="p-6 space-y-4">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-20" />
            </div>
            <Skeleton className="h-6 w-3/4" />
            <div className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-2/3" />
            </div>
            <div className="flex items-center justify-between">
              <Skeleton className="h-6 w-20" />
              <div className="flex gap-2">
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-20" />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>

    {/* Pagination skeleton */}
    <div className="flex items-center justify-center gap-2">
      <Skeleton className="h-9 w-20" />
      {[...Array(5)].map((_, i) => (
        <Skeleton key={i} className="h-9 w-10" />
      ))}
      <Skeleton className="h-9 w-16" />
    </div>
  </div>
);

// Individual blog post loading skeleton
export const BlogPostSkeleton = () => (
  <div 
    className="max-w-7xl mx-auto px-4 py-12"
    role="status"
    aria-label="Loading blog post"
  >
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
      {/* Main content skeleton */}
      <div className="lg:col-span-3 space-y-8">
        {/* Back button skeleton */}
        <Skeleton className="h-9 w-32" />

        {/* Header skeleton */}
        <div className="space-y-6">
          <Skeleton className="h-96 rounded-lg" />
          
          <div className="space-y-4">
            <div className="flex items-center gap-4">
              <Skeleton className="h-6 w-24" />
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
            
            <Skeleton className="h-12 w-4/5" />
            <Skeleton className="h-6 w-full" />
            <Skeleton className="h-6 w-3/4" />
          </div>
        </div>

        {/* Content skeleton */}
        <div className="space-y-4">
          {[...Array(8)].map((_, i) => (
            <div key={i} className="space-y-2">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
              <Skeleton className="h-4 w-4/5" />
            </div>
          ))}
        </div>

        {/* Social sharing skeleton */}
        <div className="p-4 bg-muted rounded-lg">
          <div className="flex items-center gap-4">
            <Skeleton className="h-4 w-32" />
            <div className="flex gap-2">
              {[...Array(4)].map((_, i) => (
                <Skeleton key={i} className="h-8 w-20" />
              ))}
            </div>
          </div>
        </div>

        {/* Author bio skeleton */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-start gap-4">
              <Skeleton className="h-16 w-16 rounded-full" />
              <div className="flex-1 space-y-2">
                <Skeleton className="h-6 w-48" />
                <Skeleton className="h-4 w-full" />
                <Skeleton className="h-4 w-4/5" />
                <div className="flex gap-2 mt-3">
                  <Skeleton className="h-8 w-24" />
                  <Skeleton className="h-8 w-20" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Related posts skeleton */}
        <div className="space-y-6">
          <Skeleton className="h-8 w-48" />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[...Array(3)].map((_, i) => (
              <Card key={i}>
                <Skeleton className="h-40 rounded-t-lg" />
                <CardContent className="p-4 space-y-2">
                  <Skeleton className="h-5 w-4/5" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-3 w-20" />
                  <Skeleton className="h-8 w-full mt-3" />
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>

      {/* Sidebar skeleton */}
      <div className="lg:col-span-1">
        <div className="sticky top-24">
          <Card>
            <CardHeader>
              <div className="flex items-center gap-2">
                <Skeleton className="h-5 w-5" />
                <Skeleton className="h-6 w-32" />
              </div>
            </CardHeader>
            <CardContent className="space-y-2">
              {[...Array(6)].map((_, i) => (
                <Skeleton key={i} className="h-4 w-full" />
              ))}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  </div>
);

// Admin blog page loading skeleton
export const BlogAdminSkeleton = () => (
  <div 
    className="space-y-6"
    role="status"
    aria-label="Loading blog administration"
  >
    {/* Header skeleton */}
    <div className="flex items-center justify-between">
      <Skeleton className="h-8 w-48" />
      <Skeleton className="h-10 w-32" />
    </div>

    {/* Stats cards skeleton */}
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {[...Array(3)].map((_, i) => (
        <Card key={i}>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <Skeleton className="h-4 w-24" />
              <Skeleton className="h-4 w-4" />
            </div>
            <Skeleton className="h-8 w-16 mt-2" />
            <Skeleton className="h-3 w-32 mt-1" />
          </CardContent>
        </Card>
      ))}
    </div>

    {/* Table skeleton */}
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <Skeleton className="h-6 w-32" />
          <div className="flex gap-2">
            <Skeleton className="h-9 w-24" />
            <Skeleton className="h-9 w-20" />
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center justify-between py-4 border-b">
              <div className="flex items-center gap-4">
                <Skeleton className="h-12 w-12" />
                <div className="space-y-1">
                  <Skeleton className="h-5 w-48" />
                  <Skeleton className="h-4 w-32" />
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Skeleton className="h-6 w-20" />
                <Skeleton className="h-8 w-8" />
                <Skeleton className="h-8 w-8" />
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  </div>
);

// Generic blog component loading fallback
export const BlogComponentSkeleton = ({ variant = "default" }: { variant?: "default" | "compact" | "detailed" }) => {
  if (variant === "compact") {
    return (
      <div className="animate-pulse space-y-4" role="status" aria-label="Loading content">
        <Skeleton className="h-6 w-3/4" />
        <Skeleton className="h-4 w-full" />
        <Skeleton className="h-4 w-2/3" />
      </div>
    );
  }

  if (variant === "detailed") {
    return (
      <div className="animate-pulse space-y-6" role="status" aria-label="Loading detailed content">
        <Skeleton className="h-48 w-full" />
        <Skeleton className="h-8 w-4/5" />
        <div className="space-y-2">
          {[...Array(4)].map((_, i) => (
            <Skeleton key={i} className="h-4 w-full" />
          ))}
        </div>
        <div className="flex gap-2">
          <Skeleton className="h-8 w-20" />
          <Skeleton className="h-8 w-24" />
        </div>
      </div>
    );
  }

  return (
    <div className="animate-pulse space-y-4" role="status" aria-label="Loading content">
      <Skeleton className="h-8 w-2/3" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-4/5" />
      <Skeleton className="h-4 w-3/4" />
    </div>
  );
};