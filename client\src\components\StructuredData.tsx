import { useEffect } from 'react';

// Base structured data interfaces
export interface BaseStructuredData {
  '@context': string;
  '@type': string;
}

export interface OrganizationSchema extends BaseStructuredData {
  '@type': 'Organization';
  name: string;
  description?: string;
  url: string;
  logo?: string;
  address?: {
    '@type': 'PostalAddress';
    streetAddress?: string;
    addressLocality?: string;
    addressRegion?: string;
    postalCode?: string;
    addressCountry?: string;
  };
  contactPoint?: {
    '@type': 'ContactPoint';
    contactType: string;
    email?: string;
    telephone?: string;
  };
  sameAs?: string[];
}

export interface ArticleSchema extends BaseStructuredData {
  '@type': 'Article';
  headline: string;
  description?: string;
  author: {
    '@type': 'Person';
    name: string;
  };
  publisher: {
    '@type': 'Organization';
    name: string;
    logo?: {
      '@type': 'ImageObject';
      url: string;
    };
  };
  datePublished: string;
  dateModified?: string;
  image?: string;
  articleBody?: string;
  keywords?: string[];
  wordCount?: number;
}

export interface ProductSchema extends BaseStructuredData {
  '@type': 'Product';
  name: string;
  description?: string;
  image?: string[];
  brand?: {
    '@type': 'Brand';
    name: string;
  };
  offers?: {
    '@type': 'Offer';
    price?: string;
    priceCurrency?: string;
    availability?: string;
    url?: string;
  };
  aggregateRating?: {
    '@type': 'AggregateRating';
    ratingValue: number;
    reviewCount: number;
  };
}

export interface ServiceSchema extends BaseStructuredData {
  '@type': 'Service';
  name: string;
  description?: string;
  provider: {
    '@type': 'Organization';
    name: string;
  };
  serviceType?: string;
  areaServed?: string;
  hasOfferCatalog?: {
    '@type': 'OfferCatalog';
    name: string;
    itemListElement: any[];
  };
}

export interface WebPageSchema extends BaseStructuredData {
  '@type': 'WebPage';
  name: string;
  description?: string;
  url: string;
  mainEntity?: any;
  breadcrumb?: {
    '@type': 'BreadcrumbList';
    itemListElement: Array<{
      '@type': 'ListItem';
      position: number;
      name: string;
      item: string;
    }>;
  };
}

export interface FAQSchema extends BaseStructuredData {
  '@type': 'FAQPage';
  mainEntity: Array<{
    '@type': 'Question';
    name: string;
    acceptedAnswer: {
      '@type': 'Answer';
      text: string;
    };
  }>;
}

export interface PersonSchema extends BaseStructuredData {
  '@type': 'Person';
  name: string;
  jobTitle?: string;
  worksFor?: {
    '@type': 'Organization';
    name: string;
  };
  email?: string;
  image?: string;
  sameAs?: string[];
}

type StructuredDataType = 
  | OrganizationSchema 
  | ArticleSchema 
  | ProductSchema 
  | ServiceSchema 
  | WebPageSchema 
  | FAQSchema 
  | PersonSchema;

interface StructuredDataProps {
  data: StructuredDataType | StructuredDataType[];
  id?: string;
}

export function StructuredData({ data, id = 'structured-data' }: StructuredDataProps) {
  useEffect(() => {
    // Remove existing structured data script if it exists
    const existingScript = document.getElementById(id);
    if (existingScript) {
      existingScript.remove();
    }

    // Create new structured data script
    const script = document.createElement('script');
    script.id = id;
    script.type = 'application/ld+json';
    
    // Ensure data has proper context
    const processedData = Array.isArray(data) 
      ? data.map(item => ({ '@context': 'https://schema.org', ...item }))
      : { '@context': 'https://schema.org', ...data };
    
    script.textContent = JSON.stringify(processedData, null, 2);
    document.head.appendChild(script);

    // Cleanup function
    return () => {
      const scriptToRemove = document.getElementById(id);
      if (scriptToRemove) {
        scriptToRemove.remove();
      }
    };
  }, [data, id]);

  return null; // This component doesn't render anything
}

// Helper functions to create structured data
export const createOrganizationSchema = (config: Partial<OrganizationSchema>): OrganizationSchema => ({
  '@context': 'https://schema.org',
  '@type': 'Organization',
  name: 'Agent Factory Pro',
  description: 'Enterprise-grade business automation platform',
  url: 'https://agentfactory.pro',
  logo: 'https://agentfactory.pro/logo.png',
  contactPoint: {
    '@type': 'ContactPoint',
    contactType: 'customer service',
    email: '<EMAIL>'
  },
  ...config
});

export const createArticleSchema = (config: Partial<ArticleSchema> & { headline: string; author: string; datePublished: string }): ArticleSchema => ({
  '@context': 'https://schema.org',
  '@type': 'Article',
  headline: config.headline,
  author: {
    '@type': 'Person',
    name: config.author
  },
  publisher: {
    '@type': 'Organization',
    name: 'Agent Factory Pro',
    logo: {
      '@type': 'ImageObject',
      url: 'https://agentfactory.pro/logo.png'
    }
  },
  datePublished: config.datePublished,
  dateModified: config.dateModified || config.datePublished,
  ...config
});

export const createServiceSchema = (config: Partial<ServiceSchema> & { name: string }): ServiceSchema => ({
  '@context': 'https://schema.org',
  '@type': 'Service',
  name: config.name,
  provider: {
    '@type': 'Organization',
    name: 'Agent Factory Pro'
  },
  ...config
});

export const createWebPageSchema = (config: Partial<WebPageSchema> & { name: string; url: string }): WebPageSchema => ({
  '@context': 'https://schema.org',
  '@type': 'WebPage',
  name: config.name,
  url: config.url,
  ...config
});

export const createBreadcrumbSchema = (items: Array<{ name: string; url: string }>): WebPageSchema['breadcrumb'] => ({
  '@type': 'BreadcrumbList',
  itemListElement: items.map((item, index) => ({
    '@type': 'ListItem',
    position: index + 1,
    name: item.name,
    item: item.url
  }))
});

export const createFAQSchema = (faqs: Array<{ question: string; answer: string }>): FAQSchema => ({
  '@context': 'https://schema.org',
  '@type': 'FAQPage',
  mainEntity: faqs.map(faq => ({
    '@type': 'Question',
    name: faq.question,
    acceptedAnswer: {
      '@type': 'Answer',
      text: faq.answer
    }
  }))
});