# Product Requirements Document: Agent Factory Multi-Tenant Migration

## Executive Summary

Migrate the existing Agent Factory platform from Replit DB/session-based authentication to a modern multi-tenant architecture using Supabase for centralized authentication and row-level security (RLS) for data isolation. Enable customer subdomain provisioning with cross-domain user access while maintaining zero downtime and preserving all existing functionality.

## Problem Statement

The current single-tenant architecture limits scalability and customer isolation. The platform needs centralized user management across multiple customer instances while maintaining complete data separation and preserving existing functionality. The current express-session authentication system needs modernization to support multi-tenancy.

## Success Metrics

**Primary:**
- Zero downtime during migration
- All existing functionality preserved
- Successful authentication across multiple domains
- Customer data isolation maintained with RLS
- Single sign-on across all customer subdomains

**Secondary:**
- Reduced authentication complexity
- Improved user management capabilities
- Foundation for automated customer provisioning
- Better performance through single database architecture

## Target Users

**Primary:** Existing Agent Factory users and administrators
**Secondary:** New customers requiring isolated instances
**Tertiary:** Internal administrators managing customer accounts

## Core Requirements

### 1. Centralized Authentication System
- Replace current express-session authentication with Supabase Auth
- Enable single user registration on main site (www.agent-factory.io)
- Support cross-domain login capability using JWT tokens
- Maintain role-based access control per customer instance
- Preserve existing user accounts and data during migration
- Implement single sign-on across all customer subdomains

### 2. Multi-Tenant Architecture with RLS
- Implement row-level security (RLS) in Supabase for data isolation
- Use single database architecture for better performance and cost efficiency
- Support subdomain-based customer access (customer1.agent-factory.app)
- Enable manual customer provisioning process
- Ensure complete customer data isolation through RLS policies

### 3. API Architecture
- Implement single API domain (api.agent-factory.io) with tenant headers
- Use X-Tenant-ID header for tenant identification
- Support both main site and customer subdomains calling same API
- Maintain existing API functionality and response formats
- Implement proper CORS configuration for multiple domains

### 4. Migration System
- Implement zero-downtime data migration strategy
- Preserve existing user accounts and relationships
- Migrate current session data to Supabase
- Ensure data integrity throughout migration process
- Provide rollback capabilities if needed

## Technical Architecture

### Domain Structure

Main Site: www.agent-factory.io (frontend + API)
API Domain: api.agent-factory.io (A record to same hosting)
Customer Sites: customer1.agent-factory.app, customer2.agent-factory.app, etc.


### Authentication Flow
1. User registers/logs in on www.agent-factory.io
2. Supabase issues JWT token with user claims
3. Token works across all customer subdomains
4. Each API request includes X-Tenant-ID header
5. RLS policies filter data based on tenant context

### Database Architecture
- Single Supabase database with RLS enabled
- All tables include tenant_id column
- RLS policies enforce tenant isolation
- Shared user management through Supabase Auth
- Customer-specific data filtered by tenant_id

## Implementation Phases

### Phase 1: Supabase Setup & Configuration
- Set up Supabase project and configure authentication
- Configure database schema with RLS policies
- Set up CORS for multiple domains
- Configure email templates for authentication flows
- Test basic authentication functionality

### Phase 2: Backend Migration
- Replace express-session with Supabase JWT verification
- Update authentication middleware
- Migrate user data to Supabase
- Update API routes to use Supabase auth
- Implement tenant header processing

### Phase 3: Frontend Migration
- Replace auth API calls with Supabase client
- Update React hooks to use Supabase
- Update protected route guards
- Test authentication flows across domains
- Implement tenant header injection

### Phase 4: Multi-Tenant Features
- Implement subdomain routing and tenant identification
- Create customer provisioning system
- Add tenant-aware database connections
- Implement cross-domain authentication
- Test end-to-end multi-tenant functionality

## Technical Requirements

### Backend Requirements
- Express.js server with Supabase integration
- JWT token verification middleware
- Tenant header processing
- RLS policy enforcement
- CORS configuration for multiple domains
- Rate limiting per tenant

### Frontend Requirements
- React application with Supabase client
- Tenant-aware API calls
- Cross-domain authentication support
- Protected route implementation
- Responsive design for all devices

### Database Requirements
- Supabase PostgreSQL with RLS enabled
- Tenant isolation through RLS policies
- User management through Supabase Auth
- Audit logging for all operations
- Backup and recovery procedures

### Infrastructure Requirements
- DNS configuration for API domain
- SSL certificates for all domains
- Monitoring and logging for multi-tenant operations
- Performance monitoring and alerting
- Security and compliance requirements

## Success Criteria

### Technical Success Criteria
- All existing API endpoints continue to function
- Authentication works seamlessly across all domains
- Customer data is properly isolated through RLS
- Zero data loss during migration process
- Performance remains acceptable under multi-tenant load
- JWT tokens work across all customer subdomains

### User Experience Success Criteria
- Existing users can access their accounts after migration
- Login process is intuitive across different domains
- User roles and permissions work correctly
- Application functionality remains unchanged
- Error handling provides clear user feedback
- Single sign-on works across all domains

### Business Success Criteria
- Customer provisioning process is manageable
- Architecture supports future scaling needs
- Security posture is improved or maintained
- Foundation is in place for automation
- Migration costs and risks are minimized

## Constraints and Assumptions

### Constraints
- Must maintain zero downtime during migration
- Existing user data cannot be lost
- Current application functionality must be preserved
- Manual customer provisioning is acceptable initially
- Single database architecture with RLS

### Assumptions
- Supabase can handle the expected user load
- RLS provides sufficient data isolation
- DNS changes can be made manually
- Existing codebase structure can support multi-tenancy
- Users will accept single sign-on across domains

## Dependencies

### External Dependencies
- Supabase service availability and performance
- DNS provider capabilities for subdomain management
- SSL certificate management for multiple domains

### Internal Dependencies
- Existing user data migration scripts
- Database schema updates for RLS
- Frontend authentication flow updates
- API endpoint modifications for tenant support

## Risk Assessment

### High Risk
- Data migration complexity and potential data loss
- Authentication flow changes affecting user experience
- Performance impact of RLS policies

### Medium Risk
- DNS configuration complexity
- CORS setup for multiple domains
- User session migration

### Low Risk
- Frontend component updates
- API response format changes
- Documentation updates

## Milestones

### Supabase Setup

### Backend Migration

### Frontend Migration

### Multi-Tenant Features

## Future Considerations

### Scalability
- Automated customer provisioning
- Advanced monitoring and analytics
- Performance optimization for large tenant counts

### Security
- Advanced authentication features (MFA, SSO)
- Enhanced audit logging
- Security compliance certifications

### Features
- Cross-tenant analytics (with proper isolation)
- Advanced user management features
- Integration with external systems