# Task ID: 10
# Title: Update Frontend API Calls
# Status: done
# Dependencies: 9
# Priority: high
# Description: Modify frontend API calls to include authentication tokens and tenant headers
# Details:
1. Create API client with authentication token injection
2. Add tenant header to all API requests
3. Update all API call locations in the codebase
4. Handle authentication errors and token refresh
5. Implement automatic tenant detection from subdomain
6. Example API client implementation:
```javascript
import { useAuth } from './auth-context';
import { useTenant } from './tenant-context';

export function useApiClient() {
  const { user } = useAuth();
  const { currentTenant } = useTenant();

  const apiCall = async (endpoint, options = {}) => {
    if (!user) throw new Error('Authentication required');
    if (!currentTenant) throw new Error('Tenant context required');

    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${user.access_token}`,
      'X-Tenant-ID': currentTenant.id,
      ...options.headers,
    };

    const response = await fetch(`https://api.agent-factory.io${endpoint}`, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'API request failed');
    }

    return response.json();
  };

  return { apiCall };
}
```

# Test Strategy:
Test API calls with different authentication states. Verify tenant headers are correctly included. Test error handling for authentication failures and API errors.
