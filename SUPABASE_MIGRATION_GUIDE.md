# Supabase Migration Guide for Agent Factory Pro

## 🚀 NEXT STEPS TO COMPLETE MIGRATION

### ✅ What's Already Done
- Frontend auth system migrated to Supabase
- Backend API updated with JWT authentication  
- Database schema designed with multi-tenant support
- All code changes completed

### 🎯 STEPS TO FINISH MIGRATION

### Step 1: Add Supabase Environment Variables

In your Replit Secrets, add these keys:

```
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here  
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### Step 2: Run Database Setup in Supabase

1. Go to your Supabase project dashboard
2. Navigate to **SQL Editor**  
3. Copy and paste the contents of `scripts/supabase_schema.sql`
4. Click **Run** to execute the script

### Step 3: Create Your Super Admin Account

Run this command in your terminal:

```bash
npx tsx scripts/create-super-admin.ts
```

### Step 4: Test the Migration

1. **Test Registration**: Try registering a new user
2. **Test Login**: Try logging in with the new user
3. **Test Protected Routes**: Access admin pages
4. **Test API**: Make sure API calls work with JWT tokens

### Step 5: Deploy and Go Live

1. **Deploy to production** (Replit should auto-deploy)
2. **Test in production** with real Supabase project
3. **Monitor for any auth issues**

## 🚨 IF SOMETHING BREAKS

**Rollback plan**: The old session code is commented out in `server/routes.ts`, not deleted. If needed, you can quickly revert by uncommenting the old code and commenting out the new Supabase code.

## ✅ YOU'RE DONE!

After these steps, you'll have:
- ✅ Supabase authentication working
- ✅ Multi-tenant database with configurable plans  
- ✅ User limits automatically enforced
- ✅ Complete role-based access control
- ✅ Ready for customer subdomains

---

## 📋 Quick Reference

**Environment Variables Needed:**
```
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

**Key Files:**
- `scripts/supabase_schema.sql` - Run this in Supabase SQL Editor
- `scripts/create-super-admin.ts` - Run this to create your admin account

**What You Get:**
- Multi-tenant SaaS with configurable user limits
- 7 user roles (lead, customer, support, billing_admin, sales, blog_admin, super_admin)
- 4 default plans (trial, starter, professional, enterprise)
- Automatic user limit enforcement
- Ready for customer subdomains 