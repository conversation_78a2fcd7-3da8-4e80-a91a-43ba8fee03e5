<!DOCTYPE html>
<html lang="en" class="dark">
  <head>
    <script type="module">
import { createHotContext } from "/@vite/client";
const hot = createHotContext("/__dummy__runtime-error-plugin");

function sendError(error) {
  if (!(error instanceof Error)) {
    error = new Error("(unknown runtime error)");
  }
  const serialized = {
    message: error.message,
    stack: error.stack,
  };
  hot.send("runtime-error-plugin:error", serialized);
}

window.addEventListener("error", (evt) => {
  sendError(evt.error);
});

window.addEventListener("unhandledrejection", (evt) => {
  sendError(evt.reason);
});
</script>

    <script type="module">
import RefreshRuntime from "/@react-refresh"
RefreshRuntime.injectIntoGlobalHook(window)
window.$RefreshReg$ = () => {}
window.$RefreshSig$ = () => (type) => type
window.__vite_plugin_react_preamble_installed__ = true
</script>

    <script type="module" src="/@vite/client"></script>

    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1" />
    
    <!-- Enhanced SEO and Performance -->
    <title>Agent Factory Pro - Enterprise Business Automation Platform</title>
    <meta name="description" content="Transform your business with our comprehensive automation platform. Streamline workflows, enhance productivity, and scale operations with enterprise-grade tools designed for modern businesses." />
    <meta name="keywords" content="business automation, enterprise software, workflow management, productivity tools, business process automation, digital transformation, SaaS platform" />
    <meta name="robots" content="index, follow" />
    <meta name="theme-color" content="#1a1b3e" />
    <link rel="canonical" href="https://agentfactory.pro/" />
    
    <!-- Performance Optimization -->
    <link rel="dns-prefetch" href="https://fonts.googleapis.com" />
    <link rel="dns-prefetch" href="https://fonts.gstatic.com" />
    <link rel="dns-prefetch" href="https://res.cloudinary.com" />
    
    <!-- Enhanced Open Graph -->
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://agentfactory.pro/" />
    <meta property="og:title" content="Agent Factory Pro - Enterprise Business Automation Platform" />
    <meta property="og:description" content="Transform your business with our comprehensive automation platform. Enterprise-grade functionality with user-friendly design." />
    <meta property="og:image" content="https://www.agent-factory.co/og-image.png" />
    <meta property="og:site_name" content="Agent Factory Pro" />
    
    <!-- Twitter Cards -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta name="twitter:title" content="Agent Factory Pro - Enterprise Business Automation Platform" />
    <meta name="twitter:description" content="Transform your business with our comprehensive automation platform." />
    <meta name="twitter:image" content="https://www.agent-factory.co/og-image.png" />
    
    <!-- Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "Agent Factory Pro",
      "description": "Enterprise-grade business automation platform",
      "url": "https://agentfactory.pro",
      "logo": "https://www.agent-factory.co/logo.png",
      "contactPoint": {
        "@type": "ContactPoint",
        "contactType": "customer service",
        "email": "<EMAIL>"
      }
    }
    </script>
    
    <!-- Favicon and Icons -->
    <link rel="icon" type="image/svg+xml" href="/favicon.svg" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="/site.webmanifest" />
    
    <!-- Optimized Fonts with font-display -->
    <link rel="preconnect" href="https://fonts.googleapis.com" />
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet" />
    
    <!-- Critical CSS Inlined for Performance -->
    <style>
      /* Critical above-the-fold styles */
      body { font-family: 'Inter', sans-serif; margin: 0; padding: 0; }
      .gradient-bg { background: linear-gradient(135deg, #1a1b3e 0%, #2d1b69 50%, #1a1b3e 100%); }
      .loading-spinner { width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #3498db; border-radius: 50%; animation: spin 1s linear infinite; }
      @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
    </style>
  </head>
  <body style="font-family: 'Inter', sans-serif;">
    <div id="root"></div>
    <script type="module" src="/src/main.tsx?v=LPkNV7PJ10d2-MJDU9_H6"></script>
    <!-- This is a replit script which adds a banner on the top of the page when opened in development mode outside the replit environment -->
    <script type="text/javascript" src="https://replit.com/public/js/replit-dev-banner.js"></script>
  </body>
</html>
