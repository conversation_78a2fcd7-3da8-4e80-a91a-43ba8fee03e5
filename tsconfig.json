{"include": ["client/src/vite-env.d.ts", "shared"], "exclude": ["node_modules", "build", "dist", "**/*.test.ts"], "compilerOptions": {"incremental": true, "tsBuildInfoFile": "./node_modules/typescript/tsbuildinfo", "noEmit": true, "module": "ESNext", "strict": true, "lib": ["ES2020", "DOM", "DOM.Iterable"], "jsx": "preserve", "skipLibCheck": true, "allowImportingTsExtensions": true, "moduleResolution": "bundler", "baseUrl": ".", "types": ["node", "vite/client", "jest"], "paths": {"@shared/*": ["./shared/*"], "@server/*": ["./server/*"], "@client/*": ["./client/src/*"]}, "target": "ES2020", "useDefineForClassFields": true}, "references": [{"path": "./tsconfig.node.json"}]}