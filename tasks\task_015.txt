# Task ID: 15
# Title: Implement API Rate Limiting per Tenant
# Status: pending
# Dependencies: 8, 13
# Priority: low
# Description: Add rate limiting to API endpoints based on tenant plans and usage
# Details:
1. Design rate limiting strategy based on tenant plans
2. Implement rate limiting middleware
3. Create rate limit storage in Redis or similar
4. Add tenant plan information to database
5. Implement rate limit headers in responses
6. Create monitoring for rate limit usage
7. Example implementation using Express Rate Limit:
```javascript
const rateLimit = require('express-rate-limit');
const RedisStore = require('rate-limit-redis');
const redis = require('redis');

const redisClient = redis.createClient(process.env.REDIS_URL);

// Create tenant-aware rate limiter
const tenantRateLimit = (options = {}) => {
  return async (req, res, next) => {
    if (!req.tenantId) {
      return next();
    }
    
    // Get tenant plan limits
    const { data, error } = await supabase
      .from('tenants')
      .select('plan')
      .eq('id', req.tenantId)
      .single();
    
    if (error) {
      return next(error);
    }
    
    // Set rate limits based on plan
    let rateOptions = {};
    switch (data.plan) {
      case 'free':
        rateOptions = { max: 100, windowMs: 60 * 1000 }; // 100 requests per minute
        break;
      case 'pro':
        rateOptions = { max: 1000, windowMs: 60 * 1000 }; // 1000 requests per minute
        break;
      case 'enterprise':
        rateOptions = { max: 10000, windowMs: 60 * 1000 }; // 10000 requests per minute
        break;
      default:
        rateOptions = { max: 50, windowMs: 60 * 1000 }; // Default fallback
    }
    
    // Create and apply rate limiter
    const limiter = rateLimit({
      ...rateOptions,
      ...options,
      store: new RedisStore({
        client: redisClient,
        prefix: `rate-limit:${req.tenantId}:`
      }),
      keyGenerator: (req) => req.tenantId,
      standardHeaders: true,
      legacyHeaders: false,
    });
    
    return limiter(req, res, next);
  };
};

// Apply to routes
app.use('/api/', tenantRateLimit());
```

# Test Strategy:
Test rate limiting with different tenant plans. Verify rate limit headers are correctly included in responses. Test rate limit exceeded scenarios and error handling.
