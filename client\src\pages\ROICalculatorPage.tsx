import React, { useState, useEffect } from "react";
import { <PERSON>, CardHeader, CardT<PERSON>le, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON><PERSON> } from "@/components/ui/slider";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calculator, TrendingUp, Users, Clock, DollarSign, Zap, AlertTriangle, Info, Save, Download, UserPlus, BarChart3, <PERSON><PERSON><PERSON> } from "lucide-react";
import { <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON>hart, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, CartesianGrid, Legend, Pie } from "recharts";
import { useToast } from "@/hooks/use-toast";
import { Link } from "wouter";
import { Header } from "@/components/layout/Header";

// Import types from the calculation engine
type ROIInput = {
  employees: number;
  hoursPerWeek: number;
  hourlyCost: number;
  automationPercentage: number;
  monthlyVolume: number;
  errorRate: number;
  costPerError?: number;
  implementationCost?: number;
};

type ROIResult = {
  monthlyHoursSaved: number;
  monthlyCostSavings: number;
  annualSavings: number;
  paybackPeriod: number | null;
  additionalCapacity: number;
  errorReductionSavings: number | null;
};

// Industry benchmark defaults
const INDUSTRY_DEFAULTS = {
  manufacturing: { employees: 8, hoursPerWeek: 15, hourlyCost: 45, errorRate: 0.08, monthlyVolume: 800 },
  retail: { employees: 5, hoursPerWeek: 12, hourlyCost: 35, errorRate: 0.05, monthlyVolume: 1200 },
  healthcare: { employees: 6, hoursPerWeek: 20, hourlyCost: 55, errorRate: 0.03, monthlyVolume: 400 },
  finance: { employees: 4, hoursPerWeek: 18, hourlyCost: 75, errorRate: 0.02, monthlyVolume: 600 },
};

type SavedScenario = {
  name: string;
  input: ROIInput;
  results: ROIResult;
  timestamp: string;
};

export default function ROICalculatorPage() {
  const { toast } = useToast();
  const [isCalculating, setIsCalculating] = useState(false);
  const [results, setResults] = useState<ROIResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [savedScenarios, setSavedScenarios] = useState<SavedScenario[]>([]);
  const [selectedIndustry, setSelectedIndustry] = useState<keyof typeof INDUSTRY_DEFAULTS | null>(null);
  const [showConversionModal, setShowConversionModal] = useState(false);
  const [currentScenarioName, setCurrentScenarioName] = useState("");
  
  const [input, setInput] = useState<ROIInput>({
    employees: 5,
    hoursPerWeek: 10,
    hourlyCost: 50,
    automationPercentage: 0.7,
    monthlyVolume: 500,
    errorRate: 0.05,
    costPerError: 100,
    implementationCost: 5000,
  });

  // Real-time calculation as user changes inputs
  useEffect(() => {
    const calculateROI = async () => {
      setIsCalculating(true);
      setError(null);
      
      try {
        // Ensure all required fields are present according to API specs
        const requestData = {
          ...input,
          costPerError: input.costPerError || 0, // Must be present, can be 0
        };
        
        const response = await fetch('/api/calculate-automation-roi', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestData),
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => ({ message: 'Calculation failed' }));
          throw new Error(errorData.message || 'Calculation failed');
        }

        const result = await response.json();
        setResults(result);
      } catch (err) {
        const errorMessage = err instanceof Error ? err.message : 'Failed to calculate ROI. Please check your inputs and try again.';
        setError(errorMessage);
        setResults(null);
      } finally {
        setIsCalculating(false);
      }
    };

    // Debounce calculations to avoid excessive API calls
    const timeoutId = setTimeout(calculateROI, 300);
    return () => clearTimeout(timeoutId);
  }, [input]);

  const updateInput = (field: keyof ROIInput, value: number) => {
    // Validate inputs according to API specifications
    let validatedValue = value;
    
    switch (field) {
      case 'employees':
        validatedValue = Math.max(1, Math.min(1000, value));
        break;
      case 'hoursPerWeek':
        validatedValue = Math.max(1, Math.min(40, value));
        break;
      case 'hourlyCost':
        validatedValue = Math.max(10, Math.min(200, value));
        break;
      case 'automationPercentage':
        validatedValue = Math.max(0, Math.min(1, value));
        break;
      case 'monthlyVolume':
        validatedValue = Math.max(1, value);
        break;
      case 'errorRate':
        validatedValue = Math.max(0, Math.min(1, value));
        break;
      case 'costPerError':
        validatedValue = Math.max(0, Math.min(100000, value));
        break;
      case 'implementationCost':
        validatedValue = Math.max(0, value);
        break;
    }
    
    setInput(prev => ({ ...prev, [field]: validatedValue }));
  };

  const loadIndustryDefaults = (industry: keyof typeof INDUSTRY_DEFAULTS) => {
    const defaults = INDUSTRY_DEFAULTS[industry];
    setInput(prev => ({
      ...prev,
      ...defaults,
      automationPercentage: 0.7,
      costPerError: 100,
      implementationCost: 15000,
    }));
    setSelectedIndustry(industry);
  };

  const saveScenario = () => {
    if (!results || !currentScenarioName.trim()) {
      toast({
        title: "Cannot Save Scenario",
        description: "Please enter a scenario name and ensure calculations are complete.",
        variant: "destructive",
      });
      return;
    }

    const scenario: SavedScenario = {
      name: currentScenarioName,
      input: { ...input },
      results: { ...results },
      timestamp: new Date().toISOString(),
    };

    setSavedScenarios(prev => [...prev, scenario]);
    setCurrentScenarioName("");
    toast({
      title: "Scenario Saved",
      description: `"${scenario.name}" has been saved for comparison.`,
    });
  };

  const downloadReport = () => {
    if (!results) return;
    
    const reportData = {
      scenario: currentScenarioName || "Current Scenario",
      timestamp: new Date().toISOString(),
      inputs: input,
      results: results,
      insights: {
        roiPercent: ((results.annualSavings / (input.implementationCost || 1)) * 100).toFixed(1),
        monthlyCapacityGain: `${Math.round(results.additionalCapacity)}%`,
        breakEvenMonths: results.paybackPeriod ? Math.round(results.paybackPeriod) : 'N/A'
      }
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `automation-roi-report-${Date.now()}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  // Trigger conversion modal after extended engagement or bottom interaction
  useEffect(() => {
    if (results && results.annualSavings > 50000) {
      const timer = setTimeout(() => setShowConversionModal(true), 30000); // Increased to 30 seconds
      return () => clearTimeout(timer);
    }
  }, [results]);

  // Show modal when user interacts with save/export features (bottom engagement)
  const handleSaveScenario = () => {
    saveScenario();
    if (results && results.annualSavings > 50000 && !showConversionModal) {
      setTimeout(() => setShowConversionModal(true), 2000);
    }
  };

  const handleDownloadReport = () => {
    downloadReport();
    if (results && results.annualSavings > 50000 && !showConversionModal) {
      setTimeout(() => setShowConversionModal(true), 2000);
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatPercentage = (decimal: number) => {
    return `${Math.round(decimal * 100)}%`;
  };

  // Pie chart data for cost breakdown
  const getPieChartData = () => {
    if (!results) return [];
    
    const manualLaborCost = input.employees * input.hoursPerWeek * 4.33 * input.hourlyCost;
    const automationSavings = results.monthlyCostSavings;
    const errorCosts = results.errorReductionSavings || 0;
    const remainingManualCost = manualLaborCost - automationSavings;

    return [
      { name: 'Automated Savings', value: automationSavings, color: '#10B981' },
      { name: 'Remaining Manual Cost', value: remainingManualCost, color: '#F59E0B' },
      { name: 'Error Reduction Value', value: errorCosts, color: '#6366F1' },
    ].filter(item => item.value > 0);
  };

  const ResultCard = ({ icon: Icon, title, value, subtitle, tooltip }: {
    icon: React.ElementType;
    title: string;
    value: string;
    subtitle?: string;
    tooltip?: string;
  }) => (
    <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950 dark:to-indigo-950">
      <CardContent className="p-4">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900 rounded-lg">
            <Icon className="h-5 w-5 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="flex-1">
            <div className="flex items-center gap-2">
              <p className="text-sm font-medium text-gray-600 dark:text-gray-400">{title}</p>
              {tooltip && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger>
                      <Info className="h-3 w-3 text-gray-400" />
                    </TooltipTrigger>
                    <TooltipContent>
                      <p className="max-w-xs text-sm">{tooltip}</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              )}
            </div>
            <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
            {subtitle && <p className="text-xs text-gray-500 dark:text-gray-500">{subtitle}</p>}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  const ConversionModal = () => (
    <Dialog open={showConversionModal} onOpenChange={setShowConversionModal}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5 text-green-600" />
            Impressive Results!
          </DialogTitle>
          <DialogDescription>
            Your automation could save over {formatCurrency(results?.annualSavings || 0)} annually.
            Create an account to save these results and get a personalized automation roadmap.
          </DialogDescription>
        </DialogHeader>
        <div className="flex flex-col gap-3 pt-4">
          <Button asChild className="w-full">
            <Link href="/register">
              <UserPlus className="h-4 w-4 mr-2" />
              Create Free Account
            </Link>
          </Button>
          <Button variant="outline" asChild className="w-full">
            <Link href="/login">Already have an account? Sign in</Link>
          </Button>
          <Button 
            variant="ghost" 
            onClick={() => setShowConversionModal(false)}
            className="text-sm"
          >
            Continue without account
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );

  return (
    <div className="min-h-screen">
      <Header />
      <div className="max-w-7xl mx-auto px-6 pt-2 pb-8">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Calculator className="h-12 w-12 text-blue-600 dark:text-blue-400 mr-3" />
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
              Automation ROI Calculator
            </h1>
          </div>
          <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
            Calculate the financial impact of business process automation on your organization
          </p>
        </div>

        <div className="grid xl:grid-cols-3 lg:grid-cols-2 gap-8">
          {/* Input Panel */}
          <Card className="bg-white dark:bg-gray-800 shadow-xl">
            <CardHeader>
              <CardTitle className="space-y-3">
                <div className="flex items-center">
                  <Users className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
                  Current Process Details
                </div>
                <div className="flex gap-2 justify-start">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleDownloadReport}
                    disabled={!results}
                  >
                    <Download className="h-4 w-4 mr-1" />
                    Export
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSaveScenario}
                    disabled={!results}
                  >
                    <Save className="h-4 w-4 mr-1" />
                    Save
                  </Button>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Industry Quick Setup */}
              <div className="space-y-3">
                <Label className="text-base font-semibold">Industry Quick Setup</Label>
                <div className="grid grid-cols-2 gap-2">
                  {Object.entries(INDUSTRY_DEFAULTS).map(([industry, _]) => (
                    <Button
                      key={industry}
                      variant={selectedIndustry === industry ? "default" : "outline"}
                      size="sm"
                      onClick={() => loadIndustryDefaults(industry as keyof typeof INDUSTRY_DEFAULTS)}
                      className="capitalize"
                    >
                      {industry}
                    </Button>
                  ))}
                </div>
              </div>

              <Separator />
              {/* Employees */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label htmlFor="employees">Number of Employees</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-3 w-3 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs text-sm">Count employees who spend time on repetitive, rule-based tasks that could be automated</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Input
                  id="employees"
                  type="number"
                  value={input.employees}
                  onChange={(e) => updateInput('employees', Number(e.target.value))}
                  min="1"
                  max="1000"
                  className="transition-colors"
                />
                <p className="text-sm text-gray-500">Employees working on repetitive tasks</p>
              </div>

              {/* Hours per Week */}
              <div className="space-y-2">
                <div className="flex items-center gap-2">
                  <Label>Hours per Week per Employee: {input.hoursPerWeek}</Label>
                  <TooltipProvider>
                    <Tooltip>
                      <TooltipTrigger>
                        <Info className="h-3 w-3 text-gray-400" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p className="max-w-xs text-sm">Average weekly hours each employee spends on tasks that could be automated</p>
                      </TooltipContent>
                    </Tooltip>
                  </TooltipProvider>
                </div>
                <Slider
                  value={[input.hoursPerWeek]}
                  onValueChange={([value]) => updateInput('hoursPerWeek', value)}
                  max={40}
                  min={1}
                  step={1}
                  className="w-full"
                />
                <p className="text-sm text-gray-500">Time spent on automatable tasks</p>
              </div>

              {/* Hourly Cost */}
              <div className="space-y-2">
                <Label htmlFor="hourlyCost">Hourly Cost (including benefits)</Label>
                <Input
                  id="hourlyCost"
                  type="number"
                  value={input.hourlyCost}
                  onChange={(e) => updateInput('hourlyCost', Number(e.target.value))}
                  min="10"
                  max="200"
                />
                <p className="text-sm text-gray-500">Total cost per employee hour</p>
              </div>

              {/* Automation Percentage */}
              <div className="space-y-2">
                <Label>Automation Coverage: {formatPercentage(input.automationPercentage)}</Label>
                <Slider
                  value={[input.automationPercentage * 100]}
                  onValueChange={([value]) => updateInput('automationPercentage', value / 100)}
                  max={100}
                  min={10}
                  step={5}
                  className="w-full"
                />
                <p className="text-sm text-gray-500">Percentage of process to automate</p>
              </div>

              <Separator />

              {/* Monthly Volume */}
              <div className="space-y-2">
                <Label htmlFor="monthlyVolume">Monthly Processing Volume</Label>
                <Input
                  id="monthlyVolume"
                  type="number"
                  value={input.monthlyVolume}
                  onChange={(e) => updateInput('monthlyVolume', Number(e.target.value))}
                  min="1"
                />
                <p className="text-sm text-gray-500">Orders, leads, transactions per month</p>
              </div>

              {/* Error Rate */}
              <div className="space-y-2">
                <Label>Current Error Rate: {formatPercentage(input.errorRate)}</Label>
                <Slider
                  value={[input.errorRate * 100]}
                  onValueChange={([value]) => updateInput('errorRate', value / 100)}
                  max={20}
                  min={0}
                  step={0.5}
                  className="w-full"
                />
                <p className="text-sm text-gray-500">Percentage of tasks with errors</p>
              </div>

              {/* Optional Fields */}
              <Separator />
              
              <div className="space-y-4">
                <Label className="text-base font-semibold">Optional Parameters</Label>
                
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Label htmlFor="costPerError">Cost per Error</Label>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <Info className="h-3 w-3 text-gray-400" />
                        </TooltipTrigger>
                        <TooltipContent>
                          <p className="max-w-xs text-sm">Enter the estimated cost (in dollars) to fix a single error. Typical range: $0–$100,000. Enter 0 if errors have no direct cost.</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </div>
                  <Input
                    id="costPerError"
                    type="number"
                    value={input.costPerError || ''}
                    onChange={(e) => updateInput('costPerError', Number(e.target.value) || 0)}
                    placeholder="Enter cost per error"
                    min="0"
                    max="100000"
                  />
                  <p className="text-sm text-gray-500">Cost to fix a single error ($0-$100,000)</p>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="implementationCost">Implementation Cost</Label>
                  <Input
                    id="implementationCost"
                    type="number"
                    value={input.implementationCost || ''}
                    onChange={(e) => updateInput('implementationCost', Number(e.target.value) || 0)}
                    placeholder="Enter automation setup cost"
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Results Panel */}
          <div className="xl:col-span-2 space-y-6">
            {error && (
              <Card className="bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800">
                <CardContent className="p-4">
                  <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
                    <AlertTriangle className="h-5 w-5" />
                    <span className="font-semibold">Calculation Error</span>
                  </div>
                  <p className="text-red-700 dark:text-red-300 mt-1">{error}</p>
                </CardContent>
              </Card>
            )}

            {isCalculating && (
              <Card>
                <CardContent className="p-8 text-center">
                  <div className="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full mb-4" />
                  <p className="text-gray-600 dark:text-gray-400">Calculating ROI...</p>
                </CardContent>
              </Card>
            )}

            {results && !isCalculating && (
              <>
                <Card className="bg-gradient-to-r from-green-500 to-blue-600 text-white">
                  <CardContent className="p-6">
                    <div className="text-center">
                      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-90" />
                      <h3 className="text-2xl font-bold mb-2">Annual Savings</h3>
                      <p className="text-4xl font-bold">{formatCurrency(results.annualSavings)}</p>
                      <p className="text-lg opacity-90 mt-2">
                        {formatCurrency(results.monthlyCostSavings)} per month
                      </p>
                    </div>
                  </CardContent>
                </Card>

                <div className="grid md:grid-cols-2 gap-4">
                  <ResultCard
                    icon={Clock}
                    title="Hours Saved Monthly"
                    value={Math.round(results.monthlyHoursSaved).toLocaleString()}
                    subtitle="Freed up for higher-value work"
                    tooltip="Time your team can redirect to strategic initiatives and growth activities"
                  />
                  
                  <ResultCard
                    icon={Zap}
                    title="Additional Capacity"
                    value={`${Math.round(results.additionalCapacity)}%`}
                    subtitle="Process capacity increase"
                    tooltip="Percentage increase in your team's ability to handle more work volume"
                  />
                </div>

                {/* Cost Breakdown Pie Chart */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <PieChart className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
                      Monthly Cost Breakdown
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid lg:grid-cols-2 gap-6">
                      <div className="h-64">
                        <ResponsiveContainer width="100%" height="100%">
                          <RechartsPieChart>
                            <Pie
                              data={getPieChartData()}
                              cx="50%"
                              cy="50%"
                              innerRadius={60}
                              outerRadius={100}
                              paddingAngle={5}
                              dataKey="value"
                            >
                              {getPieChartData().map((entry, index) => (
                                <Cell key={`cell-${index}`} fill={entry.color} />
                              ))}
                            </Pie>
                          </RechartsPieChart>
                        </ResponsiveContainer>
                      </div>
                      <div className="space-y-3">
                        {getPieChartData().map((item, index) => (
                          <div key={index} className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                            <div className="flex items-center gap-2">
                              <div 
                                className="w-3 h-3 rounded-full" 
                                style={{ backgroundColor: item.color }}
                              />
                              <span className="text-sm font-medium">{item.name}</span>
                            </div>
                            <span className="font-bold">{formatCurrency(item.value)}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {results.paybackPeriod !== null && results.paybackPeriod !== undefined && (
                  <ResultCard
                    icon={DollarSign}
                    title="Payback Period"
                    value={`${Math.round(results.paybackPeriod)} months`}
                    subtitle="Time to recover implementation cost"
                  />
                )}

                {results.errorReductionSavings && (
                  <ResultCard
                    icon={AlertTriangle}
                    title="Error Reduction Savings"
                    value={formatCurrency(results.errorReductionSavings)}
                    subtitle="Monthly savings from fewer errors"
                  />
                )}

                {/* Scenario Saving Input */}
                <Card className="bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-950 dark:to-pink-950">
                  <CardContent className="p-6">
                    <div className="space-y-4">
                      <div className="flex items-center gap-2">
                        <Save className="h-5 w-5 text-purple-600 dark:text-purple-400" />
                        <h3 className="text-lg font-semibold">Save This Scenario</h3>
                      </div>
                      <div className="flex gap-2">
                        <Input
                          placeholder="Enter scenario name (e.g., Current State, 50% Automation)"
                          value={currentScenarioName}
                          onChange={(e) => setCurrentScenarioName(e.target.value)}
                          className="flex-1"
                        />
                        <Button onClick={handleSaveScenario} disabled={!currentScenarioName.trim()}>
                          Save Scenario
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* What-if Scenarios */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
                      What-If Scenarios
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-3 gap-4 text-center">
                      <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">Conservative (50%)</p>
                        <p className="font-bold text-lg">
                          {formatCurrency((results.annualSavings / input.automationPercentage) * 0.5)}
                        </p>
                        <p className="text-xs text-gray-500 mt-1">Annual Savings</p>
                      </div>
                      <div className="p-4 bg-blue-50 dark:bg-blue-900 rounded-lg border-2 border-blue-200 dark:border-blue-700">
                        <p className="text-sm text-blue-600 dark:text-blue-400 mb-2">Current Plan</p>
                        <p className="font-bold text-lg text-blue-700 dark:text-blue-300">
                          {formatCurrency(results.annualSavings)}
                        </p>
                        <p className="text-xs text-blue-500 mt-1">{formatPercentage(input.automationPercentage)} Automation</p>
                      </div>
                      <div className="p-4 bg-green-50 dark:bg-green-900 rounded-lg">
                        <p className="text-sm text-green-600 dark:text-green-400 mb-2">Full Automation</p>
                        <p className="font-bold text-lg text-green-700 dark:text-green-300">
                          {formatCurrency((results.annualSavings / input.automationPercentage) * 1.0)}
                        </p>
                        <p className="text-xs text-green-500 mt-1">Maximum Potential</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* Lead Magnet CTA */}
                <Card className="bg-gradient-to-r from-green-500 to-blue-600 text-white">
                  <CardContent className="p-6 text-center">
                    <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-90" />
                    <h3 className="text-2xl font-bold mb-2">Ready to Implement Automation?</h3>
                    <p className="text-lg mb-6 opacity-90">
                      Create a free account to save your results and get a personalized automation roadmap
                    </p>
                    <div className="flex gap-3 justify-center">
                      <Button asChild size="lg" className="bg-white text-blue-600 hover:bg-gray-100">
                        <Link href="/register">
                          <UserPlus className="h-4 w-4 mr-2" />
                          Get Started Free
                        </Link>
                      </Button>
                      <Button asChild variant="outline" size="lg" className="border-white text-white hover:bg-white hover:text-blue-600">
                        <Link href="/booking">Schedule Consultation</Link>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </>
            )}
          </div>
        </div>

        {/* Conversion Modal */}
        <ConversionModal />
      </div>
    </div>
  );
}