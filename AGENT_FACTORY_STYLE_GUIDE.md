# Agent Factory Design System & Style Guide

## Overview
This style guide defines the visual identity, component patterns, and design principles for Agent Factory applications. Use this guide to maintain consistency across all websites and applications in the Agent Factory ecosystem.

## Brand Identity

### Primary Colors
```css
/* Core Brand Colors */
--primary-dark: 210 70% 8%;        /* Deep navy blue - main backgrounds */
--secondary-dark: 220 26% 18%;     /* Secondary backgrounds, cards */
--accent-cyan: 194 100% 50%;       /* Primary accent - neon effects */
--accent-blue: 199 89% 48%;        /* Secondary accent */
--text-light: 210 20% 89%;         /* Primary text on dark backgrounds */
--neon-glow: 194 100% 50%;         /* Neon glow effects */

/* Standard UI Colors */
--background: 240 10% 3.9%;        /* Base background */
--foreground: 0 0% 98%;            /* Primary text */
--muted: 240 3.7% 15.9%;           /* Muted backgrounds */
--border: 240 3.7% 15.9%;          /* Border color */
--primary: 207 90% 54%;            /* Primary buttons */
```

### Typography
- **Primary Font**: System default (Tailwind's font-sans stack)
- **Headings**: Bold weights (font-bold, font-semibold)
- **Body Text**: Regular weight with good contrast
- **Size Scale**: Follow <PERSON><PERSON>wind's text scale (text-sm, text-base, text-lg, etc.)

## Visual Elements

### Background System
```css
/* Primary background with image overlay */
body {
  background-image: url('/AgentFactoryBG.png');
  background-size: cover;
  background-position: center;
  background-attachment: fixed;
  background-repeat: no-repeat;
  background-color: #1e293b; /* Fallback */
}

/* Dark overlay for readability */
body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 23, 42, 0.4);
  backdrop-filter: blur(1px);
  z-index: -1;
  pointer-events: none;
}
```

### Neon Effects & Glow
```css
/* Neon border effect */
.neon-border {
  border: 1px solid rgba(6, 182, 212, 0.3);
  box-shadow: 
    0 0 10px rgba(6, 182, 212, 0.2),
    inset 0 0 10px rgba(6, 182, 212, 0.1);
}

/* Glow animation */
.animate-glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from { box-shadow: 0 0 10px rgba(6, 182, 212, 0.2); }
  to { box-shadow: 0 0 20px rgba(6, 182, 212, 0.4); }
}

/* Neon text glow */
.neon-glow {
  text-shadow: 0 0 10px rgba(6, 182, 212, 0.6);
}
```

## Component Patterns

### Header/Navigation
```jsx
// Header Structure
<header className="relative z-50">
  <div className="max-w-7xl mx-auto px-6 mt-6">
    <div className="neon-border animate-glow bg-[hsl(var(--secondary-dark))] bg-opacity-30 backdrop-blur-md rounded-2xl px-8 py-4">
      <nav className="flex items-center justify-between">
        {/* Logo */}
        <Link href="/" className="neon-glow">
          <Logo size="md" />
        </Link>
        
        {/* Navigation Items */}
        <div className="hidden md:flex items-center space-x-8">
          {/* Nav links */}
        </div>
        
        {/* User Menu */}
        <div className="flex items-center space-x-4">
          {/* User actions */}
        </div>
      </nav>
    </div>
  </div>
</header>
```

### Cards & Containers
```jsx
// Primary Card Pattern
<Card className="bg-white dark:bg-gray-800 shadow-xl">
  <CardHeader>
    <CardTitle className="flex items-center">
      <Icon className="h-5 w-5 mr-2 text-blue-600 dark:text-blue-400" />
      Card Title
    </CardTitle>
  </CardHeader>
  <CardContent>
    {/* Content */}
  </CardContent>
</Card>

// Gradient Feature Card
<Card className="bg-gradient-to-r from-green-500 to-blue-600 text-white">
  <CardContent className="p-6">
    {/* Content */}
  </CardContent>
</Card>
```

### Buttons
```jsx
// Primary Action Button
<Button className="bg-blue-600 hover:bg-blue-700 text-white">
  Primary Action
</Button>

// Secondary Button
<Button variant="outline" className="border-blue-600 text-blue-600 hover:bg-blue-50">
  Secondary Action
</Button>

// Gradient Call-to-Action
<Button className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700">
  Get Started
</Button>
```

### Form Elements
```jsx
// Input Field Pattern
<div className="space-y-2">
  <Label htmlFor="field">Field Label</Label>
  <Input
    id="field"
    type="text"
    placeholder="Enter value"
    className="bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-600"
  />
  <p className="text-sm text-gray-500">Helper text</p>
</div>

// Slider with Value Display
<div className="space-y-2">
  <Label>Setting: {formatValue(value)}</Label>
  <Slider
    value={[value]}
    onValueChange={([newValue]) => setValue(newValue)}
    max={100}
    min={0}
    step={1}
    className="w-full"
  />
</div>
```

## Layout Patterns

### Page Structure
```jsx
// Standard Page Layout
<div className="min-h-screen">
  <Header />
  <div className="max-w-7xl mx-auto px-6 pt-2 pb-8">
    {/* Page Header */}
    <div className="text-center mb-8">
      <div className="flex items-center justify-center mb-4">
        <Icon className="h-12 w-12 text-blue-600 dark:text-blue-400 mr-3" />
        <h1 className="text-4xl font-bold text-gray-900 dark:text-white">
          Page Title
        </h1>
      </div>
      <p className="text-xl text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
        Page description
      </p>
    </div>
    
    {/* Main Content */}
    <div className="grid xl:grid-cols-3 lg:grid-cols-2 gap-8">
      {/* Content sections */}
    </div>
  </div>
</div>
```

### Grid Layouts
```jsx
// Responsive Grid Pattern
<div className="grid xl:grid-cols-3 lg:grid-cols-2 gap-8">
  {/* Left panel - inputs/controls */}
  <div className="space-y-6">
    {/* Cards */}
  </div>
  
  {/* Right panels - results/output */}
  <div className="xl:col-span-2 space-y-6">
    {/* Results */}
  </div>
</div>

// Equal columns for features
<div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
  {/* Feature cards */}
</div>
```

## Interactive Elements

### Loading States
```jsx
// Spinner
<div className="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full" />

// Loading Card
<Card>
  <CardContent className="p-8 text-center">
    <div className="animate-spin inline-block w-8 h-8 border-4 border-current border-t-transparent text-blue-600 rounded-full mb-4" />
    <p className="text-gray-600 dark:text-gray-400">Loading...</p>
  </CardContent>
</Card>
```

### Error States
```jsx
// Error Alert
<Card className="bg-red-50 dark:bg-red-950 border-red-200 dark:border-red-800">
  <CardContent className="p-4">
    <div className="flex items-center space-x-2 text-red-600 dark:text-red-400">
      <AlertTriangle className="h-5 w-5" />
      <span className="font-semibold">Error Title</span>
    </div>
    <p className="text-red-700 dark:text-red-300 mt-1">Error message</p>
  </CardContent>
</Card>
```

### Success/Results Display
```jsx
// Highlight Results
<Card className="bg-gradient-to-r from-green-500 to-blue-600 text-white">
  <CardContent className="p-6">
    <div className="text-center">
      <TrendingUp className="h-12 w-12 mx-auto mb-4 opacity-90" />
      <h3 className="text-2xl font-bold mb-2">Success Title</h3>
      <p className="text-4xl font-bold">{formatValue(result)}</p>
    </div>
  </CardContent>
</Card>
```

## Data Visualization

### Charts & Graphs
```jsx
// Pie Chart Pattern
<div className="h-64">
  <ResponsiveContainer width="100%" height="100%">
    <PieChart>
      <Pie
        data={data}
        cx="50%"
        cy="50%"
        innerRadius={60}
        outerRadius={100}
        paddingAngle={5}
        dataKey="value"
      >
        {data.map((entry, index) => (
          <Cell key={`cell-${index}`} fill={entry.color} />
        ))}
      </Pie>
    </PieChart>
  </ResponsiveContainer>
</div>
```

### Metrics Display
```jsx
// Key Metric Card
<Card>
  <CardContent className="p-6">
    <div className="flex items-center justify-between">
      <div>
        <p className="text-sm text-gray-600 dark:text-gray-400">Metric Label</p>
        <p className="text-3xl font-bold text-gray-900 dark:text-white">
          {formatValue(value)}
        </p>
      </div>
      <div className="p-3 bg-blue-100 dark:bg-blue-900 rounded-full">
        <Icon className="h-8 w-8 text-blue-600 dark:text-blue-400" />
      </div>
    </div>
  </CardContent>
</Card>
```

## Utility Functions

### Formatting
```javascript
// Currency formatting
const formatCurrency = (amount) => {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount);
};

// Percentage formatting
const formatPercentage = (decimal) => {
  return `${Math.round(decimal * 100)}%`;
};
```

## Responsive Design

### Breakpoint Strategy
```css
/* Mobile First Approach */
.container {
  /* Base mobile styles */
  padding: 1rem;
}

@media (min-width: 768px) {
  .container {
    /* Tablet styles */
    padding: 1.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    /* Desktop styles */
    padding: 2rem;
  }
}

@media (min-width: 1280px) {
  .container {
    /* Large desktop styles */
    padding: 2.5rem;
  }
}
```

### Grid Breakpoints
- `grid-cols-1` (mobile)
- `md:grid-cols-2` (tablet)
- `lg:grid-cols-3` (desktop)
- `xl:grid-cols-4` (large desktop)

## Accessibility

### Color Contrast
- Ensure minimum 4.5:1 contrast ratio for normal text
- Use semantic colors for states (red for errors, green for success)
- Provide dark mode alternatives

### Interactive Elements
```jsx
// Accessible button
<Button
  aria-label="Descriptive action"
  className="focus:outline-none focus:ring-2 focus:ring-blue-500"
>
  Action
</Button>

// Form accessibility
<Label htmlFor="unique-id">Label Text</Label>
<Input
  id="unique-id"
  aria-describedby="help-text"
  aria-invalid={hasError}
/>
<p id="help-text" className="sr-only">Screen reader description</p>
```

## Performance Guidelines

### Image Optimization
- Use WebP format when possible
- Implement lazy loading for non-critical images
- Provide proper alt text for accessibility

### Code Splitting
```jsx
// Lazy load components
const LazyComponent = React.lazy(() => import('./Component'));

// Use with Suspense
<Suspense fallback={<LoadingSpinner />}>
  <LazyComponent />
</Suspense>
```

## Implementation Checklist

### New Application Setup
- [ ] Install Tailwind CSS with dark mode support
- [ ] Add Agent Factory color variables to CSS
- [ ] Implement neon effect classes
- [ ] Set up background image system
- [ ] Configure responsive grid system
- [ ] Add accessibility focus styles
- [ ] Implement loading and error states
- [ ] Set up proper TypeScript types
- [ ] Configure SEO meta tags
- [ ] Test dark/light mode toggle

### Component Development
- [ ] Follow card pattern for containers
- [ ] Use semantic HTML elements
- [ ] Implement proper loading states
- [ ] Add error handling UI
- [ ] Include accessibility attributes
- [ ] Test responsive behavior
- [ ] Validate color contrast
- [ ] Add hover and focus states

## Brand Assets

### Logo Usage
- Always use the official Agent Factory logo
- Maintain proper spacing around logo
- Use appropriate size variants (sm, md, lg)
- Apply neon glow effect for brand consistency

### File Structure
```
/assets
  /images
    - AgentFactoryBG.png (background)
    - AgentFactoryLogo.png (main logo)
    - AgentFactoryLogoHorizontal.png (horizontal variant)
  /icons
    - Various Lucide React icons
```

This style guide ensures consistency across all Agent Factory applications while maintaining the distinctive dark, tech-forward aesthetic with neon accents and professional functionality.