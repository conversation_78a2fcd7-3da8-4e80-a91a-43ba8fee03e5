import { useState } from 'react';
import { CloudinaryImage } from '@/components/ui/cloudinary-image';
import { ImageUpload } from '@/components/ui/image-upload';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Separator } from '@/components/ui/separator';

export default function CloudinaryDemo() {
  const [uploadedImageId, setUploadedImageId] = useState<string>('');

  const handleImageUpload = (publicId: string) => {
    setUploadedImageId(publicId);
  };

  const handleImageRemove = () => {
    setUploadedImageId('');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 py-8">
      <div className="container mx-auto px-4 max-w-4xl">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-white mb-4">
            Cloudinary Integration Demo
          </h1>
          <p className="text-gray-300">
            Test image display and upload functionality with Cloudinary
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Image Upload Section */}
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Upload Images</CardTitle>
              <CardDescription className="text-gray-300">
                Upload images directly to Cloudinary using an unsigned upload preset
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ImageUpload
                value={uploadedImageId}
                onChange={handleImageUpload}
                onRemove={handleImageRemove}
                maxWidth={400}
                maxHeight={300}
              />
              {uploadedImageId && (
                <div className="mt-4 p-3 bg-green-900/20 border border-green-700 rounded-md">
                  <p className="text-sm text-green-400">
                    ✅ Image uploaded successfully!
                  </p>
                  <p className="text-xs text-gray-400 mt-1">
                    Public ID: {uploadedImageId}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Image Display Section */}
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Display Images</CardTitle>
              <CardDescription className="text-gray-300">
                Display optimized images from Cloudinary with automatic transformations
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              {uploadedImageId ? (
                <div>
                  <h4 className="text-white font-medium mb-2">Your Uploaded Image:</h4>
                  <CloudinaryImage
                    publicId={uploadedImageId}
                    alt="Uploaded image"
                    width={300}
                    height={200}
                    className="rounded-lg border border-gray-600"
                  />
                </div>
              ) : (
                <div className="text-center py-8 text-gray-400">
                  Upload an image to see it displayed here with Cloudinary optimizations
                </div>
              )}

              <Separator className="bg-gray-600" />

              {/* Sample Cloudinary Image (if you have a sample public_id) */}
              <div>
                <h4 className="text-white font-medium mb-2">Sample Image (if available):</h4>
                <p className="text-sm text-gray-400 mb-2">
                  This will show a sample image if you have a test image in your Cloudinary account
                </p>
                <div className="border-2 border-dashed border-gray-600 rounded-lg p-4 text-center">
                  <p className="text-gray-500 text-sm">
                    Add a sample public_id to test image display
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="mt-8 text-center">
          <Card className="bg-gray-800/50 border-gray-700">
            <CardHeader>
              <CardTitle className="text-white">Configuration Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Cloud Name:</span>
                  <span className={import.meta.env.VITE_CLOUDINARY_CLOUD_NAME ? "text-green-400" : "text-red-400"}>
                    {import.meta.env.VITE_CLOUDINARY_CLOUD_NAME ? "✅ Configured" : "❌ Missing"}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-300">Upload Preset:</span>
                  <span className={import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET ? "text-green-400" : "text-red-400"}>
                    {import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET ? "✅ Configured" : "❌ Missing"}
                  </span>
                </div>
              </div>
              {(!import.meta.env.VITE_CLOUDINARY_CLOUD_NAME || !import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET) && (
                <div className="mt-4 p-3 bg-yellow-900/20 border border-yellow-700 rounded-md">
                  <p className="text-sm text-yellow-400">
                    ⚠️ Please configure your Cloudinary environment variables to test functionality
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}