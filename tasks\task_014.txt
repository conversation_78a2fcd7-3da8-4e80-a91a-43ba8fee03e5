# Task ID: 14
# Title: Implement Role-Based Access Control
# Status: done
# Dependencies: 5, 13
# Priority: medium
# Description: Maintain and enhance role-based access control within tenant context
# Details:
1. Design role-based access control schema
2. Implement role assignment within tenants
3. Create permission checking middleware
4. Update frontend components to respect permissions
5. Implement role management UI for tenant admins
6. Example RBAC implementation:
```javascript
// Backend permission middleware
const checkPermission = (requiredPermission) => {
  return async (req, res, next) => {
    if (!req.user || !req.tenantId) {
      return res.status(401).json({ error: 'Authentication required' });
    }
    
    // Get user's role in this tenant
    const { data, error } = await supabase
      .from('tenant_users')
      .select('role')
      .eq('tenant_id', req.tenantId)
      .eq('user_id', req.user.id)
      .single();
    
    if (error || !data) {
      return res.status(403).json({ error: 'Access denied' });
    }
    
    // Check if role has the required permission
    const { data: permissions, error: permError } = await supabase
      .from('role_permissions')
      .select('permission')
      .eq('role', data.role)
      .eq('permission', requiredPermission);
    
    if (permError || permissions.length === 0) {
      return res.status(403).json({ error: 'Insufficient permissions' });
    }
    
    next();
  };
};

// Usage in routes
app.post('/api/projects', 
  authMiddleware, 
  tenantMiddleware, 
  checkPermission('create:projects'), 
  async (req, res) => {
    // Create project logic
  }
);

// Frontend permission hook
export function usePermission(requiredPermission) {
  const { user } = useAuth();
  const { currentTenant } = useTenant();
  const [hasPermission, setHasPermission] = useState(false);
  
  useEffect(() => {
    if (!user || !currentTenant) {
      setHasPermission(false);
      return;
    }
    
    // Check permission from API
    fetch(`https://api.agent-factory.io/permissions/check?permission=${requiredPermission}`)
      .then(res => res.json())
      .then(data => setHasPermission(data.hasPermission))
      .catch(() => setHasPermission(false));
  }, [user, currentTenant, requiredPermission]);
  
  return hasPermission;
}
```

# Test Strategy:
Test permission checks with different user roles. Verify UI elements are correctly shown/hidden based on permissions. Test permission assignment and management functionality.
