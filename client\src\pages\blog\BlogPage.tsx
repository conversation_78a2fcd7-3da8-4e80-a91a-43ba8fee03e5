import { Suspense, lazy } from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { BlogListSkeleton } from "@/components/blog/BlogLoadingStates";

// Lazy load the blog list component for optimal performance
const BlogList = lazy(() => import("@/components/blog/BlogList"));

/**
 * Optimized Blog Page with Code Splitting
 * 
 * Performance Optimizations:
 * - Lazy loading of BlogList component reduces initial bundle size
 * - Dedicated loading skeleton improves perceived performance
 * - Component-level code splitting for better caching
 */
export default function BlogPage() {
  return (
    <MainLayout>
      <div className="max-w-7xl mx-auto px-4 py-12 space-y-8">
        {/* Page Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">
            Agent Factory Blog
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Insights on automation, AI agents, and business process optimization
          </p>
        </div>

        {/* Lazy-loaded Blog List with minimal loading */}
        <Suspense fallback={
          <div className="text-center py-8">
            <div className="inline-flex items-center gap-2 text-muted-foreground">
              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              Loading articles...
            </div>
          </div>
        }>
          <BlogList />
        </Suspense>
      </div>
    </MainLayout>
  );
}