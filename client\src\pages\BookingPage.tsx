import { useState, useEffect, useRef } from "react";
import { MainLayout } from "@/components/layouts/MainLayout";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Calendar, Clock, CheckCircle, RefreshCw, AlertTriangle, ExternalLink } from "lucide-react";

interface BookingService {
  id: string;
  name: string;
  duration: string;
  price: string;
  description: string;
  bookingUrl?: string;
}

interface MicrosoftBookingProps {
  bookingUrl: string;
  onBookingComplete?: (data: any) => void;
  onError?: (error: string) => void;
}

const MicrosoftBookingIframe = ({ bookingUrl, onBookingComplete, onError }: MicrosoftBookingProps) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasError, setHasError] = useState(false);
  const [iframeHeight, setIframeHeight] = useState(600);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  useEffect(() => {
    const handleMessage = (event: MessageEvent) => {
      // Handle messages from Microsoft Booking iframe
      if (event.origin.includes('bookings.microsoft.com') || event.origin.includes('outlook.office365.com')) {
        try {
          if (typeof event.data === 'string') {
            const data = JSON.parse(event.data);
            
            if (data.type === 'bookingComplete') {
              onBookingComplete?.(data);
            } else if (data.type === 'error') {
              setHasError(true);
              onError?.(data.message || 'Booking system error');
            } else if (data.type === 'resize' && data.height) {
              setIframeHeight(Math.max(400, data.height));
            }
          }
        } catch (error) {
          // Handle non-JSON messages silently
          console.debug('Non-JSON message from iframe:', event.data);
        }
      }
    };

    window.addEventListener('message', handleMessage);
    return () => window.removeEventListener('message', handleMessage);
  }, [onBookingComplete, onError]);

  const handleIframeLoad = () => {
    setIsLoading(false);
    setHasError(false);
    
    // Auto-resize functionality
    const iframe = iframeRef.current;
    if (iframe && iframe.contentWindow) {
      try {
        const resizeObserver = new ResizeObserver(() => {
          try {
            const contentHeight = iframe.contentDocument?.body.scrollHeight;
            if (contentHeight && contentHeight > 300) {
              setIframeHeight(Math.min(contentHeight + 50, 800));
            }
          } catch (e) {
            // Cross-origin restrictions - fallback to default height
          }
        });
        
        if (iframe.contentDocument?.body) {
          resizeObserver.observe(iframe.contentDocument.body);
        }
      } catch (e) {
        // Cross-origin restrictions
        console.debug('Auto-resize not available due to cross-origin restrictions');
      }
    }
  };

  const handleIframeError = () => {
    setIsLoading(false);
    setHasError(true);
    onError?.('Failed to load booking system');
  };

  const handleRetry = () => {
    setIsLoading(true);
    setHasError(false);
    
    // Force iframe reload
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  };

  if (hasError) {
    return (
      <div className="space-y-4">
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Unable to load the booking system. Please try again or contact us directly.
          </AlertDescription>
        </Alert>
        
        <div className="flex flex-col sm:flex-row gap-3">
          <Button onClick={handleRetry} variant="outline" className="flex-1">
            <RefreshCw className="h-4 w-4 mr-2" />
            Retry Loading
          </Button>
          <Button asChild className="flex-1">
            <a href="mailto:<EMAIL>" target="_blank" rel="noopener noreferrer">
              <ExternalLink className="h-4 w-4 mr-2" />
              Contact Us Directly
            </a>
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="relative w-full">
      {isLoading && (
        <div className="absolute inset-0 z-10 bg-background/80 backdrop-blur-sm flex items-center justify-center rounded-lg">
          <div className="text-center space-y-4">
            <div className="animate-spin">
              <Calendar className="h-8 w-8 text-primary" />
            </div>
            <p className="text-sm text-muted-foreground">Loading booking calendar...</p>
          </div>
        </div>
      )}
      
      <iframe
        ref={iframeRef}
        src={bookingUrl}
        className="w-full border-0 rounded-lg bg-background transition-all duration-300"
        style={{ height: `${iframeHeight}px`, minHeight: '400px' }}
        onLoad={handleIframeLoad}
        onError={handleIframeError}
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-popups-to-escape-sandbox"
        title="Microsoft Booking Calendar"
        allow="camera; microphone; geolocation"
      />
    </div>
  );
};

export default function BookingPage() {
  const [selectedService, setSelectedService] = useState<string>("");
  const [bookingCompleted, setBookingCompleted] = useState(false);

  const services: BookingService[] = [
    {
      id: "automation-consultation",
      name: "Automation Envisioning Session",
      duration: "30 minutes",
      price: "Free",
      description: "Complimentary consultation to explore automation opportunities and see if we're a good fit"
    },
    {
      id: "ai-agent-discovery", 
      name: "AI Agent Discovery Call",
      duration: "30 minutes",
      price: "Free", 
      description: "Free exploration of how AI agents could transform your specific business processes"
    },
    {
      id: "integration-assessment",
      name: "Integration Assessment", 
      duration: "30 minutes",
      price: "Free",
      description: "Complimentary review of your current systems and integration possibilities"
    }
  ];

  const selectedServiceData = services.find(s => s.id === selectedService);

  const handleBookingComplete = (data: any) => {
    setBookingCompleted(true);
    console.log('Booking completed:', data);
  };

  const handleBookingError = (error: string) => {
    console.error('Booking error:', error);
  };

  if (bookingCompleted) {
    return (
      <MainLayout>
        <div className="max-w-2xl mx-auto px-4 py-16 text-center space-y-6">
          <div className="w-16 h-16 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">Booking Confirmed!</h1>
            <p className="text-lg text-muted-foreground">
              Thank you for scheduling your consultation. You'll receive a confirmation email shortly.
            </p>
          </div>

          <div className="space-y-4 text-left bg-muted/50 rounded-lg p-6">
            <h3 className="font-semibold">What happens next?</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                You'll receive a calendar invitation with meeting details
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                We'll send you a pre-consultation questionnaire
              </li>
              <li className="flex items-center gap-2">
                <CheckCircle className="h-4 w-4 text-primary" />
                Our team will prepare customized recommendations
              </li>
            </ul>
          </div>

          <Button onClick={() => window.location.href = '/'}>
            Return to Homepage
          </Button>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <div className="max-w-6xl mx-auto px-4 py-12 space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-4xl font-bold">
            Book Your Free Envisioning Session
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
            Schedule a complimentary 30-minute session to explore how automation can transform your business and see if we're a good fit
          </p>
        </div>

        {/* Single Service Card */}
        <div className="max-w-2xl mx-auto">
          <Card className="border-2 border-primary shadow-lg">
            <CardHeader className="text-center">
              <CardTitle className="text-2xl mb-2">Free Envisioning Session</CardTitle>
              <div className="flex justify-center items-center gap-6 text-muted-foreground">
                <span className="flex items-center gap-2">
                  <Clock className="w-5 h-5" />
                  30 minutes
                </span>
                <span className="text-2xl font-bold text-primary">FREE</span>
              </div>
            </CardHeader>
            <CardContent className="text-center space-y-4">
              <p className="text-lg leading-relaxed">
                Let's explore automation opportunities in your business and see if Agent Factory is the right fit for your needs.
              </p>
              <Button 
                size="lg" 
                className="w-full"
                onClick={() => setSelectedService("envisioning-session")}
              >
                Schedule Your Free Session
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Booking Section */}
        {selectedService && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Calendar className="w-5 h-5" />
                Schedule Your Free Envisioning Session
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Session Summary */}
              <div className="bg-muted/50 rounded-lg p-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">FREE</div>
                  <div className="text-sm text-muted-foreground">No Cost</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">30 min</div>
                  <div className="text-sm text-muted-foreground">Duration</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold">1-on-1</div>
                  <div className="text-sm text-muted-foreground">Personal Session</div>
                </div>
              </div>

              {/* Booking Interface */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div className="space-y-4">
                  <h3 className="text-lg font-semibold">What You'll Get</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">Personalized Assessment</div>
                        <div className="text-sm text-muted-foreground">Deep dive into your current processes and pain points</div>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">Custom Strategy</div>
                        <div className="text-sm text-muted-foreground">Tailored automation roadmap for your business</div>
                      </div>
                    </li>
                    <li className="flex items-start gap-3">
                      <CheckCircle className="w-5 h-5 text-primary mt-0.5 flex-shrink-0" />
                      <div>
                        <div className="font-medium">ROI Projections</div>
                        <div className="text-sm text-muted-foreground">Measurable outcomes</div>
                      </div>
                    </li>

                  </ul>

                  <div className="pt-4 border-t">
                    <h4 className="font-medium mb-2">Need Help?</h4>
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p><strong>Questions?</strong> <a href="mailto:<EMAIL>" className="text-primary hover:underline">Contact us</a></p>
                      <p><strong>Response Time:</strong> Within 24 hours</p>
                    </div>
                  </div>
                </div>

                <div className="col-span-full">
                  <MicrosoftBookingIframe
                    bookingUrl="https://outlook.office.com/owa/calendar/<EMAIL>/bookings/"
                    onBookingComplete={handleBookingComplete}
                    onError={handleBookingError}
                  />
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </MainLayout>
  );
}