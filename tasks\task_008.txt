# Task ID: 8
# Title: Update Backend API Routes
# Status: done
# Dependencies: 5, 6
# Priority: high
# Description: Modify existing API routes to work with Supabase authentication and multi-tenant context
# Details:
1. Update all API route handlers to use new authentication middleware
2. Modify database queries to use Supabase client
3. Remove session-based authentication code
4. Ensure all queries respect tenant context
5. Update error handling for authentication failures
6. Maintain existing API response formats
7. Example API route update:
```javascript
// Before
app.get('/api/projects', sessionAuth, async (req, res) => {
  try {
    const projects = await db.getProjects(req.session.userId);
    res.json(projects);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// After
app.get('/api/projects', authMiddleware, tenantMiddleware, async (req, res) => {
  try {
    const { data, error } = await supabase
      .from('projects')
      .select('*');
      
    if (error) throw error;
    res.json(data);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});
```

# Test Strategy:
Test each updated API endpoint with valid and invalid authentication. Verify tenant isolation works correctly. Compare response formats with original API to ensure compatibility.
