---
description:
globs:
alwaysApply: false
---
# Drizzle-Zod Type Inference Fix

## Issue Summary
When using `z.infer<typeof createSelectSchema(table)>` directly, TypeScript may fail to properly infer types with errors like:
- "Type 'CreateSelectSchema' does not satisfy the constraint 'ZodType<any, any, any>'"
- Missing properties in inferred types

## Root Cause
- drizzle-zod 0.8.x requires Zod v4 (breaking change)
- Direct type inference on function calls can fail in certain TypeScript configurations
- The issue affects createSelectSchema, createInsertSchema, and createUpdateSchema

## Solution: Variable-First Pattern

### ✅ DO: Assign schemas to variables first
```typescript
// Define all schemas as variables first for proper type inference
const selectUserSchema = createSelectSchema(users);
const insertUserBaseSchema = createInsertSchema(users);
const updateUserBaseSchema = createUpdateSchema(users);

// Then extend/modify as needed
export const insertUserSchema = insertUserBaseSchema
  .extend({
    email: z.string().email("Invalid email address"),
    role: z.enum(userRoles).default("user"),
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

// Export clean types using the variables
export type User = z.infer<typeof selectUserSchema>;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type UpdateUser = z.infer<typeof updateUserSchema>;
```

### ❌ DON'T: Use direct inference
```typescript
// This may cause type inference errors
export type User = z.infer<typeof createSelectSchema(users)>;
```

## Version Compatibility
- **drizzle-zod 0.7.0**: Compatible with Zod v3
- **drizzle-zod 0.8.x**: Requires Zod v4 (breaking change)
- Current project uses: drizzle-zod@^0.7.0 with zod@^3.24.2

## Implementation Pattern
1. Create base schema variables for all tables
2. Extend/modify schemas as needed
3. Export types using the variable references
4. Apply this pattern consistently across all schema files

## Benefits
- Reliable type inference
- Better TypeScript performance
- Cleaner, more maintainable code
- Easier to debug type issues

## Related Files
- [schema.ts](mdc:shared/schema.ts) - Main schema file with the fix applied
- [package.json](mdc:package.json) - Version constraints
