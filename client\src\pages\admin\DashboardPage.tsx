import { useQuery } from "@tanstack/react-query";
import { <PERSON> } from "wouter";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { AdminLayout } from "@/components/layouts/AdminLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { hasPermission } from "@/lib/auth";
import { api } from "@/lib/apiClient";
import { 
  Users, 
  FileText, 
  Edit, 
  Activity, 
  Settings,
  Plus,
  UserPlus,
  BarChart3
} from "lucide-react";

export default function DashboardPage() {
  const { user, isLoading: isAuthLoading } = useAuthContext();

  const { data: stats, isLoading: isStatsLoading } = useQuery({
    queryKey: ["/api/admin/stats"],
    queryFn: () => api.getAdminStats(),
    enabled: !!user,
  });

  if (isAuthLoading) {
    return (
      <AdminLayout title="Loading...">
        <div className="flex items-center justify-center min-h-64">
          <div className="animate-pulse text-muted-foreground">Loading dashboard...</div>
        </div>
      </AdminLayout>
    );
  }

  if (!user) {
    return (
      <AdminLayout title="Access Denied">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Please Log In</h1>
            <p className="text-gray-600 dark:text-gray-400">
              You need to be logged in to access the admin dashboard.
            </p>
            <Link href="/auth/login">
              <Button>
                Login
              </Button>
            </Link>
          </div>
        </div>
      </AdminLayout>
    );
  }

  const canManageUsers = hasPermission(user.role, "manage_users");
  const canManageBlog = hasPermission(user.role, "manage_blog");
  const canManageSystem = hasPermission(user.role, "manage_system");

  if (!canManageUsers && !canManageBlog && !canManageSystem) {
    return (
      <AdminLayout title="Access Denied">
        <div className="flex items-center justify-center min-h-64">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Access Denied</h1>
            <p className="text-gray-600 dark:text-gray-400">
              You don't have permission to access the admin dashboard.
            </p>
            <Link href="/">
              <Button>
                Return Home
              </Button>
            </Link>
          </div>
        </div>
      </AdminLayout>
    );
  }

  return (
    <AdminLayout title="Dashboard">
      <div className="space-y-8">
        {/* Welcome Message */}
        <div className="space-y-2">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            Welcome back, {user.firstName || user.email}!
          </h2>
          <p className="text-gray-600 dark:text-gray-400">
            Here's an overview of your platform.
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {canManageUsers && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  Total Users
                </CardTitle>
                <Users className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {(stats as any)?.totalUsers || 0}
                </div>
                <p className="text-xs text-muted-foreground">
                  Active platform users
                </p>
              </CardContent>
            </Card>
          )}

          {canManageBlog && (
            <>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Blog Posts
                  </CardTitle>
                  <FileText className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(stats as any)?.totalPosts || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Published articles
                  </p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Drafts
                  </CardTitle>
                  <Edit className="h-4 w-4 text-blue-600" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {(stats as any)?.draftPosts || 0}
                  </div>
                  <p className="text-xs text-muted-foreground">
                    Unpublished drafts
                  </p>
                </CardContent>
              </Card>
            </>
          )}

          {canManageSystem && (
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">
                  System Status
                </CardTitle>
                <Activity className="h-4 w-4 text-blue-600" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  Online
                </div>
                <p className="text-xs text-muted-foreground">
                  All systems operational
                </p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Quick Actions */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {canManageBlog && (
              <Link href="/admin/blog">
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-lg bg-blue-100 dark:bg-blue-900 flex items-center justify-center">
                        <Plus className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">New Blog Post</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Create a new article</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )}

            {canManageUsers && (
              <Link href="/admin/users">
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-lg bg-green-100 dark:bg-green-900 flex items-center justify-center">
                        <UserPlus className="h-6 w-6 text-green-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">Manage Users</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">View and edit users</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )}

            {canManageSystem && (
              <Link href="/admin/settings">
                <Card className="hover:shadow-lg transition-shadow cursor-pointer">
                  <CardContent className="p-6">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 rounded-lg bg-purple-100 dark:bg-purple-900 flex items-center justify-center">
                        <Settings className="h-6 w-6 text-purple-600" />
                      </div>
                      <div>
                        <h3 className="font-medium text-gray-900 dark:text-white">System Settings</h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Configure platform</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )}
          </div>
        </div>

        {/* Recent Activity */}
        {canManageBlog && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">Recent Activity</h2>
            <Card>
              <CardContent className="p-6">
                <div className="text-center text-gray-600 dark:text-gray-400">
                  <BarChart3 className="w-12 h-12 mx-auto mb-4 opacity-50" />
                  <p>Activity tracking coming soon...</p>
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AdminLayout>
  );
}