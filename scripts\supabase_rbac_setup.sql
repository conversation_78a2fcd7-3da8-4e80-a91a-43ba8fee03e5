-- RBAC Setup for Agent Factory Pro - Proper Supabase RBAC Implementation
-- Based on Context7 Supabase RBAC documentation
-- Run this AFTER the main schema setup to add proper RBAC

-- Create app permissions enum
CREATE TYPE public.app_permission AS ENUM (
  'users.read',
  'users.write', 
  'users.delete',
  'tenants.read',
  'tenants.write',
  'tenants.delete',
  'blog.read',
  'blog.write',
  'blog.delete',
  'invoices.read',
  'invoices.write',
  'invoices.delete',
  'plans.read',
  'plans.write',
  'system.admin'
);

-- Create app roles enum (same as user_role_enum but for RBAC)
CREATE TYPE public.app_role AS ENUM (
  'lead',
  'customer', 
  'support',
  'billing_admin',
  'sales',
  'blog_admin',
  'super_admin'
);

-- User roles table - maps users to their roles
CREATE TABLE public.user_roles (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  user_id UUID REFERENCES auth.users ON DELETE CASCADE NOT NULL,
  role app_role NOT NULL,
  UNIQUE (user_id, role)
);

-- Role permissions table - maps roles to their permissions
CREATE TABLE public.role_permissions (
  id BIGINT GENERATED BY DEFAULT AS IDENTITY PRIMARY KEY,
  role app_role NOT NULL,
  permission app_permission NOT NULL,
  UNIQUE (role, permission)
);

-- Insert role permissions mapping
INSERT INTO public.role_permissions (role, permission) VALUES
-- Lead permissions (minimal)
('lead', 'users.read'),

-- Customer permissions 
('customer', 'users.read'),
('customer', 'users.write'),
('customer', 'tenants.read'),
('customer', 'blog.read'),
('customer', 'invoices.read'),

-- Support permissions
('support', 'users.read'),
('support', 'users.write'),
('support', 'tenants.read'),
('support', 'blog.read'),
('support', 'invoices.read'),

-- Billing admin permissions
('billing_admin', 'users.read'),
('billing_admin', 'users.write'),
('billing_admin', 'tenants.read'),
('billing_admin', 'tenants.write'),
('billing_admin', 'invoices.read'),
('billing_admin', 'invoices.write'),
('billing_admin', 'invoices.delete'),
('billing_admin', 'plans.read'),

-- Sales permissions
('sales', 'users.read'),
('sales', 'users.write'),
('sales', 'tenants.read'),
('sales', 'tenants.write'),
('sales', 'blog.read'),
('sales', 'invoices.read'),
('sales', 'plans.read'),

-- Blog admin permissions
('blog_admin', 'users.read'),
('blog_admin', 'blog.read'),
('blog_admin', 'blog.write'),
('blog_admin', 'blog.delete'),

-- Super admin permissions (all)
('super_admin', 'users.read'),
('super_admin', 'users.write'),
('super_admin', 'users.delete'),
('super_admin', 'tenants.read'),
('super_admin', 'tenants.write'),
('super_admin', 'tenants.delete'),
('super_admin', 'blog.read'),
('super_admin', 'blog.write'),
('super_admin', 'blog.delete'),
('super_admin', 'invoices.read'),
('super_admin', 'invoices.write'),
('super_admin', 'invoices.delete'),
('super_admin', 'plans.read'),
('super_admin', 'plans.write'),
('super_admin', 'system.admin');

-- Authorization function for RLS policies
CREATE OR REPLACE FUNCTION public.authorize(
  requested_permission app_permission
)
RETURNS BOOLEAN AS $$
DECLARE
  bind_permissions INT;
  user_role public.app_role;
BEGIN
  -- Fetch user role from JWT claims
  SELECT (auth.jwt() ->> 'user_role')::public.app_role INTO user_role;

  -- Count matching permissions
  SELECT COUNT(*)
  INTO bind_permissions
  FROM public.role_permissions
  WHERE role_permissions.permission = requested_permission
    AND role_permissions.role = user_role;

  RETURN bind_permissions > 0;
END;
$$ LANGUAGE plpgsql STABLE SECURITY DEFINER SET search_path = '';

-- Custom access token hook to inject user role into JWT
CREATE OR REPLACE FUNCTION public.custom_access_token_hook(event jsonb)
RETURNS jsonb
LANGUAGE plpgsql
STABLE
AS $$
DECLARE
  claims jsonb;
  user_role public.app_role;
BEGIN
  -- Fetch the user role from the user_roles table
  SELECT role INTO user_role 
  FROM public.user_roles 
  WHERE user_id = (event->>'user_id')::uuid;

  claims := event->'claims';

  IF user_role IS NOT NULL THEN
    -- Set the claim
    claims := jsonb_set(claims, '{user_role}', to_jsonb(user_role));
  ELSE
    -- Default to 'lead' if no role found
    claims := jsonb_set(claims, '{user_role}', '"lead"');
  END IF;

  -- Update the 'claims' object in the original event
  event := jsonb_set(event, '{claims}', claims);

  -- Return the modified event
  RETURN event;
END;
$$;

-- Grant permissions for the auth hook
GRANT USAGE ON SCHEMA public TO supabase_auth_admin;

GRANT EXECUTE
  ON FUNCTION public.custom_access_token_hook
  TO supabase_auth_admin;

REVOKE EXECUTE
  ON FUNCTION public.custom_access_token_hook
  FROM authenticated, anon, public;

GRANT ALL
  ON TABLE public.user_roles
  TO supabase_auth_admin;

REVOKE ALL
  ON TABLE public.user_roles
  FROM authenticated, anon, public;

GRANT ALL
  ON TABLE public.role_permissions
  TO supabase_auth_admin;

REVOKE ALL
  ON TABLE public.role_permissions
  FROM authenticated, anon, public;

-- Create policy for auth admin to read user roles
CREATE POLICY "Allow auth admin to read user roles" ON public.user_roles
AS PERMISSIVE FOR SELECT
TO supabase_auth_admin
USING (true);

-- Create policy for auth admin to read role permissions
CREATE POLICY "Allow auth admin to read role permissions" ON public.role_permissions
AS PERMISSIVE FOR SELECT
TO supabase_auth_admin
USING (true);

-- Enable RLS on RBAC tables
ALTER TABLE public.user_roles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.role_permissions ENABLE ROW LEVEL SECURITY;

-- Update existing RLS policies to use the authorize function
-- Drop existing policies first
DROP POLICY IF EXISTS "Super admins can manage all plan configs" ON public.plan_configs;
DROP POLICY IF EXISTS "Super admins can manage all tenants" ON public.tenants;
DROP POLICY IF EXISTS "Super admins can manage all users" ON public.users;
DROP POLICY IF EXISTS "Support can view all users" ON public.users;
DROP POLICY IF EXISTS "Sales can view leads and customers" ON public.users;

-- Create new RBAC-based policies for plan_configs
CREATE POLICY "Allow authorized plan read access" ON public.plan_configs 
  FOR SELECT TO authenticated 
  USING (authorize('plans.read'));

CREATE POLICY "Allow authorized plan write access" ON public.plan_configs 
  FOR INSERT TO authenticated 
  WITH CHECK (authorize('plans.write'));

CREATE POLICY "Allow authorized plan update access" ON public.plan_configs 
  FOR UPDATE TO authenticated 
  USING (authorize('plans.write'));

CREATE POLICY "Allow authorized plan delete access" ON public.plan_configs 
  FOR DELETE TO authenticated 
  USING (authorize('plans.write'));

-- Create new RBAC-based policies for tenants
CREATE POLICY "Allow authorized tenant read access" ON public.tenants 
  FOR SELECT TO authenticated 
  USING (authorize('tenants.read'));

CREATE POLICY "Allow authorized tenant write access" ON public.tenants 
  FOR INSERT TO authenticated 
  WITH CHECK (authorize('tenants.write'));

CREATE POLICY "Allow authorized tenant update access" ON public.tenants 
  FOR UPDATE TO authenticated 
  USING (authorize('tenants.write'));

CREATE POLICY "Allow authorized tenant delete access" ON public.tenants 
  FOR DELETE TO authenticated 
  USING (authorize('tenants.delete'));

-- Create new RBAC-based policies for users
CREATE POLICY "Allow authorized user read access" ON public.users 
  FOR SELECT TO authenticated 
  USING (
    -- Users can always read their own profile
    auth.uid()::text = external_id 
    OR 
    -- Or if they have users.read permission
    authorize('users.read')
  );

CREATE POLICY "Allow authorized user write access" ON public.users 
  FOR INSERT TO authenticated 
  WITH CHECK (authorize('users.write'));

CREATE POLICY "Allow authorized user update access" ON public.users 
  FOR UPDATE TO authenticated 
  USING (
    -- Users can update their own profile (but not role/status)
    (auth.uid()::text = external_id AND 
     role = (SELECT role FROM public.users WHERE external_id = auth.uid()::text) AND
     is_active = (SELECT is_active FROM public.users WHERE external_id = auth.uid()::text))
    OR 
    -- Or if they have users.write permission
    authorize('users.write')
  );

CREATE POLICY "Allow authorized user delete access" ON public.users 
  FOR DELETE TO authenticated 
  USING (authorize('users.delete'));

-- Create new RBAC-based policies for blog_posts
CREATE POLICY "Allow authorized blog read access" ON public.blog_posts 
  FOR SELECT TO authenticated 
  USING (
    -- Published posts are visible to anyone with blog.read
    (is_published = true AND authorize('blog.read'))
    OR
    -- All posts visible to those with blog.write
    authorize('blog.write')
  );

CREATE POLICY "Allow authorized blog write access" ON public.blog_posts 
  FOR INSERT TO authenticated 
  WITH CHECK (authorize('blog.write'));

CREATE POLICY "Allow authorized blog update access" ON public.blog_posts 
  FOR UPDATE TO authenticated 
  USING (authorize('blog.write'));

CREATE POLICY "Allow authorized blog delete access" ON public.blog_posts 
  FOR DELETE TO authenticated 
  USING (authorize('blog.delete'));

-- Create new RBAC-based policies for invoices
CREATE POLICY "Allow authorized invoice read access" ON public.invoices 
  FOR SELECT TO authenticated 
  USING (authorize('invoices.read'));

CREATE POLICY "Allow authorized invoice write access" ON public.invoices 
  FOR INSERT TO authenticated 
  WITH CHECK (authorize('invoices.write'));

CREATE POLICY "Allow authorized invoice update access" ON public.invoices 
  FOR UPDATE TO authenticated 
  USING (authorize('invoices.write'));

CREATE POLICY "Allow authorized invoice delete access" ON public.invoices 
  FOR DELETE TO authenticated 
  USING (authorize('invoices.delete'));

-- Function to assign role to user (for admin use)
CREATE OR REPLACE FUNCTION public.assign_user_role(
  user_uuid UUID,
  user_role app_role
)
RETURNS VOID AS $$
BEGIN
  INSERT INTO public.user_roles (user_id, role)
  VALUES (user_uuid, user_role)
  ON CONFLICT (user_id, role) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission on the function to service role
GRANT EXECUTE ON FUNCTION public.assign_user_role TO service_role;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'RBAC setup completed successfully!';
  RAISE NOTICE 'Created user_roles and role_permissions tables';
  RAISE NOTICE 'Configured custom access token hook for JWT claims';
  RAISE NOTICE 'Updated all RLS policies to use authorize() function';
  RAISE NOTICE 'IMPORTANT: You must configure the auth hook in Supabase Dashboard:';
  RAISE NOTICE '1. Go to Authentication > Hooks';
  RAISE NOTICE '2. Create a new "Custom Access Token" hook';
  RAISE NOTICE '3. Set the hook to call: public.custom_access_token_hook';
  RAISE NOTICE '4. Test by assigning roles to users with assign_user_role function';
END $$; 