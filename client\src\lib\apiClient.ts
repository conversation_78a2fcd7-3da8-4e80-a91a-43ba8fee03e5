import { supabase } from './supabaseClient';

/**
 * HTTP Client that automatically includes Supabase JWT tokens
 * and tenant context for authenticated requests to our backend API
 */
class ApiClient {
  private baseUrl: string;
  private tenantId: number | null = null;

  constructor() {
    // Use the current origin for API calls (works in both dev and production)
    this.baseUrl = '';
  }

  // Set tenant ID for API requests
  setTenantId(tenantId: number | null) {
    this.tenantId = tenantId;
  }

  // Get current tenant ID
  getTenantId(): number | null {
    return this.tenantId;
  }

  // Get auth headers with automatic tenant context
  private async getHeaders(includeAuth = true): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    if (includeAuth && supabase) {
      try {
        const { data: { session } } = await supabase.auth.getSession();
        if (session?.access_token) {
          headers.Authorization = `Bearer ${session.access_token}`;
        }
      } catch (error) {
        console.warn('Failed to get auth token:', error);
      }
    }

    // Automatically include tenant ID if available
    if (this.tenantId) {
      headers['X-Tenant-ID'] = this.tenantId.toString();
    }

    return headers;
  }

  // Generic request method
  private async request<T>(
    endpoint: string, 
    options: RequestInit = {},
    includeAuth = true
  ): Promise<T> {
    const headers = await this.getHeaders(includeAuth);
    
    const config: RequestInit = {
      headers: {
        ...headers,
        ...options.headers,
      },
      ...options,
    };

    const response = await fetch(endpoint, config);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} ${response.statusText} - ${errorText}`);
    }

    return response.json();
  }

  // Auth methods
  async loginUser(credentials: { email: string; password: string }) {
    return this.request('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    }, false);
  }

  async registerUser(userData: any) {
    return this.request('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    }, false);
  }

  async logoutUser() {
    return this.request('/api/auth/logout', {
      method: 'POST',
    });
  }

  async getCurrentUser() {
    return this.request('/api/auth/user');
  }

  // Tenant methods
  async getTenantBySubdomain(subdomain: string) {
    return this.request(`/api/tenants/by-subdomain/${subdomain}`, {}, false);
  }

  async getTenantContext() {
    return this.request('/api/tenant/context');
  }

  // Blog methods
  async getBlogPosts() {
    return this.request('/api/blog/posts', {}, false);
  }

  async getBlogPost(slug: string) {
    return this.request(`/api/blog/posts/${slug}`, {}, false);
  }

  async createBlogPost(postData: any) {
    return this.request('/api/admin/blog/posts', {
      method: 'POST',
      body: JSON.stringify(postData),
    });
  }

  async updateBlogPost(id: number, postData: any) {
    return this.request(`/api/admin/blog/posts/${id}`, {
      method: 'PUT',
      body: JSON.stringify(postData),
    });
  }

  async deleteBlogPost(id: number) {
    return this.request(`/api/admin/blog/posts/${id}`, {
      method: 'DELETE',
    });
  }

  async getAdminBlogPosts() {
    return this.request('/api/admin/blog/posts');
  }

  // Admin methods
  async getAdminStats() {
    return this.request('/api/admin/stats');
  }

  async getAllUsers() {
    return this.request('/api/admin/users');
  }

  async updateUserRole(userId: number, role: string) {
    return this.request(`/api/admin/users/${userId}/role`, {
      method: 'PUT',
      body: JSON.stringify({ role }),
    });
  }

  async updateUserStatus(userId: number, isActive: boolean) {
    return this.request(`/api/admin/users/${userId}/status`, {
      method: 'PUT',
      body: JSON.stringify({ isActive }),
    });
  }

  // Profile methods
  async updateProfile(data: any) {
    return this.request('/api/auth/profile', {
      method: 'PUT',
      body: JSON.stringify(data),
    });
  }

  // Health check
  async healthCheck() {
    return this.request('/api/health', {}, false);
  }
}

// Export a singleton instance
export const apiClient = new ApiClient();

// Convenience functions for common API calls
export const api = {
  // Blog Posts
  getBlogPosts: () => apiClient.getBlogPosts(),
  getBlogPost: (slug: string) => apiClient.getBlogPost(slug),
  
  // Admin Blog Posts
  getAdminBlogPosts: () => apiClient.getAdminBlogPosts(),
  createBlogPost: (post: any) => apiClient.createBlogPost(post),
  updateBlogPost: (id: number, post: any) => apiClient.updateBlogPost(id, post),
  deleteBlogPost: (id: number) => apiClient.deleteBlogPost(id),
  
  // User Management
  getUsers: () => apiClient.getAllUsers(),
  updateUserRole: (id: number, role: string) => apiClient.updateUserRole(id, role),
  updateUserStatus: (id: number, isActive: boolean) => apiClient.updateUserStatus(id, isActive),
  
  // Admin Stats
  getAdminStats: () => apiClient.getAdminStats(),
  
  // Profile
  updateProfile: (data: any) => apiClient.updateProfile(data),
  
  // Health Check
  healthCheck: () => apiClient.healthCheck(),
  
  // Tenant Context
  getTenantContext: () => apiClient.getTenantContext(),
}; 