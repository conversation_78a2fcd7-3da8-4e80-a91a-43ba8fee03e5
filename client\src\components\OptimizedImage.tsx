import { useState, useRef, useCallback } from 'react';
import { LazyLoad } from './LazyLoad';

interface OptimizedImageProps {
  src: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  loading?: 'lazy' | 'eager';
  priority?: boolean;
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  className = '',
  loading = 'lazy',
  priority = false
}: OptimizedImageProps) {
  const [isLoaded, setIsLoaded] = useState(false);
  const [hasError, setHasError] = useState(false);
  const imgRef = useRef<HTMLImageElement>(null);

  const handleLoad = useCallback(() => {
    setIsLoaded(true);
  }, []);

  const handleError = useCallback(() => {
    setHasError(true);
  }, []);

  // Create optimized src with WebP fallback
  const optimizedSrc = src.includes('cloudinary') 
    ? `${src.replace('/upload/', '/upload/f_auto,q_auto/')}`
    : src;

  const imageElement = (
    <div className={`relative overflow-hidden ${className}`}>
      {!isLoaded && !hasError && (
        <div 
          className="absolute inset-0 bg-gray-200 dark:bg-gray-700 animate-pulse rounded"
          style={{ aspectRatio: width && height ? `${width}/${height}` : undefined }}
        />
      )}
      
      {hasError ? (
        <div 
          className="flex items-center justify-center bg-gray-100 dark:bg-gray-800 text-gray-400 rounded"
          style={{ 
            width: width || '100%', 
            height: height || 'auto',
            aspectRatio: width && height ? `${width}/${height}` : undefined
          }}
        >
          <svg className="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
          </svg>
        </div>
      ) : (
        <img
          ref={imgRef}
          src={optimizedSrc}
          alt={alt}
          width={width}
          height={height}
          loading={priority ? 'eager' : loading}
          onLoad={handleLoad}
          onError={handleError}
          className={`transition-opacity duration-300 ${
            isLoaded ? 'opacity-100' : 'opacity-0'
          } ${className}`}
          style={{
            aspectRatio: width && height ? `${width}/${height}` : undefined
          }}
        />
      )}
    </div>
  );

  // Use eager loading for priority images
  if (priority || loading === 'eager') {
    return imageElement;
  }

  // Use lazy loading for non-priority images
  return (
    <LazyLoad fallback={
      <div 
        className={`bg-gray-200 dark:bg-gray-700 rounded ${className}`}
        style={{ 
          width: width || '100%', 
          height: height || 200,
          aspectRatio: width && height ? `${width}/${height}` : undefined
        }}
      />
    }>
      {imageElement}
    </LazyLoad>
  );
}