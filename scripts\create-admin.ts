/**
 * <PERSON><PERSON><PERSON> to create an admin user for AgentFactoryPro
 * 
 * Usage:
 * 1. Edit the admin credentials below (email, password, name)
 * 2. Run: npm run create-admin
 * 3. Login and change the password immediately
 * 
 * Security: Delete this file after creating your admin user
 */

import bcrypt from 'bcrypt';
import postgres from 'postgres';

// Get database URL from environment
const DATABASE_URL = process.env.DATABASE_URL || process.env.REPLIT_DB_URL;

if (!DATABASE_URL) {
  console.error('No database URL found. Set DATABASE_URL or REPLIT_DB_URL');
  process.exit(1);
}

const sql = postgres(DATABASE_URL, {
  ssl: DATABASE_URL.includes('localhost') ? false : 'require',
});

async function createAdminUser() {
  try {
    // You can change these values
    const adminEmail = '<EMAIL>';
    const adminPassword = 'changeme123!'; // CHANGE THIS!
    const firstName = 'Admin';
    const lastName = 'User';
    
    // Check if user already exists
    const existing = await sql`
      SELECT id FROM users WHERE email = ${adminEmail}
    `;
    
    if (existing.length > 0) {
      console.log('Admin user already exists');
      process.exit(0);
    }
    
    // Hash the password
    const hashedPassword = await bcrypt.hash(adminPassword, 12);
    
    // Create the admin user
    const [user] = await sql`
      INSERT INTO users (
        email, 
        password, 
        first_name, 
        last_name, 
        full_name,
        role,
        is_active
      )
      VALUES (
        ${adminEmail},
        ${hashedPassword},
        ${firstName},
        ${lastName},
        ${firstName + ' ' + lastName},
        'super_admin',
        true
      )
      RETURNING id, email, role
    `;
    
    console.log('Admin user created successfully:');
    console.log(`Email: ${user.email}`);
    console.log(`Role: ${user.role}`);
    console.log(`ID: ${user.id}`);
    console.log('\nIMPORTANT: Change the password after first login!');
    
  } catch (error) {
    console.error('Error creating admin user:', error);
  } finally {
    await sql.end();
  }
}

createAdminUser(); 