import { AdvancedImage } from '@cloudinary/react';
import { cloudinary } from '@/lib/cloudinary';
import { auto } from '@cloudinary/url-gen/actions/resize';
import { quality } from '@cloudinary/url-gen/actions/delivery';
import { cn } from '@/lib/utils';

interface CloudinaryImageProps {
  publicId: string;
  alt: string;
  width?: number;
  height?: number;
  className?: string;
  priority?: boolean;
}

export function CloudinaryImage({ 
  publicId, 
  alt, 
  width, 
  height, 
  className,
  priority = false 
}: CloudinaryImageProps) {
  const image = cloudinary.image(publicId);
  
  // Apply optimizations
  image.delivery(quality('auto'));
  
  if (width && height) {
    image.resize(auto().width(width).height(height));
  } else if (width) {
    image.resize(auto().width(width));
  } else if (height) {
    image.resize(auto().height(height));
  }

  return (
    <AdvancedImage
      cldImg={image}
      alt={alt}
      className={cn("object-cover", className)}
      loading={priority ? "eager" : "lazy"}
    />
  );
}