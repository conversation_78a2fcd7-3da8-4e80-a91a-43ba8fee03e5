import { useQuery } from "@tanstack/react-query";
import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Separator } from "@/components/ui/separator";
import { CloudinaryImage } from "@/components/ui/cloudinary-image";
import { Link } from "wouter";
import { 
  Calendar, 
  Clock, 
  ArrowLeft, 
  Twitter, 
  Linkedin, 
  Facebook, 
  Share2,
  User,
  List,
  ChevronRight,
  ExternalLink
} from "lucide-react";
import type { BlogPost as BlogPostType } from "@shared/schema";

// SEO Head component
const SEOHead = ({ post }: { post: BlogPostType }) => {
  useEffect(() => {
    // Set page title
    document.title = post.metaTitle || `${post.title} | Agent Factory Blog`;
    
    // Set meta description
    const metaDescription = post.metaDescription || post.excerpt || 
      post.content.replace(/<[^>]*>/g, '').substring(0, 160);
    
    let metaDescTag = document.querySelector('meta[name="description"]');
    if (!metaDescTag) {
      metaDescTag = document.createElement('meta');
      metaDescTag.setAttribute('name', 'description');
      document.head.appendChild(metaDescTag);
    }
    metaDescTag.setAttribute('content', metaDescription);

    // Set Open Graph tags
    const currentUrl = window.location.href;
    const ogImage = post.featuredImageUrl ? 
      `https://res.cloudinary.com/${import.meta.env.VITE_CLOUDINARY_CLOUD_NAME}/image/upload/w_1200,h_630,c_fill/${post.featuredImageUrl}` :
      `${window.location.origin}/og-default.jpg`;

    const ogTags = [
      { property: 'og:title', content: post.title },
      { property: 'og:description', content: metaDescription },
      { property: 'og:image', content: ogImage },
      { property: 'og:url', content: currentUrl },
      { property: 'og:type', content: 'article' },
      { property: 'og:site_name', content: 'Agent Factory' },
      { property: 'article:published_time', content: new Date(post.publishedAt || post.createdAt).toISOString() },
      { property: 'article:author', content: 'Agent Factory Team' },
      { name: 'twitter:card', content: 'summary_large_image' },
      { name: 'twitter:title', content: post.title },
      { name: 'twitter:description', content: metaDescription },
      { name: 'twitter:image', content: ogImage },
    ];

    ogTags.forEach(tag => {
      let metaTag = document.querySelector(`meta[${tag.property ? 'property' : 'name'}="${tag.property || tag.name}"]`);
      if (!metaTag) {
        metaTag = document.createElement('meta');
        if (tag.property) {
          metaTag.setAttribute('property', tag.property);
        } else {
          metaTag.setAttribute('name', tag.name!);
        }
        document.head.appendChild(metaTag);
      }
      metaTag.setAttribute('content', tag.content);
    });

    // Set canonical URL
    let canonicalLink = document.querySelector('link[rel="canonical"]');
    if (!canonicalLink) {
      canonicalLink = document.createElement('link');
      canonicalLink.setAttribute('rel', 'canonical');
      document.head.appendChild(canonicalLink);
    }
    canonicalLink.setAttribute('href', currentUrl);

    // Add JSON-LD structured data
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "BlogPosting",
      "headline": post.title,
      "description": metaDescription,
      "image": ogImage,
      "url": currentUrl,
      "datePublished": new Date(post.publishedAt || post.createdAt).toISOString(),
      "dateModified": post.updatedAt ? new Date(post.updatedAt).toISOString() : new Date(post.createdAt).toISOString(),
      "author": {
        "@type": "Organization",
        "name": "Agent Factory",
        "url": window.location.origin
      },
      "publisher": {
        "@type": "Organization",
        "name": "Agent Factory",
        "logo": {
          "@type": "ImageObject",
          "url": `${window.location.origin}/logo.png`
        }
      },
      "mainEntityOfPage": {
        "@type": "WebPage",
        "@id": currentUrl
      }
    };

    let jsonLdScript = document.querySelector('script[type="application/ld+json"]');
    if (!jsonLdScript) {
      jsonLdScript = document.createElement('script');
      jsonLdScript.setAttribute('type', 'application/ld+json');
      document.head.appendChild(jsonLdScript);
    }
    jsonLdScript.textContent = JSON.stringify(structuredData);

    return () => {
      // Cleanup on unmount
      document.title = 'Agent Factory';
    };
  }, [post]);

  return null;
};

// Table of Contents component
const TableOfContents = ({ content }: { content: string }) => {
  const [headings, setHeadings] = useState<Array<{ id: string; text: string; level: number }>>([]);
  const [activeHeading, setActiveHeading] = useState<string>('');

  useEffect(() => {
    // Extract headings from content safely using DOMParser
    const parser = new DOMParser();
    const doc = parser.parseFromString(content, 'text/html');
    const headingElements = doc.querySelectorAll('h1, h2, h3, h4, h5, h6');
    
    const headingsList = Array.from(headingElements).map((heading, index) => {
      const id = `heading-${index}`;
      const level = parseInt(heading.tagName.charAt(1));
      const text = heading.textContent || '';
      return { id, text, level };
    });

    setHeadings(headingsList);

    // Add IDs to actual headings in the rendered content
    setTimeout(() => {
      const actualHeadings = document.querySelectorAll('.blog-content h1, .blog-content h2, .blog-content h3, .blog-content h4, .blog-content h5, .blog-content h6');
      actualHeadings.forEach((heading, index) => {
        heading.id = `heading-${index}`;
      });
    }, 100);

    // Set up intersection observer for active heading
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            setActiveHeading(entry.target.id);
          }
        });
      },
      { rootMargin: '-20% 0% -35% 0%' }
    );

    setTimeout(() => {
      const headingElements = document.querySelectorAll('.blog-content h1, .blog-content h2, .blog-content h3, .blog-content h4, .blog-content h5, .blog-content h6');
      headingElements.forEach((el) => observer.observe(el));
    }, 200);

    return () => observer.disconnect();
  }, [content]);

  if (headings.length === 0) return null;

  return (
    <Card className="sticky top-24">
      <CardHeader>
        <CardTitle className="flex items-center gap-2 text-lg">
          <List className="h-5 w-5" />
          Table of Contents
        </CardTitle>
      </CardHeader>
      <CardContent>
        <nav className="space-y-2">
          {headings.map((heading) => (
            <a
              key={heading.id}
              href={`#${heading.id}`}
              className={`block text-sm transition-colors hover:text-primary ${
                activeHeading === heading.id ? 'text-primary font-medium' : 'text-muted-foreground'
              }`}
              style={{ paddingLeft: `${(heading.level - 1) * 12}px` }}
              onClick={(e) => {
                e.preventDefault();
                document.getElementById(heading.id)?.scrollIntoView({ behavior: 'smooth' });
              }}
            >
              {heading.text}
            </a>
          ))}
        </nav>
      </CardContent>
    </Card>
  );
};

// Social sharing component
const SocialShareButtons = ({ post }: { post: BlogPostType }) => {
  const url = window.location.href;
  const title = encodeURIComponent(post.title);
  const encodedUrl = encodeURIComponent(url);

  return (
    <div className="flex items-center gap-4 p-4 bg-muted rounded-lg">
      <span className="text-sm font-medium">Share this article:</span>
      <div className="flex gap-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open(`https://twitter.com/intent/tweet?text=${title}&url=${encodedUrl}`, '_blank')}
        >
          <Twitter className="h-4 w-4 mr-2" />
          Twitter
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open(`https://linkedin.com/sharing/share-offsite/?url=${encodedUrl}`, '_blank')}
        >
          <Linkedin className="h-4 w-4 mr-2" />
          LinkedIn
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => window.open(`https://facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank')}
        >
          <Facebook className="h-4 w-4 mr-2" />
          Facebook
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => navigator.share?.({ title: post.title, url })}
        >
          <Share2 className="h-4 w-4 mr-2" />
          Share
        </Button>
      </div>
    </div>
  );
};

// Author bio component
const AuthorBio = () => {
  return (
    <Card>
      <CardContent className="p-6">
        <div className="flex items-start gap-4">
          <Avatar className="h-16 w-16">
            <AvatarImage src="/author-avatar.jpg" alt="Agent Factory Team" />
            <AvatarFallback>
              <User className="h-8 w-8" />
            </AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <h3 className="text-lg font-semibold">Agent Factory Team</h3>
            <p className="text-sm text-muted-foreground mb-3">
              Our team of automation experts and AI specialists brings years of experience in 
              business process optimization, helping companies transform their operations with 
              cutting-edge technology solutions.
            </p>
            <div className="flex gap-2">
              <Button variant="outline" size="sm">
                <ExternalLink className="h-4 w-4 mr-2" />
                View Profile
              </Button>
              <Button variant="outline" size="sm">
                <Twitter className="h-4 w-4 mr-2" />
                Follow
              </Button>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

// Related posts component
const RelatedPosts = ({ currentPost }: { currentPost: BlogPostType }) => {
  const { data: posts } = useQuery<BlogPostType[]>({
    queryKey: ["/api/blog/posts"],
  });

  const relatedPosts = posts?.filter(post => 
    post.id !== currentPost.id && post.isPublished
  ).slice(0, 3) || [];

  if (relatedPosts.length === 0) return null;

  return (
    <div className="space-y-6">
      <h2 className="text-2xl font-bold">Related Articles</h2>
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {relatedPosts.map((post) => (
          <Card key={post.id} className="group hover:shadow-lg transition-shadow">
            {post.featuredImageUrl && (
              <div className="relative h-40 overflow-hidden rounded-t-lg">
                <CloudinaryImage
                  publicId={post.featuredImageUrl}
                  alt={post.title}
                  width={300}
                  height={160}
                  className="object-cover w-full h-full group-hover:scale-105 transition-transform"
                />
              </div>
            )}
            <CardContent className="p-4">
              <div className="space-y-2">
                <h3 className="font-semibold line-clamp-2 group-hover:text-primary transition-colors">
                  {post.title}
                </h3>
                <p className="text-sm text-muted-foreground line-clamp-2">
                  {post.excerpt || post.content.replace(/<[^>]*>/g, '').substring(0, 100) + "..."}
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  {new Date(post.createdAt).toLocaleDateString()}
                </div>
              </div>
              <Link href={`/blog/${post.slug}`}>
                <Button variant="ghost" size="sm" className="w-full mt-3 group-hover:bg-primary group-hover:text-primary-foreground">
                  Read Article
                  <ChevronRight className="h-4 w-4 ml-2" />
                </Button>
              </Link>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

// Calculate read time
const calculateReadTime = (content: string): number => {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
};

interface BlogPostProps {
  post: BlogPostType;
}

export default function BlogPost({ post }: BlogPostProps) {
  return (
    <>
      <SEOHead post={post} />
      
      <div className="min-h-screen bg-[hsl(var(--background))] relative">
        <div className="absolute inset-0 bg-cover bg-center bg-no-repeat opacity-[0.03]" 
             style={{ backgroundImage: "url('/attached_assets/AgentFactoryBG.png')" }}>
        </div>
        <div className="relative z-10 max-w-7xl mx-auto px-4 py-12 animate-in fade-in duration-300">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main content */}
          <div className="lg:col-span-3 space-y-8">
            {/* Back navigation */}
            <Link href="/blog">
              <Button variant="ghost" className="mb-6 text-white hover:text-[hsl(var(--accent-cyan))] hover:bg-[hsl(var(--secondary-dark))] hover:bg-opacity-30">
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Blog
              </Button>
            </Link>

            {/* Article header */}
            <div className="space-y-6">
              {post.featuredImageUrl && (
                <div className="relative h-96 rounded-lg overflow-hidden">
                  <CloudinaryImage
                    publicId={post.featuredImageUrl}
                    alt={post.title}
                    width={800}
                    height={400}
                    className="object-cover w-full h-full"
                  />
                </div>
              )}

              <div className="space-y-4">
                <div className="flex items-center gap-4 text-sm text-muted-foreground">
                  <Badge variant="secondary">AI & Automation</Badge>
                  <span className="flex items-center gap-1">
                    <Calendar className="h-4 w-4" />
                    {new Date(post.publishedAt || post.createdAt).toLocaleDateString()}
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    {calculateReadTime(post.content)} min read
                  </span>
                </div>

                <h1 className="text-4xl font-bold leading-tight text-white">{post.title}</h1>
                
                {post.excerpt && (
                  <p className="text-xl text-muted-foreground leading-relaxed">
                    {post.excerpt}
                  </p>
                )}
              </div>
            </div>

            <Separator />

            {/* Article content */}
            <div 
              className="blog-content prose prose-lg max-w-none dark:prose-invert"
              dangerouslySetInnerHTML={{ __html: post.content }}
            />

            <Separator />

            {/* Social sharing */}
            <SocialShareButtons post={post} />

            <Separator />

            {/* Book a Demo CTA */}
            <Card className="glass-card bg-[hsl(var(--secondary-dark))] bg-opacity-30 border-[hsl(var(--accent-cyan))] border-opacity-30">
              <CardContent className="p-8 text-center space-y-4">
                <h3 className="text-2xl font-bold text-white">Ready to Transform Your Operations?</h3>
                <p className="text-[hsl(var(--text-light))] text-lg">
                  See how our AI agents can automate your business processes and eliminate operational headaches.
                </p>
                <Link href="/booking">
                  <Button className="btn-primary px-8 py-3 text-lg">
                    Book a Free Demo
                  </Button>
                </Link>
              </CardContent>
            </Card>

            <Separator />

            {/* Author bio */}
            <AuthorBio />

            <Separator />

            {/* Related posts */}
            <RelatedPosts currentPost={post} />
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="sticky top-24 space-y-6">
              <TableOfContents content={post.content} />
            </div>
          </div>
        </div>
        </div>
      </div>
    </>
  );
}