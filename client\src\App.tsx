import { Switch, Route } from "wouter";
import { Suspense, lazy, useEffect } from "react";
import { queryClient } from "./lib/queryClient";
import { QueryClientProvider } from "@tanstack/react-query";
import { Toaster } from "@/components/ui/toaster";
import { TooltipProvider } from "@/components/ui/tooltip";
import { AuthProvider } from "@/components/auth/AuthProvider";
import { TenantProvider } from "@/components/TenantProvider";
import { SEOProvider } from "@/components/SEOProvider";
import { LazyLoad } from "@/components/LazyLoad";
import { PerformanceMonitor } from "@/components/PerformanceMonitor";
// Background image served from public directory

// Critical pages loaded immediately
import HomePage from "@/pages/HomePage";
import NotFound from "@/pages/not-found";

// Non-critical pages loaded lazily
const LoginPage = lazy(() => import("@/pages/auth/LoginPage"));
const RegisterPage = lazy(() => import("@/pages/auth/RegisterPage"));
const AuthCallbackPage = lazy(() => import("@/pages/auth/AuthCallbackPage"));
const BookingPage = lazy(() => import("@/pages/BookingPage"));

// Blog section - highly optimized code splitting
const BlogPage = lazy(() => import("@/pages/blog/BlogPage"));
const BlogPostPage = lazy(() => import("@/pages/blog/BlogPostPage"));

// Blog components - granular code splitting for maximum performance
const BlogList = lazy(() => import("@/components/blog/BlogList"));
const BlogPost = lazy(() => import("@/components/blog/BlogPost"));

// Admin pages - import directly for faster loading
import DashboardPage from "@/pages/admin/DashboardPage";
const BlogAdminPage = lazy(() => import("@/pages/admin/BlogAdminPage"));
const UserAdminPage = lazy(() => import("@/pages/admin/UserAdminPage"));
const WorkspacesAdminPage = lazy(() => import("@/pages/admin/WorkspacesAdminPage"));
const SettingsPage = lazy(() => import("@/pages/admin/SettingsPage"));

// Profile and demo pages
const ProfilePage = lazy(() => import("@/pages/ProfilePage"));
const CloudinaryDemo = lazy(() => import("@/pages/CloudinaryDemo"));
const ROICalculatorPage = lazy(() => import("@/pages/ROICalculatorPage"));

// Footer pages
const PrivacyPage = lazy(() => import("@/pages/PrivacyPage"));
const TermsPage = lazy(() => import("@/pages/TermsPage"));
const ContactPage = lazy(() => import("@/pages/ContactPage"));

// Optimized loading components for different route types
function PageLoader({ type = "default" }: { type?: "default" | "admin" | "auth" | "blog" }) {
  const skeletonClass = "animate-pulse bg-gray-200 dark:bg-gray-700 rounded";
  
  if (type === "blog") {
    return (
      <div className="min-h-screen bg-background">
        <div className="max-w-7xl mx-auto px-4 py-12 space-y-8">
          {/* Minimal loading indicator */}
          <div className="text-center">
            <div className="inline-flex items-center gap-2 text-muted-foreground">
              <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
              Loading blog...
            </div>
          </div>
        </div>
      </div>
    );
  }
  
  if (type === "admin") {
    return (
      <div className="min-h-screen bg-background p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          <div className={`h-8 w-64 ${skeletonClass}`} />
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className={`h-32 ${skeletonClass}`} />
            <div className={`h-32 ${skeletonClass}`} />
            <div className={`h-32 ${skeletonClass}`} />
          </div>
          <div className={`h-96 ${skeletonClass}`} />
        </div>
      </div>
    );
  }
  
  if (type === "auth") {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="w-full max-w-md space-y-6 p-6">
          <div className={`h-8 w-48 mx-auto ${skeletonClass}`} />
          <div className="space-y-4">
            <div className={`h-12 ${skeletonClass}`} />
            <div className={`h-12 ${skeletonClass}`} />
            <div className={`h-12 ${skeletonClass}`} />
          </div>
        </div>
      </div>
    );
  }
  
  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-6 py-12 space-y-8">
        <div className={`h-12 w-96 mx-auto ${skeletonClass}`} />
        <div className={`h-64 ${skeletonClass}`} />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className={`h-48 ${skeletonClass}`} />
          <div className={`h-48 ${skeletonClass}`} />
        </div>
      </div>
    </div>
  );
}

// Wrapper component for lazy-loaded routes with error boundaries
function LazyRoute({ 
  component: Component, 
  loaderType = "default",
  ...props 
}: { 
  component: React.ComponentType<any>;
  loaderType?: "default" | "admin" | "auth" | "blog";
}) {
  return (
    <Suspense fallback={<PageLoader type={loaderType} />}>
      <Component {...props} />
    </Suspense>
  );
}

function Router() {
  return (
    <Switch>
      {/* Critical route - loaded immediately */}
      <Route path="/" component={HomePage} />
      
      {/* Authentication routes */}
      <Route path="/login">
        {(params) => <LazyRoute component={LoginPage} loaderType="auth" {...params} />}
      </Route>
      <Route path="/register">
        {(params) => <LazyRoute component={RegisterPage} loaderType="auth" {...params} />}
      </Route>
      <Route path="/auth/callback">
        {(params) => <LazyRoute component={AuthCallbackPage} loaderType="auth" {...params} />}
      </Route>
      
      {/* Content routes - blog optimized with dedicated loading states */}
      <Route path="/blog">
        {(params) => <LazyRoute component={BlogPage} loaderType="blog" {...params} />}
      </Route>
      <Route path="/blog/:slug">
        {(params) => <LazyRoute component={BlogPostPage} loaderType="blog" {...params} />}
      </Route>
      <Route path="/booking">
        {(params) => <LazyRoute component={BookingPage} loaderType="blog" {...params} />}
      </Route>
      <Route path="/roi-calculator">
        {(params) => <LazyRoute component={ROICalculatorPage} loaderType="default" {...params} />}
      </Route>
      
      {/* Admin routes - dashboard loads immediately */}
      <Route path="/dashboard" component={DashboardPage} />
      <Route path="/admin/blog">
        {(params) => <LazyRoute component={BlogAdminPage} loaderType="admin" {...params} />}
      </Route>
      <Route path="/admin/users">
        {(params) => <LazyRoute component={UserAdminPage} loaderType="admin" {...params} />}
      </Route>
      <Route path="/admin/workspaces">
        {(params) => <LazyRoute component={WorkspacesAdminPage} loaderType="admin" {...params} />}
      </Route>
      <Route path="/admin/settings">
        {(params) => <LazyRoute component={SettingsPage} loaderType="admin" {...params} />}
      </Route>
      
      {/* Profile and demo routes */}
      <Route path="/profile">
        {(params) => <LazyRoute component={ProfilePage} loaderType="blog" {...params} />}
      </Route>
      <Route path="/cloudinary-demo">
        {(params) => <LazyRoute component={CloudinaryDemo} loaderType="blog" {...params} />}
      </Route>
      
      {/* Footer pages */}
      <Route path="/privacy">
        {(params) => <LazyRoute component={PrivacyPage} loaderType="default" {...params} />}
      </Route>
      <Route path="/terms">
        {(params) => <LazyRoute component={TermsPage} loaderType="default" {...params} />}
      </Route>
      <Route path="/contact">
        {(params) => <LazyRoute component={ContactPage} loaderType="default" {...params} />}
      </Route>
      
      {/* 404 - loaded immediately for UX */}
      <Route component={NotFound} />
    </Switch>
  );
}

function App() {
  return (
    <div className="min-h-screen">
      <QueryClientProvider client={queryClient}>
        <SEOProvider>
          <TooltipProvider>
            <AuthProvider>
              <TenantProvider>
                <Toaster />
                <Router />
                <PerformanceMonitor />
              </TenantProvider>
            </AuthProvider>
          </TooltipProvider>
        </SEOProvider>
      </QueryClientProvider>
    </div>
  );
}

export default App;
