import { useState, useEffect } from 'react';
import { 
  Cog, 
  TrendingUp, 
  Factory, 
  Clock, 
  LucideIcon 
} from 'lucide-react';

interface RotatingTextProps {
  baseText: string;
  className?: string;
  mode?: 'default' | 'roi';
}

const phrases: Array<{
  text: string;
  icon: LucideIcon | null;
  color: string;
  special?: boolean;
}> = [
  { text: "Smart Operations", icon: Cog, color: "text-blue-400" },
  { text: "Competitive Advantage", icon: TrendingUp, color: "text-green-400" },
  { text: "Scaling Operations", icon: Factory, color: "text-purple-400" },
  { text: "Busy Professionals", icon: Clock, color: "text-orange-400" },
  { text: "YOU!", icon: null, color: "text-[hsl(var(--accent-cyan))]", special: true }
];

const roiPhrases: Array<{
  text: string;
  icon: LucideIcon | null;
  color: string;
  special?: boolean;
}> = [
  { text: "your industry", icon: Factory, color: "text-blue-400" },
  { text: "your processes", icon: Cog, color: "text-green-400" },
  { text: "your potential", icon: TrendingUp, color: "text-purple-400" },
  { text: "your numbers", icon: Clock, color: "text-orange-400" },
  { text: "YOU!", icon: null, color: "text-[hsl(var(--accent-cyan))]", special: true }
];

export function RotatingText({ baseText, className = "", mode = 'default' }: RotatingTextProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);
  const [stopped, setStopped] = useState(false);

  const activePhrases = mode === 'roi' ? roiPhrases : phrases;

  useEffect(() => {
    if (stopped) {
      // Restart after 10 seconds on "YOU!"
      const restartTimeout = setTimeout(() => {
        setIsVisible(false);
        
        setTimeout(() => {
          setStopped(false);
          setCurrentIndex(0);
          setIsVisible(true);
        }, 500);
      }, 10000);
      
      return () => clearTimeout(restartTimeout);
    }
    
    const interval = setInterval(() => {
      setIsVisible(false);
      
      setTimeout(() => {
        const nextIndex = (currentIndex + 1) % activePhrases.length;
        setCurrentIndex(nextIndex);
        
        // Stop on "YOU!" (last phrase)
        if (nextIndex === activePhrases.length - 1) {
          setStopped(true);
        }
        
        setIsVisible(true);
      }, 500); // Half of transition duration
      
    }, 4000); // Show each phrase for 4 seconds

    return () => clearInterval(interval);
  }, [currentIndex, stopped, activePhrases]);

  const currentPhrase = activePhrases[currentIndex];
  const IconComponent = currentPhrase.icon;

  return (
    <div className={`flex flex-col items-center ${className}`}>
      <div className="ml-6">
        <span>{baseText}...</span>
      </div>
      <div className="relative w-full h-8 flex items-center justify-center mt-2">
        <div 
          className={`
            absolute flex items-center justify-center gap-2 transition-opacity duration-1000 ease-in-out
            ${isVisible ? 'opacity-100' : 'opacity-0'}
          `}
          style={{ left: '50%', transform: 'translateX(-50%)' }}
        >
          <span 
            className={`
              font-bold whitespace-nowrap
              ${currentPhrase.color}
              ${currentPhrase.special ? 
                'text-2xl font-extrabold' + 
                ' bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent'
                : 'text-xl'
              }
            `}
          >
            {currentPhrase.text}
          </span>
          {IconComponent && (
            <IconComponent 
              className={`
                w-6 h-6
                ${currentPhrase.color}
              `}
            />
          )}
        </div>
      </div>
    </div>
  );
}