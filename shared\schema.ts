import { pgTable, text, serial, integer, boolean, timestamp, numeric, date, pgEnum } from "drizzle-orm/pg-core";
import { createInsertSchema, createSelectSchema } from "drizzle-zod";
import { z } from "zod";
import { relations } from "drizzle-orm";

// User roles enum for database and Zod - platform-level roles only
export const userRoles = ["lead", "customer", "support", "billing_admin", "sales", "blog_admin", "super_admin"] as const;
export type UserRole = typeof userRoles[number];
export const userRoleEnum = pgEnum("user_role_enum", userRoles); // Drizzle pgEnum

// Tenant status enum
export const tenantStatuses = ["active", "suspended", "trial", "cancelled"] as const;
export type TenantStatus = typeof tenantStatuses[number];
export const tenantStatusEnum = pgEnum("tenant_status_enum", tenantStatuses);

// Workspace status enum
export const workspaceStatuses = ["active", "inactive", "archived", "error"] as const;
export type WorkspaceStatus = typeof workspaceStatuses[number];
export const workspaceStatusEnum = pgEnum("workspace_status_enum", workspaceStatuses);

// Plan configurations table - admin configurable plans
export const planConfigs = pgTable("plan_configs", {
  id: serial("id").primaryKey(),
  name: text("name").notNull().unique(), // e.g., "Starter", "Pro", "Enterprise", "Custom-50"
  displayName: text("display_name").notNull(), // e.g., "Professional Plan"
  description: text("description"), // Plan description
  maxUsers: integer("max_users").notNull(), // -1 for unlimited
  monthlyPrice: integer("monthly_price").default(0), // Price in cents
  yearlyPrice: integer("yearly_price").default(0), // Price in cents  
  features: text("features").default("[]"), // JSON array of features
  isActive: boolean("is_active").default(true).notNull(),
  isCustom: boolean("is_custom").default(false).notNull(), // Custom plans for specific customers
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().$onUpdate(() => new Date()),
});

// Tenants/Organizations table
export const tenants = pgTable("tenants", {
  id: serial("id").primaryKey(),
  name: text("name").notNull(),
  slug: text("slug").notNull().unique(), // For subdomain
  domain: text("domain").unique(), // Custom domain
  status: tenantStatusEnum("status").default("trial").notNull(),
  planConfigId: integer("plan_config_id").references(() => planConfigs.id), // Reference to plan config
  customMaxUsers: integer("custom_max_users"), // Override for custom limits (null = use plan default)
  settings: text("settings").default("{}"), // JSON settings as text
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().$onUpdate(() => new Date()),
});

// Users table
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  externalId: text("external_id").unique(), // For Supabase auth user ID
  email: text("email").notNull().unique(),
  firstName: text("first_name"),
  lastName: text("last_name"),
  businessName: text("business_name"),
  role: userRoleEnum("role").default("lead").notNull(), // Default new signups are leads
  isActive: boolean("is_active").default(true).notNull(),
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().$onUpdate(() => new Date()),
});

// Tenant memberships table (many-to-many)
export const tenantMemberships = pgTable("tenant_memberships", {
  id: serial("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id, { onDelete: "cascade" }),
  tenantId: integer("tenant_id").notNull().references(() => tenants.id, { onDelete: "cascade" }),
  role: text("role").default("member"), // owner, admin, member
  isActive: boolean("is_active").default(true).notNull(),
  joinedAt: timestamp("joined_at", { withTimezone: true }).defaultNow().notNull(),
});

// Blog posts table (tenant-aware)
export const blogPosts = pgTable("blog_posts", {
  id: serial("id").primaryKey(),
  tenantId: integer("tenant_id").references(() => tenants.id), // NULL for global posts
  title: text("title").notNull(),
  slug: text("slug").notNull(),
  content: text("content").notNull(),
  excerpt: text("excerpt"),
  featuredImageUrl: text("featured_image_url"),
  metaTitle: text("meta_title"),
  metaDescription: text("meta_description"),
  isPublished: boolean("is_published").notNull().default(false),
  authorId: integer("author_id").references(() => users.id),
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).notNull().defaultNow(),
  publishedAt: timestamp("published_at", { withTimezone: true }),
});

// Invoices table (tenant-aware)
export const invoices = pgTable("invoices", {
  id: serial("id").primaryKey(),
  tenantId: integer("tenant_id").references(() => tenants.id), // NULL for platform invoices
  userId: integer("user_id").references(() => users.id),
  amount: numeric("amount", { precision: 10, scale: 2 }).notNull(),
  status: text("status").notNull(),
  dueDate: date("due_date"),
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
});

// User sessions table for authentication
export const userSessions = pgTable("user_sessions", {
  id: text("id").primaryKey(),
  userId: integer("user_id").notNull().references(() => users.id),
  expiresAt: timestamp("expires_at", { withTimezone: true }).notNull(),
  createdAt: timestamp("created_at", { withTimezone: true }).notNull().defaultNow(),
});

// Zod schemas for validation
const selectUserSchema = createSelectSchema(users);
const insertUserBaseSchema = createInsertSchema(users);
const selectTenantSchema = createSelectSchema(tenants);
const insertTenantBaseSchema = createInsertSchema(tenants);
const selectPlanConfigSchema = createSelectSchema(planConfigs);
const insertPlanConfigBaseSchema = createInsertSchema(planConfigs);
const selectTenantMembershipSchema = createSelectSchema(tenantMemberships);
const insertTenantMembershipBaseSchema = createInsertSchema(tenantMemberships);
const selectBlogPostSchema = createSelectSchema(blogPosts);
const insertBlogPostBaseSchema = createInsertSchema(blogPosts);
const selectInvoiceSchema = createSelectSchema(invoices);
const insertInvoiceBaseSchema = createInsertSchema(invoices);

export const insertUserSchema = insertUserBaseSchema
  .extend({
    email: z.string().email("Invalid email address"),
    role: z.enum(userRoles).default("lead"),
    businessName: z.string().nullable().optional(),
  })
  .omit({
    id: true,
    isActive: true,
    createdAt: true,
    updatedAt: true,
  });

export const insertUserWithPasswordSchema = insertUserSchema.extend({
  password: z.string().min(8, "Password must be at least 8 characters long"),
});

export const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

export const insertTenantSchema = insertTenantBaseSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  name: z.string().min(1, "Tenant name is required"),
  slug: z.string().min(1, "Slug is required"),
  status: z.enum(tenantStatuses).default("trial"),
  planConfigId: z.number().optional(),
  customMaxUsers: z.number().min(1).optional(), // Custom override
});

export const insertPlanConfigSchema = insertPlanConfigBaseSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  name: z.string().min(1, "Plan name is required"),
  displayName: z.string().min(1, "Display name is required"),
  maxUsers: z.number().min(-1, "Must be -1 (unlimited) or positive number"),
  monthlyPrice: z.number().min(0, "Price must be non-negative").default(0),
  yearlyPrice: z.number().min(0, "Price must be non-negative").default(0),
  features: z.string().default("[]"), // JSON string
});

export const insertTenantMembershipSchema = insertTenantMembershipBaseSchema.omit({
  id: true,
  joinedAt: true,
}).extend({
  userId: z.number(),
  tenantId: z.number(),
  role: z.string().default("member"),
});

export const insertBlogPostSchema = insertBlogPostBaseSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
  publishedAt: true,
}).extend({
  title: z.string().min(1, "Title is required"),
  content: z.string().min(1, "Content is required"),
  slug: z.string().min(1, "Slug is required"),
});

export const updateBlogPostSchema = insertBlogPostSchema.partial();

export const insertInvoiceSchema = insertInvoiceBaseSchema.omit({
  id: true,
  createdAt: true,
}).extend({
  amount: z.string().regex(/^\d+(\.\d{1,2})?$/, "Invalid amount format"),
  status: z.enum(["pending", "paid"]),
});

export const updateUserSchema = selectUserSchema
  .extend({
    email: z.string().email("Invalid email address").optional(),
    role: z.enum(userRoles).optional(),
    businessName: z.string().min(1, "Business Name cannot be empty if provided").nullable().optional(),
    firstName: z.string().nullable().optional(),
    lastName: z.string().nullable().optional(),
    isActive: z.boolean().optional(),
    externalId: z.string().nullable().optional(),
  })
  .omit({
    id: true,
    createdAt: true,
    updatedAt: true,
  });

// Export clean types using the variable-first pattern
export type User = z.infer<typeof selectUserSchema>;
export type InsertUser = z.infer<typeof insertUserSchema>;
export type InsertUserWithPassword = z.infer<typeof insertUserWithPasswordSchema>;
export type LoginCredentials = z.infer<typeof loginSchema>;
export type Tenant = z.infer<typeof selectTenantSchema>;
export type InsertTenant = z.infer<typeof insertTenantSchema>;
export type PlanConfig = z.infer<typeof selectPlanConfigSchema>;
export type InsertPlanConfig = z.infer<typeof insertPlanConfigSchema>;
export type TenantMembership = z.infer<typeof selectTenantMembershipSchema>;
export type InsertTenantMembership = z.infer<typeof insertTenantMembershipSchema>;
export type BlogPost = typeof blogPosts.$inferSelect;
export type InsertBlogPost = z.infer<typeof insertBlogPostSchema>;
export type UpdateBlogPost = z.infer<typeof updateBlogPostSchema>;
export type Invoice = typeof invoices.$inferSelect;
export type InsertInvoice = z.infer<typeof insertInvoiceSchema>;
export type UserSession = typeof userSessions.$inferSelect;
export type UpdateUser = z.infer<typeof updateUserSchema>;
export type Workspace = typeof workspaces.$inferSelect;
export type InsertWorkspace = z.infer<typeof insertWorkspaceSchema>;
export type UpdateWorkspace = z.infer<typeof updateWorkspaceSchema>;

// Workspaces table
export const workspaces = pgTable("workspaces", {
  id: text("id").primaryKey(), // UUID string
  name: text("name").notNull(),
  description: text("description"),
  templateId: text("template_id").notNull(),
  environment: text("environment").notNull(), // development, staging, production
  status: workspaceStatusEnum("status").default("active").notNull(),
  userId: integer("user_id").notNull().references(() => users.id),
  config: text("config").default("{}"), // JSON configuration
  createdAt: timestamp("created_at", { withTimezone: true }).defaultNow().notNull(),
  updatedAt: timestamp("updated_at", { withTimezone: true }).defaultNow().$onUpdate(() => new Date()),
});

export const selectWorkspaceSchema = createSelectSchema(workspaces);
export const insertWorkspaceBaseSchema = createInsertSchema(workspaces);

export const insertWorkspaceSchema = insertWorkspaceBaseSchema.omit({
  id: true,
  createdAt: true,
  updatedAt: true,
}).extend({
  name: z.string().min(1, "Workspace name is required"),
  templateId: z.string().min(1, "Template is required"),
  environment: z.enum(["development", "staging", "production"]),
  status: z.enum(workspaceStatuses).default("active"),
});

export const updateWorkspaceSchema = insertWorkspaceSchema.partial();

export const userRelations = relations(users, ({ many }) => ({
  blogPosts: many(blogPosts, { relationName: 'authorToPosts' }),
  invoices: many(invoices),
  sessions: many(userSessions),
  tenantMemberships: many(tenantMemberships),
  workspaces: many(workspaces),
}));

export const workspaceRelations = relations(workspaces, ({ one }) => ({
  user: one(users, {
    fields: [workspaces.userId],
    references: [users.id],
  }),
}));

export const tenantRelations = relations(tenants, ({ one, many }) => ({
  planConfig: one(planConfigs, { fields: [tenants.planConfigId], references: [planConfigs.id] }),
  memberships: many(tenantMemberships),
  blogPosts: many(blogPosts),
  invoices: many(invoices),
}));

export const planConfigRelations = relations(planConfigs, ({ many }) => ({
  tenants: many(tenants),
}));

export const tenantMembershipRelations = relations(tenantMemberships, ({ one }) => ({
  user: one(users, { fields: [tenantMemberships.userId], references: [users.id] }),
  tenant: one(tenants, { fields: [tenantMemberships.tenantId], references: [tenants.id] }),
}));
