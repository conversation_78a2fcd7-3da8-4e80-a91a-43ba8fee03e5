import { useTenant } from './TenantProvider';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Building2, Globe, AlertCircle } from 'lucide-react';

export function TenantBanner() {
  const { currentTenant, isLoading, error, getDomainInfo } = useTenant();
  const domainInfo = getDomainInfo();

  // Only show on customer subdomains
  if (domainInfo?.type !== 'customer') {
    return null;
  }

  if (isLoading) {
    return (
      <div className="bg-blue-50 border-b border-blue-200 px-4 py-2">
        <div className="flex items-center gap-2 text-sm text-blue-700">
          <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
          Loading tenant information...
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive" className="rounded-none border-0 border-b">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          {error}
        </AlertDescription>
      </Alert>
    );
  }

  if (!currentTenant) {
    return (
      <Alert variant="destructive" className="rounded-none border-0 border-b">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>
          Tenant not found for subdomain: {domainInfo?.subdomain}
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border-b border-blue-200 px-4 py-3">
      <div className="flex items-center justify-between max-w-7xl mx-auto">
        <div className="flex items-center gap-3">
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-blue-600" />
            <span className="font-semibold text-blue-900">{currentTenant.name}</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-blue-700">
            <Globe className="h-4 w-4" />
            <span>{domainInfo.subdomain}.agent-factory.app</span>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge 
            variant={currentTenant.status === 'active' ? 'default' : 'secondary'}
            className="capitalize"
          >
            {currentTenant.status}
          </Badge>
          
          {currentTenant.plan && (
            <Badge variant="outline" className="capitalize">
              {currentTenant.plan} Plan
            </Badge>
          )}
        </div>
      </div>
    </div>
  );
} 