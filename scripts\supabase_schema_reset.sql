-- Supabase Schema and RLS Policies RESET for Agent Factory Pro
-- This script is idempotent and safe to run multiple times.
-- It will drop and recreate all relevant tables, indexes, and policies.

-- 1. Drop Policies (if exist)
drop policy if exists "Users can view their own profile" on profiles;
drop policy if exists "Users can update their own profile" on profiles;
drop policy if exists "Super Admin full access" on profiles;
drop policy if exists "Public can read published posts" on blog_posts;
drop policy if exists "Authors can manage their own posts" on blog_posts;
drop policy if exists "Admins full access" on blog_posts;
drop policy if exists "Users can read their own invoices" on invoices;
drop policy if exists "Admins full access" on invoices;

-- 2. Drop Indexes (if exist)
drop index if exists profiles_email_idx;
drop index if exists blog_posts_slug_idx;

-- 3. Drop Tables (if exist)
drop table if exists invoices cascade;
drop table if exists blog_posts cascade;
drop table if exists profiles cascade;

-- 4. Recreate Tables
create table profiles (
  id uuid primary key references auth.users(id) on delete cascade,
  email text not null,
  full_name text,
  role text not null default 'user',
  is_active boolean not null default true,
  created_at timestamp with time zone default timezone('utc', now()),
  updated_at timestamp with time zone default timezone('utc', now())
);
create index profiles_email_idx on profiles(email);

create table blog_posts (
  id uuid primary key default gen_random_uuid(),
  title text not null,
  slug text not null unique,
  content text not null,
  excerpt text,
  featured_image_url text,
  meta_title text,
  meta_description text,
  is_published boolean not null default false,
  author_id uuid references profiles(id),
  created_at timestamp with time zone default timezone('utc', now()),
  updated_at timestamp with time zone default timezone('utc', now()),
  published_at timestamp with time zone
);
create index blog_posts_slug_idx on blog_posts(slug);

create table invoices (
  id uuid primary key default gen_random_uuid(),
  user_id uuid references profiles(id),
  amount numeric(10,2) not null,
  status text not null check (status in ('pending', 'paid')),
  due_date date,
  created_at timestamp with time zone default timezone('utc', now())
);

-- 5. Enable Row Level Security (RLS)
alter table profiles enable row level security;
alter table blog_posts enable row level security;
alter table invoices enable row level security;

-- 6. Recreate RLS Policies
-- Profiles Table
create policy "Users can view their own profile"
  on profiles for select
  using (auth.uid() = id);

create policy "Users can update their own profile"
  on profiles for update
  using (auth.uid() = id);

create policy "Super Admin full access"
  on profiles for all
  using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role = 'super_admin'
    )
  );

-- Blog Posts Table
create policy "Public can read published posts"
  on blog_posts for select
  using (is_published = true);

create policy "Authors can manage their own posts"
  on blog_posts for all
  using (author_id = auth.uid());

create policy "Admins full access"
  on blog_posts for all
  using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('blog_admin', 'super_admin')
    )
  );

-- Invoices Table
create policy "Users can read their own invoices"
  on invoices for select
  using (user_id = auth.uid());

create policy "Admins full access"
  on invoices for all
  using (
    exists (
      select 1 from profiles p where p.id = auth.uid() and p.role in ('user_admin', 'super_admin')
    )
  ); 