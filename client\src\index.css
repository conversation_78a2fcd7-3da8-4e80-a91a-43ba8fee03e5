@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
  --radius: 0.5rem;
  
  /* Custom Agent Factory colors */
  --primary-dark: 210 70% 8%;
  --secondary-dark: 220 26% 18%;
  --accent-cyan: 194 100% 50%;
  --accent-blue: 199 89% 48%;
  --text-light: 210 20% 89%;
  --neon-glow: 194 100% 50%;
}

/* Agent Factory Background */
body {
  background-image: url('/AgentFactoryBG.png') !important;
  background-size: cover !important;
  background-position: center !important;
  background-attachment: fixed !important;
  background-repeat: no-repeat !important;
  background-color: #1e293b !important;
}

body::before {
  content: '';
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(15, 23, 42, 0.4) !important;
  backdrop-filter: blur(1px);
  z-index: -1;
  pointer-events: none;
}

.dark {
  --background: 240 10% 3.9%;
  --foreground: 0 0% 98%;
  --muted: 240 3.7% 15.9%;
  --muted-foreground: 240 5% 64.9%;
  --popover: 240 10% 3.9%;
  --popover-foreground: 0 0% 98%;
  --card: 240 10% 3.9%;
  --card-foreground: 0 0% 98%;
  --border: 240 3.7% 15.9%;
  --input: 240 3.7% 15.9%;
  --primary: 207 90% 54%;
  --primary-foreground: 211 100% 99%;
  --secondary: 240 3.7% 15.9%;
  --secondary-foreground: 0 0% 98%;
  --accent: 240 3.7% 15.9%;
  --accent-foreground: 0 0% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 0 0% 98%;
  --ring: 240 4.9% 83.9%;
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply font-sans antialiased bg-background text-foreground;
    background: linear-gradient(135deg, hsl(var(--primary-dark)) 0%, hsl(var(--secondary-dark)) 50%, hsl(210 70% 4%) 100%);
    min-height: 100vh;
  }

  html {
    scroll-behavior: smooth;
  }
}

@layer components {
  .gradient-bg {
    background: linear-gradient(135deg, hsl(var(--primary-dark)) 0%, hsl(var(--secondary-dark)) 50%, hsl(210 70% 4%) 100%);
  }
  
  .glass-card {
    background: hsla(var(--secondary-dark), 0.3);
    backdrop-filter: blur(10px);
    border: 1px solid hsla(var(--accent-cyan), 0.3);
    transition: all 0.3s ease;
  }
  
  .glass-card:hover {
    background: hsla(var(--secondary-dark), 0.5);
    border-color: hsla(var(--accent-cyan), 0.6);
    transform: translateY(-5px);
  }
  
  .neon-border {
    border: 1px solid hsl(var(--accent-cyan));
    box-shadow: 0 0 30px 10px hsla(var(--accent-cyan), 0.9), 
                0 0 60px 20px hsla(var(--accent-cyan), 0.7),
                inset 0 0 20px hsla(var(--accent-cyan), 0.2);
  }
  
  .neon-border:hover {
    border: 2px solid hsl(var(--accent-cyan));
    box-shadow: 0 0 40px 15px hsla(var(--accent-cyan), 1), 
                0 0 80px 25px hsla(var(--accent-cyan), 0.8),
                0 0 120px 30px hsla(var(--accent-cyan), 0.6),
                inset 0 0 30px hsla(var(--accent-cyan), 0.3);
  }
  
  .neon-glow {
    text-shadow: 0 0 30px hsla(var(--accent-cyan), 0.9), 
                 0 0 60px hsla(var(--accent-cyan), 0.7);
  }
  
  .hero-text {
    background: linear-gradient(135deg, #FFFFFF 0%, hsl(var(--text-light)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    padding-bottom: 8px;
    overflow: visible;
  }

  .btn-primary {
    @apply bg-transparent text-[hsl(var(--accent-cyan))] px-8 py-4 rounded-lg font-semibold hover:bg-[hsl(var(--accent-cyan))] hover:text-[hsl(var(--primary-dark))] transition-all duration-300 hover:scale-105;
    border: 2px solid hsl(var(--accent-cyan));
    box-shadow: 0 0 30px 10px hsla(var(--accent-cyan), 0.9), 
                0 0 60px 20px hsla(var(--accent-cyan), 0.7),
                inset 0 0 20px hsla(var(--accent-cyan), 0.2);
  }
  
  .btn-primary:hover {
    border: 3px solid hsl(var(--accent-cyan));
    box-shadow: 0 0 40px 15px hsla(var(--accent-cyan), 1), 
                0 0 80px 25px hsla(var(--accent-cyan), 0.8),
                0 0 120px 30px hsla(var(--accent-cyan), 0.6),
                inset 0 0 30px hsla(var(--accent-cyan), 0.3);
  }

  .btn-secondary {
    @apply neon-border bg-transparent px-6 py-3 rounded-lg font-semibold text-[hsl(var(--accent-cyan))] hover:bg-[hsl(var(--accent-cyan))] hover:text-[hsl(var(--primary-dark))] transition-all duration-300;
  }
}

@layer utilities {
  .animate-glow {
    animation: glow 2s ease-in-out infinite alternate;
  }
  
  .animate-float {
    animation: float 3s ease-in-out infinite;
  }
  
  .animate-fade-in {
    animation: fadeIn 0.6s ease-out;
  }
}

@keyframes glow {
  0% { 
    box-shadow: 0 0 30px 10px hsla(var(--accent-cyan), 0.9), 
                0 0 60px 20px hsla(var(--accent-cyan), 0.7),
                inset 0 0 20px hsla(var(--accent-cyan), 0.2);
  }
  50% { 
    box-shadow: 0 0 50px 20px hsla(var(--accent-cyan), 1), 
                0 0 100px 30px hsla(var(--accent-cyan), 0.8),
                0 0 150px 40px hsla(196, 100%, 60%, 0.4),
                inset 0 0 30px hsla(var(--accent-cyan), 0.3);
  }
  100% { 
    box-shadow: 0 0 30px 10px hsla(var(--accent-cyan), 0.9), 
                0 0 60px 20px hsla(var(--accent-cyan), 0.7),
                inset 0 0 20px hsla(var(--accent-cyan), 0.2);
  }
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: translateY(20px); }
  100% { opacity: 1; transform: translateY(0); }
}
