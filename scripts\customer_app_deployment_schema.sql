-- Customer App Deployment Management System Database Extension
-- Run this script AFTER the main supabase_schema.sql to add customer app deployment functionality
-- This extends the existing multi-tenant architecture with customer app deployment management

-- Create deployment status enum
CREATE TYPE deployment_status_enum AS ENUM (
  'pending',      -- Deployment request created, waiting to be processed
  'provisioning', -- Creating Replit app and configuring
  'deploying',    -- Deploying code to the new app
  'active',       -- Successfully deployed and running
  'maintenance',  -- Temporarily down for updates
  'suspended',    -- Paused (payment issues, etc.)
  'failed',       -- Deployment failed
  'archived'      -- Customer cancelled, app preserved for data
);

-- Create deployment template enum
CREATE TYPE deployment_template_enum AS ENUM (
  'agent_basic',     -- Basic AI agent template
  'agent_advanced',  -- Advanced AI agent with custom features
  'tool_calculator', -- ROI Calculator tool template
  'tool_analytics',  -- Analytics dashboard template
  'tool_custom',     -- Custom tool template
  'saas_starter',    -- Basic SaaS application template
  'saas_pro'         -- Professional SaaS application template
);

-- Create customer app deployments table
CREATE TABLE customer_app_deployments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Customer relationship
  customer_user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  tenant_id INTEGER REFERENCES tenants(id) ON DELETE SET NULL, -- Optional: link to tenant if using tenant system
  
  -- Deployment details
  app_name VARCHAR(100) NOT NULL, -- e.g., "John's AI Assistant"
  subdomain VARCHAR(50) NOT NULL UNIQUE, -- e.g., "johns-ai" for johns-ai.agent-factory.app
  template_type deployment_template_enum NOT NULL DEFAULT 'agent_basic',
  status deployment_status_enum NOT NULL DEFAULT 'pending',
  
  -- Replit integration
  replit_url TEXT, -- e.g., "https://replit.com/@username/johns-ai-assistant"
  deployment_url TEXT, -- e.g., "https://johns-ai.agent-factory.app"
  github_repo_url TEXT, -- Source code repository if using GitHub integration
  
  -- Configuration
  custom_config JSONB DEFAULT '{}', -- Store app-specific configuration
  environment_vars JSONB DEFAULT '{}', -- Environment variables for the deployment
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now(),
  deployed_at TIMESTAMPTZ, -- When deployment completed successfully
  last_health_check TIMESTAMPTZ, -- Last time we checked if the app is running
  
  -- Constraints
  CONSTRAINT valid_subdomain CHECK (subdomain ~ '^[a-z0-9-]+$'), -- Only lowercase, numbers, hyphens
  CONSTRAINT subdomain_length CHECK (length(subdomain) BETWEEN 3 AND 50)
);

-- Create deployment templates table (for managing available templates)
CREATE TABLE deployment_templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  
  -- Template details
  name VARCHAR(100) NOT NULL,
  template_type deployment_template_enum NOT NULL UNIQUE,
  description TEXT,
  
  -- Template source
  github_repo_url TEXT NOT NULL, -- Source template repository
  replit_template_url TEXT, -- If we have a Replit template
  
  -- Configuration
  default_config JSONB DEFAULT '{}', -- Default configuration for this template
  required_env_vars TEXT[] DEFAULT '{}', -- List of required environment variables
  
  -- Metadata
  version VARCHAR(20) DEFAULT '1.0.0',
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create deployment logs table (for tracking deployment process)
CREATE TABLE deployment_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  deployment_id UUID NOT NULL REFERENCES customer_app_deployments(id) ON DELETE CASCADE,
  
  -- Log details
  log_level VARCHAR(10) NOT NULL DEFAULT 'info', -- info, warning, error, debug
  message TEXT NOT NULL,
  details JSONB DEFAULT '{}', -- Additional structured data
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT now(),
  created_by INTEGER REFERENCES users(id) -- Who/what created this log entry
);

-- Create customer app access table (for managing who can access which deployed apps)
CREATE TABLE customer_app_access (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  deployment_id UUID NOT NULL REFERENCES customer_app_deployments(id) ON DELETE CASCADE,
  user_id INTEGER NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Access details
  role VARCHAR(20) NOT NULL DEFAULT 'user', -- owner, admin, user, viewer
  granted_at TIMESTAMPTZ DEFAULT now(),
  granted_by INTEGER REFERENCES users(id),
  
  -- Constraints
  UNIQUE(deployment_id, user_id)
);

-- Add indexes for performance
CREATE INDEX idx_deployments_customer ON customer_app_deployments(customer_user_id);
CREATE INDEX idx_deployments_subdomain ON customer_app_deployments(subdomain);
CREATE INDEX idx_deployments_status ON customer_app_deployments(status);
CREATE INDEX idx_deployments_template ON customer_app_deployments(template_type);
CREATE INDEX idx_deployment_logs_deployment ON deployment_logs(deployment_id);
CREATE INDEX idx_deployment_logs_created ON deployment_logs(created_at);
CREATE INDEX idx_app_access_deployment ON customer_app_access(deployment_id);
CREATE INDEX idx_app_access_user ON customer_app_access(user_id);

-- Create updated_at trigger function (if not already exists)
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = now();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Add updated_at triggers
CREATE TRIGGER update_customer_app_deployments_updated_at 
  BEFORE UPDATE ON customer_app_deployments 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_deployment_templates_updated_at 
  BEFORE UPDATE ON deployment_templates 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get the current user's integer ID from their auth UUID
CREATE OR REPLACE FUNCTION get_current_user_id()
RETURNS INTEGER AS $$
DECLARE
  user_id_int INTEGER;
BEGIN
  SELECT id INTO user_id_int FROM public.users WHERE external_id = auth.uid()::text;
  RETURN user_id_int;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if the current user is a super admin
CREATE OR REPLACE FUNCTION is_super_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.users 
    WHERE id = get_current_user_id()
    AND role = 'super_admin'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Row Level Security (RLS) Policies

-- Enable RLS on all tables
ALTER TABLE customer_app_deployments ENABLE ROW LEVEL SECURITY;
ALTER TABLE deployment_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE deployment_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE customer_app_access ENABLE ROW LEVEL SECURITY;

-- Customer App Deployments Policies
CREATE POLICY "Users can view their own deployments" ON customer_app_deployments
  FOR SELECT USING (
    customer_user_id = get_current_user_id() OR
    EXISTS (
      SELECT 1 FROM customer_app_access 
      WHERE deployment_id = customer_app_deployments.id 
      AND user_id = get_current_user_id()
    )
  );

CREATE POLICY "Super admins can view all deployments" ON customer_app_deployments
  FOR SELECT USING (is_super_admin());

CREATE POLICY "Users can create their own deployments" ON customer_app_deployments
  FOR INSERT WITH CHECK (customer_user_id = get_current_user_id());

CREATE POLICY "Users can update their own deployments" ON customer_app_deployments
  FOR UPDATE USING (
    customer_user_id = get_current_user_id() OR
    EXISTS (
      SELECT 1 FROM customer_app_access 
      WHERE deployment_id = customer_app_deployments.id 
      AND user_id = get_current_user_id() 
      AND role IN ('owner', 'admin')
    )
  );

CREATE POLICY "Super admins can update all deployments" ON customer_app_deployments
  FOR UPDATE USING (is_super_admin());

-- Deployment Templates Policies (read-only for most users)
CREATE POLICY "Anyone can view active templates" ON deployment_templates
  FOR SELECT USING (is_active = true);

CREATE POLICY "Super admins can manage templates" ON deployment_templates
  FOR ALL USING (is_super_admin());

-- Deployment Logs Policies
CREATE POLICY "Users can view logs for their deployments" ON deployment_logs
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM customer_app_deployments 
      WHERE id = deployment_logs.deployment_id 
      AND (
        customer_user_id = get_current_user_id() OR
        EXISTS (
          SELECT 1 FROM customer_app_access 
          WHERE deployment_id = customer_app_deployments.id 
          AND user_id = get_current_user_id()
        )
      )
    )
  );

CREATE POLICY "Super admins can view all logs" ON deployment_logs
  FOR SELECT USING (is_super_admin());

CREATE POLICY "System can create logs" ON deployment_logs
  FOR INSERT WITH CHECK (true); -- Allow system processes to create logs

-- Customer App Access Policies
CREATE POLICY "Users can view access for their deployments" ON customer_app_access
  FOR SELECT USING (
    user_id = get_current_user_id() OR
    EXISTS (
      SELECT 1 FROM customer_app_deployments 
      WHERE id = customer_app_access.deployment_id 
      AND customer_user_id = get_current_user_id()
    )
  );

CREATE POLICY "Deployment owners can manage access" ON customer_app_access
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM customer_app_deployments 
      WHERE id = customer_app_access.deployment_id 
      AND customer_user_id = get_current_user_id()
    )
  );

CREATE POLICY "Super admins can manage all access" ON customer_app_access
  FOR ALL USING (is_super_admin());

-- Insert default deployment templates
INSERT INTO deployment_templates (name, template_type, description, github_repo_url, default_config) VALUES
  (
    'Basic AI Agent',
    'agent_basic',
    'A simple AI agent template with basic chat functionality and customizable prompts.',
    'https://github.com/your-org/agent-basic-template',
    '{"max_tokens": 1000, "temperature": 0.7, "model": "gpt-3.5-turbo"}'
  ),
  (
    'Advanced AI Agent',
    'agent_advanced',
    'Advanced AI agent with custom tools, memory, and integrations.',
    'https://github.com/your-org/agent-advanced-template',
    '{"max_tokens": 2000, "temperature": 0.7, "model": "gpt-4", "tools_enabled": true}'
  ),
  (
    'ROI Calculator Tool',
    'tool_calculator',
    'Interactive ROI calculator tool for business analysis.',
    'https://github.com/your-org/roi-calculator-template',
    '{"calculation_methods": ["simple", "advanced"], "export_formats": ["pdf", "excel"]}'
  ),
  (
    'Analytics Dashboard',
    'tool_analytics',
    'Business analytics and reporting dashboard.',
    'https://github.com/your-org/analytics-dashboard-template',
    '{"chart_types": ["line", "bar", "pie"], "data_sources": ["csv", "api"]}'
  ),
  (
    'SaaS Starter',
    'saas_starter',
    'Basic SaaS application template with user management and billing.',
    'https://github.com/your-org/saas-starter-template',
    '{"auth_provider": "supabase", "payment_provider": "stripe"}'
  );

-- Create helper functions

-- Function to get deployment by subdomain
CREATE OR REPLACE FUNCTION get_deployment_by_subdomain(subdomain_param TEXT)
RETURNS customer_app_deployments AS $$
DECLARE
  deployment customer_app_deployments;
BEGIN
  SELECT * INTO deployment 
  FROM customer_app_deployments 
  WHERE subdomain = subdomain_param 
  AND status = 'active';
  
  RETURN deployment;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to create deployment log
CREATE OR REPLACE FUNCTION create_deployment_log(
  deployment_id_param UUID,
  log_level_param TEXT,
  message_param TEXT,
  details_param JSONB DEFAULT '{}'
)
RETURNS UUID AS $$
DECLARE
  log_id UUID;
BEGIN
  INSERT INTO deployment_logs (deployment_id, log_level, message, details, created_by)
  VALUES (deployment_id_param, log_level_param, message_param, details_param, get_current_user_id())
  RETURNING id INTO log_id;
  
  RETURN log_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update deployment status
CREATE OR REPLACE FUNCTION update_deployment_status(
  deployment_id_param UUID,
  new_status deployment_status_enum,
  log_message TEXT DEFAULT NULL
)
RETURNS BOOLEAN AS $$
DECLARE
  deployment_exists BOOLEAN;
BEGIN
  -- Check if deployment exists and user has permission
  SELECT EXISTS(
    SELECT 1 FROM customer_app_deployments 
    WHERE id = deployment_id_param 
    AND (
      customer_user_id = get_current_user_id() OR is_super_admin()
    )
  ) INTO deployment_exists;
  
  IF NOT deployment_exists THEN
    RETURN FALSE;
  END IF;
  
  -- Update the status
  UPDATE customer_app_deployments 
  SET status = new_status,
      deployed_at = CASE WHEN new_status = 'active' THEN now() ELSE deployed_at END
  WHERE id = deployment_id_param;
  
  -- Create log entry if message provided
  IF log_message IS NOT NULL THEN
    PERFORM create_deployment_log(
      deployment_id_param,
      'info',
      log_message,
      jsonb_build_object('new_status', new_status)
    );
  END IF;
  
  RETURN TRUE;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT ALL ON customer_app_deployments TO authenticated;
GRANT ALL ON deployment_templates TO authenticated;
GRANT ALL ON deployment_logs TO authenticated;
GRANT ALL ON customer_app_access TO authenticated;

-- Grant execute permissions on functions
GRANT EXECUTE ON FUNCTION get_deployment_by_subdomain(TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION create_deployment_log(UUID, TEXT, TEXT, JSONB) TO authenticated;
GRANT EXECUTE ON FUNCTION update_deployment_status(UUID, deployment_status_enum, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION get_current_user_id() TO authenticated;
GRANT EXECUTE ON FUNCTION is_super_admin() TO authenticated;

-- Create views for easier querying

-- View for deployment summary (without sensitive data)
CREATE VIEW deployment_summary AS
SELECT 
  d.id,
  d.app_name,
  d.subdomain,
  d.template_type,
  d.status,
  d.deployment_url,
  d.created_at,
  d.deployed_at,
  u.email as customer_email,
  concat_ws(' ', u.first_name, u.last_name) as customer_name,
  t.name as template_name
FROM customer_app_deployments d
JOIN users u ON d.customer_user_id = u.id
JOIN deployment_templates t ON d.template_type = t.template_type
WHERE d.status != 'archived';

-- View for user's deployments
CREATE VIEW my_deployments AS
SELECT 
  d.*,
  t.name as template_name,
  t.description as template_description
FROM customer_app_deployments d
JOIN deployment_templates t ON d.template_type = t.template_type
WHERE d.customer_user_id = get_current_user_id()
   OR EXISTS (
     SELECT 1 FROM customer_app_access 
     WHERE deployment_id = d.id 
     AND user_id = get_current_user_id()
   );

-- Grant permissions on views
GRANT SELECT ON deployment_summary TO authenticated;
GRANT SELECT ON my_deployments TO authenticated;

-- Comments for documentation
COMMENT ON TABLE customer_app_deployments IS 'Tracks customer app deployments - each record represents a separate Replit app deployed for a customer';
COMMENT ON TABLE deployment_templates IS 'Available templates for creating new customer app deployments';
COMMENT ON TABLE deployment_logs IS 'Audit trail and deployment process logs';
COMMENT ON TABLE customer_app_access IS 'Manages who can access which deployed customer apps';
COMMENT ON FUNCTION get_deployment_by_subdomain(TEXT) IS 'Retrieve active deployment by subdomain for routing purposes';
COMMENT ON FUNCTION update_deployment_status(UUID, deployment_status_enum, TEXT) IS 'Update deployment status with automatic logging';
COMMENT ON FUNCTION get_current_user_id() IS 'Returns the integer ID of the currently authenticated user by mapping from auth.uid()';
COMMENT ON FUNCTION is_super_admin() IS 'Checks if the currently authenticated user has the super_admin role'; 