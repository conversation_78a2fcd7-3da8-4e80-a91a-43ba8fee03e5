import { createClient, SupabaseClient } from '@supabase/supabase-js';
import { randomUUID } from 'crypto';
import type { IStorage } from './storage';
import type { BlogPost, User } from '@shared/schema';

// Helper to get the Supabase client
function getSupabaseClient(): SupabaseClient {
    const supabaseUrl = process.env.VITE_SUPABASE_URL;
    const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseServiceRoleKey) {
        throw new Error("Supabase environment variables are not set.");
    }
    return createClient(supabaseUrl, supabaseServiceRoleKey);
}

function toSnakeCase(obj: any): any {
    if (Array.isArray(obj)) {
        return obj.map(v => toSnakeCase(v));
    } else if (obj !== null && typeof obj === 'object' && obj.constructor === Object) {
        return Object.keys(obj).reduce((result, key) => {
            const snakeKey = key.replace(/[A-Z]/g, letter => `_${letter.toLowerCase()}`);
            result[snakeKey] = toSnakeCase(obj[key]);
            return result;
        }, {} as any);
    }
    return obj;
}

export class SupabaseStorage implements IStorage {
    private supabase: SupabaseClient;

    constructor() {
        this.supabase = getSupabaseClient();
    }

    // --- Deployment Management ---

    async listWorkspaceTemplates(): Promise<any[]> {
        // Mock data for now - replace with actual Supabase query when table exists
        return [
            { id: "web-app", name: "Web Application", description: "Full-stack web application workspace" },
            { id: "api-service", name: "API Service", description: "Microservice API workspace" },
            { id: "data-pipeline", name: "Data Pipeline", description: "Data processing and analytics workspace" }
        ];
    }

    async listAllWorkspaces(): Promise<any[]> {
        const { data, error } = await this.supabase
            .from('workspaces')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching all workspaces:', error);
            return [];
        }

        return data || [];
    }

    async listUserWorkspaces(userId: number): Promise<any[]> {
        const { data, error } = await this.supabase
            .from('workspaces')
            .select('*')
            .eq('user_id', userId)
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching user workspaces:', error);
            return [];
        }

        return data || [];
    }

    async createWorkspace(workspaceData: any): Promise<any> {
        const newId = randomUUID();
        const workspaceToInsert = toSnakeCase({
            ...workspaceData,
            id: newId,
        });

        const { data, error } = await this.supabase
            .from('workspaces')
            .insert([workspaceToInsert])
            .select()
            .single();

        if (error) {
            console.error(`Failed to create workspace: ${error.message}`, { details: error.details, hint: error.hint });
            throw new Error(`Failed to create workspace: ${error.message}`);
        }

        return data;
    }

    async getWorkspaceById(workspaceId: string, userId?: number): Promise<any | null> {
        let query = this.supabase
            .from('workspaces')
            .select('*')
            .eq('id', workspaceId);

        if (userId) {
            query = query.eq('user_id', userId);
        }

        const { data, error } = await query.single();

        if (error) {
            console.error('Error fetching workspace:', error);
            return null;
        }

        return data;
    }

    async updateWorkspace(workspaceId: string, updateData: any): Promise<any | null> {
        const workspaceToUpdate = toSnakeCase(updateData);

        const { data, error } = await this.supabase
            .from('workspaces')
            .update(workspaceToUpdate)
            .eq('id', workspaceId)
            .select()
            .single();

        if (error) {
            throw new Error(`Failed to update workspace: ${error.message}`);
        }

        return data;
    }

    async deleteWorkspace(workspaceId: string): Promise<boolean> {
        const { error } = await this.supabase
            .from('workspaces')
            .delete()
            .eq('id', workspaceId);

        if (error) {
            throw new Error(`Failed to delete workspace: ${error.message}`);
        }

        return true;
    }

    async archiveWorkspace(workspaceId: string): Promise<any | null> {
        const { data, error } = await this.supabase
            .from('workspaces')
            .update({ 
                status: 'archived',
                updated_at: new Date().toISOString()
            })
            .eq('id', workspaceId)
            .select()
            .single();

        if (error) {
            throw new Error(`Failed to archive workspace: ${error.message}`);
        }

        return data;
    }

    async listAllDeployments(): Promise<any[]> {
        // This should only be used by super admins, RLS will enforce this
        const { data, error } = await this.supabase
            .from('deployment_summary') // Use the summary view for performance
            .select('*')
            .order('created_at', { ascending: false });

        if (error) {
            console.error('Error fetching all deployments:', error);
            throw new Error(error.message);
        }
        return data || [];
    }

    async listUserDeployments(userId: number): Promise<any[]> {
        // RLS policy on the 'my_deployments' view will handle security
        const { data, error } = await this.supabase
            .from('my_deployments')
            .select('*')
            .order('created_at', { ascending: false });

        if (error) {
            console.error(`Error fetching deployments for user ${userId}:`, error);
            throw new Error(error.message);
        }
        return data || [];
    }
    
    async createDeployment(deploymentData: any): Promise<any> {
        const { data, error } = await this.supabase
            .from('customer_app_deployments')
            .insert(deploymentData)
            .select()
            .single();

        if (error) {
            console.error('Error creating deployment:', error);
            throw new Error(error.message);
        }
        return data;
    }

    async getDeploymentById(deploymentId: string, userId?: number): Promise<any | null> {
        // RLS will enforce that the user can only see their own deployment
        const { data, error } = await this.supabase
            .from('my_deployments') // Use the view that respects user permissions
            .select('*')
            .eq('id', deploymentId)
            .single();
        
        if (error && error.code !== 'PGRST116') { // Ignore 'No rows found' error
            console.error(`Error fetching deployment ${deploymentId}:`, error);
            throw new Error(error.message);
        }
        return data;
    }

    async updateDeployment(deploymentId: string, updateData: any): Promise<any | null> {
        // RLS policies will ensure only owners/admins can update
        const { data, error } = await this.supabase
            .from('customer_app_deployments')
            .update(updateData)
            .eq('id', deploymentId)
            .select()
            .single();

        if (error) {
            console.error(`Error updating deployment ${deploymentId}:`, error);
            throw new Error(error.message);
        }
        return data;
    }


    // --- Blog Post Management (Matching IStorage interface) ---

    async getBlogPosts(publishedOnly?: boolean): Promise<BlogPost[]> {
        throw new Error('Method not implemented.');
    }
    async getBlogPostBySlug(slug: string): Promise<BlogPost | undefined> {
        throw new Error('Method not implemented.');
    }
    async createBlogPost(post: Omit<BlogPost, "id" | "createdAt" | "updatedAt">): Promise<BlogPost> {
        throw new Error('Method not implemented.');
    }
    async updateBlogPost(id: number, updates: Partial<BlogPost>): Promise<BlogPost> {
        throw new Error('Method not implemented.');
    }
    async deleteBlogPost(id: number): Promise<boolean> {
        throw new Error('Method not implemented.');
    }
    
    // --- User Management (to be implemented) ---
    async getUser(id: number): Promise<User | undefined> {
        throw new Error('Method not implemented.');
    }
    async getUserByEmail(email: string): Promise<User | undefined> {
        throw new Error('Method not implemented.');
    }
    async updateUser(id: number, updates: Partial<User>): Promise<User> {
        throw new Error('Method not implemented.');
    }
    async deleteUser(id: number): Promise<void> {
        throw new Error('Method not implemented.');
    }
    async getAllUsers(tenantId?: number): Promise<User[]> {
        throw new Error('Method not implemented.');
    }

    async createUser(insertUser: import("../shared/schema").InsertUser): Promise<User> {
        throw new Error('Method not implemented.');
    }
    
    public async getBlogStats(tenantId?: number | undefined): Promise<{ totalPosts: number; draftPosts: number; }> {
        throw new Error("Method not implemented.");
    }

    async getAdminStats(): Promise<{ totalUsers: number; totalPosts: number; draftPosts: number; }> {
        try {
            const [
                { count: totalUsers, error: usersError },
                { count: totalPosts, error: publishedError },
                { count: draftPosts, error: draftsError }
            ] = await Promise.all([
                this.supabase.from('users').select('*', { count: 'exact', head: true }),
                this.supabase.from('blog_posts').select('*', { count: 'exact', head: true }).eq('is_published', true),
                this.supabase.from('blog_posts').select('*', { count: 'exact', head: true }).eq('is_published', false)
            ]);

            if (usersError) throw new Error(`Error fetching total users: ${usersError.message}`);
            if (publishedError) throw new Error(`Error fetching published posts: ${publishedError.message}`);
            if (draftsError) throw new Error(`Error fetching draft posts: ${draftsError.message}`);

            return {
                totalUsers: totalUsers || 0,
                totalPosts: totalPosts || 0,
                draftPosts: draftPosts || 0,
            };
        } catch (error: any) {
            console.error('Error in getAdminStats:', error);
            throw new Error(`Failed to retrieve admin statistics: ${error.message}`);
        }
    }
} 