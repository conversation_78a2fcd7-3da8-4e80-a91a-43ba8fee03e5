---
description: 
globs: 
alwaysApply: true
---
# AI-Assisted Development Workflow: Cursor & Replit

## Executive Summary

This document outlines a systematic workflow for collaborative development using <PERSON><PERSON><PERSON> (as code reviewer and task manager) and <PERSON><PERSON> (as builder), with a human coordinator overseeing the process. The workflow addresses key challenges in AI-assisted development:

1. **Technical Debt Prevention**: Structured checkpoints and review processes to prevent AI coders from implementing conflicting solutions or coding themselves into corners
2. **Context Retention**: Documentation protocols to minimize repetitive explanations when opening new sessions
3. **Human-in-the-Loop Control**: Strategic intervention points where human expertise is most valuable
4. **Automation**: File-based communication through repositories to reduce manual copying and pasting

The workflow is organized into four main phases with supporting communication protocols:

1. **Project Initialization**: Repository setup, requirements definition, and task planning
2. **Development**: Incremental implementation with strategic checkpoints
3. **Review and Feedback**: Structured evaluation and improvement processes
4. **Deployment**: Controlled release with clear rollback procedures

Each phase includes detailed procedures, documentation templates, and best practices to ensure consistent, high-quality results while minimizing manual intervention.

This workflow can be implemented by development teams of any size, from individual developers to large organizations, and is suitable for various project types including web applications, data science projects, and AI-integrated solutions.

