import { ReactNode } from 'react';
import { Link, useLocation } from 'wouter';
import { Logo } from '@/components/ui/logo';
import { Button } from '@/components/ui/button';
import { useAuthContext } from '@/components/auth/AuthProvider';
import { 
  LayoutDashboard, 
  FileText, 
  Users, 
  Settings, 
  LogOut,
  Menu,
  X
} from 'lucide-react';
import { useState } from 'react';

interface AdminLayoutProps {
  children: ReactNode;
  className?: string;
  title?: string;
}

/**
 * AdminLayout - Layout for admin and dashboard pages
 * 
 * Provides consistent structure for admin pages including:
 * - Admin header with user info and logout
 * - Collapsible sidebar navigation
 * - Main content area with semantic HTML
 * - Mobile-responsive design
 * - Skip link for accessibility
 * 
 * @param children - Main content to display
 * @param className - Optional CSS classes for the main content area
 * @param title - Optional page title for the header
 */
export function AdminLayout({ 
  children, 
  className = '', 
  title 
}: AdminLayoutProps) {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [location] = useLocation();
  const { user, logout } = useAuthContext();

  const navigationItems = [
    {
      name: 'Dashboard',
      href: '/dashboard',
      icon: LayoutDashboard,
    },
    {
      name: 'Blog Management',
      href: '/admin/blog',
      icon: FileText,
    },
    {
      name: 'User Management',
      href: '/admin/users',
      icon: Users,
    },
    ...(user?.role === 'super_admin' ? [{
      name: 'Workspaces',
      href: '/admin/workspaces',
      icon: Settings,
    }] : []),
    {
      name: 'Settings',
      href: '/admin/settings',
      icon: Settings,
    },
  ];

  const isActiveLink = (href: string) => location === href;

  return (
    <div className="flex min-h-screen bg-gray-100 dark:bg-gray-900">
      {/* Skip link for accessibility */}
      <a
        href="#admin-main-content"
        className="sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 bg-white text-black px-4 py-2 rounded z-50"
      >
        Skip to main content
      </a>

      {/* Sidebar */}
      <aside
        className={`w-64 bg-white dark:bg-gray-800 border-r border-gray-200 dark:border-gray-700 flex-shrink-0 z-50 ${sidebarOpen ? '' : 'hidden lg:block'}`}
        role="navigation"
        aria-label="Admin navigation"
      >
        <div className="flex flex-col h-full">
          {/* Sidebar header */}
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <Link href="/">
              <Logo size="sm" variant="horizontal" />
            </Link>
            <Button
              variant="ghost"
              size="sm"
              className="lg:hidden"
              onClick={() => setSidebarOpen(false)}
              aria-label="Close sidebar"
            >
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Navigation */}
          <nav className="flex-1 p-4 space-y-2" role="navigation">
            {navigationItems.map((item) => {
              const Icon = item.icon;
              const active = isActiveLink(item.href);
              
              return (
                <Link 
                  key={item.name} 
                  href={item.href}
                  className={`flex items-center px-3 py-2 text-sm font-medium rounded-lg transition-colors ${
                    active
                      ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-100'
                      : 'text-gray-700 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                  }`}
                  aria-current={active ? 'page' : undefined}
                >
                  <Icon className="h-5 w-5 mr-3" />
                  {item.name}
                </Link>
              );
            })}
          </nav>

          {/* User info and logout */}
          <div className="p-4 border-t border-gray-200 dark:border-gray-700">
            <div className="flex items-center mb-3">
              <div className="flex-1">
                <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                  {user?.firstName && user?.lastName ? `${user.firstName} ${user.lastName}` : user?.email}
                </p>
                <p className="text-xs text-gray-500 dark:text-gray-400 capitalize">
                  {user?.role}
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={logout}
              className="w-full"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Sign Out
            </Button>
          </div>
        </div>
      </aside>

      {/* Mobile sidebar overlay */}
      {sidebarOpen && (
        <div 
          className="fixed inset-0 z-40 bg-black bg-opacity-50 lg:hidden"
          onClick={() => setSidebarOpen(false)}
        />
      )}

      {/* Main content area */}
      <div className="flex-1 flex flex-col">
        {/* Top header */}
        <header 
          className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-4 py-4"
          role="banner"
        >
          <div className="flex items-center justify-between h-8">
            <div className="flex items-center h-full">
              <Button
                variant="ghost"
                size="sm"
                className="lg:hidden mr-2 h-8 w-8 p-0"
                onClick={() => setSidebarOpen(true)}
                aria-label="Open sidebar"
              >
                <Menu className="h-5 w-5" />
              </Button>
              {title && (
                <h1 className="text-xl font-semibold text-gray-900 dark:text-white leading-8">
                  {title}
                </h1>
              )}
            </div>
            <div className="h-full"></div>
          </div>
        </header>

        {/* Main content */}
        <main 
          id="admin-main-content"
          role="main"
          className={`flex-1 p-6 ${className}`}
          tabIndex={-1}
        >
          {children}
        </main>
      </div>
    </div>
  );
}