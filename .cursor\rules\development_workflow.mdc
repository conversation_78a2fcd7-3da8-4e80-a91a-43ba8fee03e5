---
description: 
globs: 
alwaysApply: true
---
# Development Workflow

## Overview

The development workflow defines the iterative process through which <PERSON><PERSON> (the builder) implements features based on the tasks defined during project initialization, with <PERSON><PERSON><PERSON> (the reviewer) providing guidance and quality control. This phase is designed to prevent technical debt accumulation, maintain code quality, and ensure adherence to the project requirements while minimizing context loss between sessions.

## Detailed Procedure

### Task Selection and Assignment

Begin each development cycle by selecting the next highest-priority task from the TASKS.md file. Tasks should be selected based on their priority level, dependencies, and logical grouping. Avoid jumping between unrelated features, as this increases the risk of context switching and technical debt.

When assigning a task to <PERSON><PERSON>, create a new branch in the repository named according to the convention `feature/task-name` or `bugfix/issue-description`. This branch-based workflow ensures that each feature is developed in isolation and can be reviewed independently before being merged into the main codebase.

Prepare a detailed task assignment prompt using the templates defined in PROMPTS.md. The prompt should include:

1. A clear description of the feature or component to be implemented
2. References to relevant sections of the PRD
3. Links to any design assets or specifications
4. Explicit boundaries for the implementation scope
5. Defined checkpoints where <PERSON><PERSON> should pause for review

This structured approach to task assignment helps prevent scope creep and ensures that <PERSON><PERSON> has all necessary context to complete the task successfully.

### Implementation with Checkpoints

Rep<PERSON> begins implementation based on the task assignment prompt, working within the specified branch. The development process should follow these guidelines:

1. Start with a clear understanding of the component's purpose and interfaces
2. Implement the feature incrementally, focusing on core functionality first
3. Add tests for critical functionality as development progresses
4. Document code with clear comments explaining complex logic
5. Pause at predefined checkpoints for review

Checkpoints should be strategically placed at points where architectural decisions are being made or when a significant component is completed. These checkpoints prevent the accumulation of technical debt by catching design issues early.

When reaching a checkpoint, Replit should:

1. Commit the current state of the code with a descriptive message
2. Push the changes to the GitHub repository
3. Generate a brief summary of the implementation approach and any questions or concerns
4. Signal that the code is ready for review

This checkpoint system creates natural breaking points that help maintain context and prevent Replit from coding itself into a corner.

### Incremental Review Process

Upon notification that a checkpoint has been reached, open a session with Cursor and provide a review request prompt based on the templates in PROMPTS.md. The prompt should include:

1. The specific branch and files to be reviewed
2. Context about the feature being implemented
3. Any specific concerns or areas that need particular attention
4. References to relevant sections of the PRD or design documents

Cursor should then perform a comprehensive review that includes:

1. Code quality assessment (adherence to standards, best practices)
2. Architectural evaluation (design patterns, component structure)
3. Potential technical debt identification
4. Security and performance considerations
5. Alignment with project requirements

The review should be documented in a structured format as defined in the communication protocol, with clear categorization of issues (critical, major, minor) and specific recommendations for improvements.

### Feedback Implementation

After receiving Cursor's review, provide Replit with a feedback implementation prompt that includes:

1. A link to Cursor's review document
2. Prioritized list of issues to address
3. Specific guidance on how to approach complex changes
4. Any clarifications or decisions made based on the review

Replit should then implement the feedback methodically, addressing critical issues first and documenting how each issue was resolved. This implementation should be committed with clear references to the specific review comments being addressed.

After implementing the feedback, Replit should signal that the changes are ready for verification, and the code should undergo a brief verification review by Cursor to ensure all critical issues have been properly addressed.

### Feature Completion and Integration

Once a feature passes the verification review, it is ready for integration into the main codebase. This process involves:

1. Creating a pull request from the feature branch to the main branch
2. Having Cursor perform a final review of the pull request
3. Resolving any remaining issues identified during the pull request review
4. Merging the feature branch into the main branch
5. Updating the TASKS.md file to mark the task as completed

This structured integration process ensures that only high-quality, reviewed code enters the main codebase, preventing the accumulation of technical debt.

### Context Retention Between Sessions

To address the challenge of context loss between sessions, implement these strategies:

1. Maintain a CONTEXT.md file in the repository that summarizes the current state of development, recent decisions, and ongoing challenges
2. Update this file at the end of each development session
3. Begin each new session by reviewing this file to reestablish context
4. Use the context restoration prompts from PROMPTS.md when starting new sessions with either AI agent

Additionally, maintain a DECISIONS.md file that documents important architectural and design decisions, including the rationale behind each decision. This documentation serves as a reference that helps prevent contradictory decisions in future sessions.

### Handling Blockers and Challenges

When development encounters significant challenges or blockers, implement this escalation process:

1. Document the specific issue in detail, including attempted solutions
2. Consult with Cursor for potential approaches or alternatives
3. If necessary, create a focused research task to explore solutions
4. Document the resolution approach in DECISIONS.md for future reference

This structured approach to problem-solving prevents Replit from getting stuck in loops of ineffective fixes and ensures that complex challenges are addressed methodically.

NEVER DELETE ANYTHING WITHOUT APPROVAL
YOU ALWAYS GET APPROVAL BEFORE MAKING ANY CHANGES!!!!!!!!!!!!!!!!!!
