{"tasks": [{"id": 1, "title": "Set up Supabase Project", "description": "Create and configure a new Supabase project for the multi-tenant architecture", "details": "1. Create a new Supabase project\n2. Configure project settings (region, pricing plan)\n3. Set up database connection\n4. Configure authentication settings (email templates, password policies)\n5. Enable necessary extensions\n6. Set up initial admin account\n7. Configure project API keys and security settings\n8. Document project configuration for team reference", "testStrategy": "Verify successful project creation and configuration by testing database connection, authentication flow, and API access. Create test user to confirm email templates are working correctly.", "priority": "high", "dependencies": [], "status": "done", "subtasks": []}, {"id": 2, "title": "Design Multi-Tenant Database Schema", "description": "Design and implement the database schema with tenant_id columns and prepare for Row Level Security", "details": "1. Analyze existing database schema\n2. Add tenant_id column to all relevant tables\n3. Create necessary indexes on tenant_id columns\n4. Design foreign key relationships with tenant context\n5. Create migration scripts for existing data\n6. Document schema changes\n7. Create database diagrams for the new multi-tenant schema\n8. Consider performance implications of queries with tenant filtering", "testStrategy": "Create test queries to verify schema design. Ensure all tables have proper tenant_id columns and indexes. Validate foreign key relationships work correctly with tenant context.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 3, "title": "Implement Row Level Security Policies", "description": "Create and apply RLS policies to enforce tenant data isolation across all tables", "details": "1. Create RLS policies for each table with tenant_id\n2. Enable RLS on all tables\n3. Create tenant-aware roles and permissions\n4. Test policies with different tenant contexts\n5. Implement policy for shared resources (if any)\n6. Document all RLS policies\n7. Example policy for a table:\n```sql\nCREATE POLICY tenant_isolation_policy\n  ON table_name\n  USING (tenant_id = current_setting('app.current_tenant_id')::uuid);\n```", "testStrategy": "Create test users with different tenant associations. Verify users can only access their tenant's data. Test edge cases like null tenant_id or attempts to access other tenant data.", "priority": "high", "dependencies": [2], "status": "done", "subtasks": []}, {"id": 4, "title": "Configure CORS for Multi-Domain Support", "description": "Set up CORS configuration to allow API access from multiple customer subdomains", "status": "done", "dependencies": [1], "priority": "medium", "details": "✅ CORS Configuration has been completed with the following implementation:\n\n1. **Installed Dependencies**: Added `cors` and `@types/cors` packages\n2. **Server Configuration**: Added comprehensive CORS middleware to `server/index.ts`\n\n**CORS Features Implemented:**\n- **Multi-Domain Support**: Allows requests from main domain (`www.agent-factory.io`) and customer subdomains (`*.agent-factory.app`)\n- **Development Support**: Includes localhost origins for development\n- **Security**: Blocks unauthorized origins with logging\n- **Credentials**: Enables cookies and authorization headers\n- **Headers**: Supports tenant identification via `X-Tenant-ID` header\n- **Methods**: Allows all standard HTTP methods\n- **Preflight**: Handles OPTIONS requests for complex CORS scenarios\n\n**Allowed Origins:**\n- `https://www.agent-factory.io` (main site)\n- `https://agent-factory.io` (apex domain)\n- `https://api.agent-factory.io` (API domain)\n- `https://*.agent-factory.app` (customer subdomains via regex)\n- Local development origins (localhost:3000, localhost:5000, etc.)\n\n**Security Features:**\n- Origin validation with explicit allowlist\n- Regex pattern matching for customer subdomains\n- CORS violation logging for monitoring\n- Credential support for authenticated requests", "testStrategy": "Test API requests from all configured domains:\n1. Main domain (www.agent-factory.io)\n2. Apex domain (agent-factory.io)\n3. API domain (api.agent-factory.io)\n4. Various customer subdomains (customer1.agent-factory.app, customer2.agent-factory.app)\n5. Local development environments (localhost:3000, localhost:5000)\n\nVerify the following:\n- Preflight requests (OPTIONS) work correctly\n- Authentication headers and cookies are properly transmitted\n- X-Tenant-ID header is properly supported\n- Invalid domains are blocked with appropriate error responses\n- CORS violations are properly logged", "subtasks": [{"id": 4.1, "title": "Install CORS dependencies", "status": "completed", "description": "Added `cors` and `@types/cors` packages to the project"}, {"id": 4.2, "title": "Implement CORS middleware", "status": "completed", "description": "Added comprehensive CORS configuration to server/index.ts with multi-domain support"}, {"id": 4.3, "title": "Configure allowed origins", "status": "completed", "description": "Set up allowlist for main domains, customer subdomains, and development environments"}, {"id": 4.4, "title": "Implement security features", "status": "completed", "description": "Added origin validation, regex pattern matching, and CORS violation logging"}, {"id": 4.5, "title": "Document CORS configuration", "status": "completed", "description": "Documented the CORS setup including allowed origins and security features"}]}, {"id": 5, "title": "Implement JWT Authentication Middleware", "description": "Create middleware to verify Supabase JWT tokens and extract user and tenant information", "details": "1. Create authentication middleware for Express.js\n2. Implement JWT verification using Supabase public key\n3. Extract user claims from verified token\n4. Extract and validate tenant information\n5. Set tenant context for RLS\n6. Handle authentication errors\n7. Example middleware implementation:\n```javascript\nconst { createClient } = require('@supabase/supabase-js');\n\nconst authMiddleware = async (req, res, next) => {\n  const authHeader = req.headers.authorization;\n  if (!authHeader || !authHeader.startsWith('Bearer ')) {\n    return res.status(401).json({ error: 'Unauthorized' });\n  }\n  \n  const token = authHeader.split(' ')[1];\n  const tenantId = req.headers['x-tenant-id'];\n  \n  try {\n    // Verify JWT token\n    const { data, error } = await supabase.auth.getUser(token);\n    if (error) throw error;\n    \n    // Set user and tenant context\n    req.user = data.user;\n    req.tenantId = tenantId;\n    \n    // Set PostgreSQL RLS context\n    await supabase.rpc('set_tenant_context', { tenant_id: tenantId });\n    \n    next();\n  } catch (error) {\n    return res.status(401).json({ error: 'Invalid token' });\n  }\n};\n```", "testStrategy": "Test middleware with valid and invalid tokens. Verify user information is correctly extracted. Test with missing or invalid tenant headers. Verify RLS context is correctly set.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 6, "title": "Create Tenant Header Processing", "description": "Implement middleware to process X-Tenant-ID headers and set tenant context for database queries", "status": "done", "dependencies": [3, 5], "priority": "high", "details": "✅ IMPLEMENTATION COMPLETE\n\n**What was implemented:**\n\n1. **Tenant Context Middleware (`requireTenantContext`)**:\n   - Extracts tenant ID from `X-Tenant-ID` header or subdomain parsing\n   - Validates tenant exists and is active in database\n   - Checks user membership in tenant via `tenant_memberships` table\n   - Sets `req.tenantId`, `req.tenantSlug`, `req.tenantRole` for downstream use\n   - Special handling for super_admin users (optional tenant context)\n   - Proper error handling for missing/invalid tenant IDs\n   - Comprehensive logging for debugging\n\n2. **Subdomain Processing**:\n   - Automatically detects tenant from subdomain (e.g., `testco.agent-factory.app`)\n   - Skips main domains (`www`, `api`, `localhost`)\n   - Looks up tenant by slug in database\n\n3. **Access Control**:\n   - Verifies user has active membership in requested tenant\n   - Returns appropriate error messages for invalid/unauthorized access\n   - Super admins can access any tenant or platform-wide resources\n   - Validates tenant is active (`status = 'active'`)\n   - Uses Supabase user ID for membership validation\n\n4. **API Client Updates**:\n   - Added `setTenantId()` and `getTenantId()` methods\n   - Automatically includes `X-Tenant-ID` header when tenant context is set\n   - Maintains backward compatibility for non-tenant requests\n\n5. **Test Infrastructure**:\n   - Created `/api/tenant/context` endpoint for testing\n   - Added `scripts/setup-test-tenant.sql` for creating test data\n   - Convenience `requireTenantAuth` middleware combining auth + tenant\n\n6. **TypeScript Integration**:\n   - Extended Express Request interface with tenant properties\n   - Proper type safety for tenant context\n\n**Implementation Location:**\nThe tenant middleware is fully implemented in `server/routes.ts` with comprehensive functionality.\n\n**Implementation Quality:**\n- Follows best practices for middleware chaining\n- Comprehensive error handling and logging\n- Supports both header-based and subdomain-based tenant detection\n- Proper access control and security validation\n- Ready for production use", "testStrategy": "✅ TESTING COMPLETE\n\nTesting approach used:\n- Run test tenant setup script in Supabase\n- Test with `X-Tenant-ID` header\n- Test with subdomain routing  \n- Test access control for different user roles\n\nSpecific test cases covered:\n1. Extracting tenant ID from header\n2. Subdomain parsing for automatic tenant detection\n3. Validating tenant ID against allowed tenants for user\n4. Setting tenant context for database queries\n5. Handling missing or invalid tenant IDs\n6. Special handling for super_admin users\n7. API client with tenant context methods\n8. Combined auth + tenant middleware\n9. Validation of tenant active status\n10. Proper error responses for various failure scenarios\n11. TypeScript type safety for tenant context properties", "subtasks": []}, {"id": 7, "title": "Implement Data Migration Strategy", "description": "Document a future data migration strategy, but proceed with fresh Supabase implementation as no current migration is required", "status": "done", "dependencies": [2, 3], "priority": "low", "details": "**Current Status: No Migration Required**\n\n**Analysis Complete:**\n- The application is still in development phase\n- No existing users or production data to migrate\n- Fresh Supabase setup with clean database\n- All new users will be created directly in Supabase\n\n**Future Migration Strategy (for reference):**\n1. Analyze existing data structure and volume\n2. Create data extraction scripts from source database\n3. Design transformation process to add tenant context\n4. Implement data loading scripts for Supabase\n5. Create validation and verification procedures\n6. Design rollback strategy\n7. Implement data synchronization for zero-downtime migration\n8. Document complete migration process\n\n**Example future migration script structure:**\n```javascript\nasync function migrateData() {\n  // Extract data from source DB\n  const users = await extractUsers();\n  const projects = await extractProjects();\n  // ... other data\n  \n  // Transform data with tenant context\n  const transformedUsers = transformUsers(users);\n  const transformedProjects = transformProjects(projects);\n  \n  // Load data into Supabase\n  await loadUsers(transformedUsers);\n  await loadProjects(transformedProjects);\n  \n  // Verify data integrity\n  const verification = await verifyMigration();\n  console.log('Migration verification:', verification);\n}\n```", "testStrategy": "No immediate testing required as we're proceeding with a fresh implementation. Document testing procedures for future migrations including: test migrations with sample data, data integrity verification, rollback procedure testing, and migration performance optimization.", "subtasks": [{"id": 7.1, "title": "Document future migration strategy", "status": "completed", "description": "Document the approach for future migrations when they become necessary"}, {"id": 7.2, "title": "Confirm fresh Supabase implementation", "status": "completed", "description": "Verify that we are proceeding with a clean Supabase setup with no migration needed"}]}, {"id": 8, "title": "Update Backend API Routes", "description": "Modify existing API routes to work with Supabase authentication and multi-tenant context", "details": "1. Update all API route handlers to use new authentication middleware\n2. Modify database queries to use Supabase client\n3. Remove session-based authentication code\n4. Ensure all queries respect tenant context\n5. Update error handling for authentication failures\n6. Maintain existing API response formats\n7. Example API route update:\n```javascript\n// Before\napp.get('/api/projects', sessionAuth, async (req, res) => {\n  try {\n    const projects = await db.getProjects(req.session.userId);\n    res.json(projects);\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n});\n\n// After\napp.get('/api/projects', authMiddleware, tenantMiddleware, async (req, res) => {\n  try {\n    const { data, error } = await supabase\n      .from('projects')\n      .select('*');\n      \n    if (error) throw error;\n    res.json(data);\n  } catch (error) {\n    res.status(500).json({ error: error.message });\n  }\n});\n```", "testStrategy": "Test each updated API endpoint with valid and invalid authentication. Verify tenant isolation works correctly. Compare response formats with original API to ensure compatibility.", "priority": "high", "dependencies": [5, 6], "status": "done", "subtasks": []}, {"id": 9, "title": "Implement Supabase Auth Client in Frontend", "description": "Replace current authentication system with Supabase Auth in the frontend application", "details": "1. Install Supabase client library\n2. Configure Supabase client with project URL and public key\n3. Implement sign up, sign in, and sign out functions\n4. Create authentication context provider\n5. Update protected route guards\n6. Implement password reset and email verification flows\n7. Handle authentication errors and user feedback\n8. Example implementation:\n```javascript\nimport { createClient } from '@supabase/supabase-js';\nimport { createContext, useContext, useState, useEffect } from 'react';\n\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL;\nconst supabaseKey = process.env.REACT_APP_SUPABASE_ANON_KEY;\nconst supabase = createClient(supabaseUrl, supabaseKey);\n\nconst AuthContext = createContext();\n\nexport function AuthProvider({ children }) {\n  const [user, setUser] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Check for existing session\n    const session = supabase.auth.getSession();\n    setUser(session?.user || null);\n    setLoading(false);\n\n    // Listen for auth changes\n    const { data: { subscription } } = supabase.auth.onAuthStateChange(\n      (event, session) => {\n        setUser(session?.user || null);\n      }\n    );\n\n    return () => subscription.unsubscribe();\n  }, []);\n\n  const value = {\n    signUp: (data) => supabase.auth.signUp(data),\n    signIn: (data) => supabase.auth.signInWithPassword(data),\n    signOut: () => supabase.auth.signOut(),\n    user,\n  };\n\n  return (\n    <AuthContext.Provider value={value}>\n      {!loading && children}\n    </AuthContext.Provider>\n  );\n}\n\nexport function useAuth() {\n  return useContext(AuthContext);\n}\n```", "testStrategy": "Test all authentication flows (signup, login, logout, password reset). Verify authentication state is correctly maintained. Test error handling for invalid credentials and network issues.", "priority": "high", "dependencies": [1], "status": "done", "subtasks": []}, {"id": 10, "title": "Update Frontend API Calls", "description": "Modify frontend API calls to include authentication tokens and tenant headers", "details": "1. Create API client with authentication token injection\n2. Add tenant header to all API requests\n3. Update all API call locations in the codebase\n4. Handle authentication errors and token refresh\n5. Implement automatic tenant detection from subdomain\n6. Example API client implementation:\n```javascript\nimport { useAuth } from './auth-context';\nimport { useTenant } from './tenant-context';\n\nexport function useApiClient() {\n  const { user } = useAuth();\n  const { currentTenant } = useTenant();\n\n  const apiCall = async (endpoint, options = {}) => {\n    if (!user) throw new Error('Authentication required');\n    if (!currentTenant) throw new Error('Tenant context required');\n\n    const headers = {\n      'Content-Type': 'application/json',\n      'Authorization': `Bearer ${user.access_token}`,\n      'X-Tenant-ID': currentTenant.id,\n      ...options.headers,\n    };\n\n    const response = await fetch(`https://api.agent-factory.io${endpoint}`, {\n      ...options,\n      headers,\n    });\n\n    if (!response.ok) {\n      const error = await response.json();\n      throw new Error(error.message || 'API request failed');\n    }\n\n    return response.json();\n  };\n\n  return { apiCall };\n}\n```", "testStrategy": "Test API calls with different authentication states. Verify tenant headers are correctly included. Test error handling for authentication failures and API errors.", "priority": "high", "dependencies": [9], "status": "done", "subtasks": []}, {"id": 11, "title": "Implement Cross-Domain Authentication", "description": "Enable seamless authentication across main site and customer subdomains", "status": "done", "dependencies": [9, 10], "priority": "high", "details": "## Implementation Complete\n\n### 1. Enhanced Supabase Client Configuration (`client/src/lib/supabaseClient.ts`)\n- Configured PKCE flow for secure cross-domain authentication\n- Custom storage handlers for localStorage persistence across domains\n- Auto-refresh and session detection enabled\n- Cross-domain authentication utilities with domain detection\n- Session transfer mechanisms for seamless domain switching\n- Automatic cleanup of transferred auth state\n\n### 2. Updated AuthProvider (`client/src/components/auth/AuthProvider.tsx`)\n- Cross-domain session restoration on page load\n- Enhanced login method with optional subdomain redirect\n- `loginWithRedirect()` method for explicit cross-domain flows\n- `handleAuthCallback()` method for processing auth redirects\n- Domain-aware logout that cleans up all cross-domain state\n- `getCurrentDomain()` utility for domain context awareness\n\n### 3. Authentication Callback Page (`client/src/pages/auth/AuthCallbackPage.tsx`)\n- Dedicated page for handling cross-domain auth redirects\n- Visual feedback during authentication processing\n- Domain-aware redirect logic (customer subdomain vs main domain)\n- Error handling with user-friendly messages\n- Automatic cleanup and navigation after successful auth\n\n### 4. Router Integration (`client/src/App.tsx`)\n- Added `/auth/callback` route for handling cross-domain redirects\n- Proper lazy loading with auth-specific loading states\n\n### 5. Cross-Domain Features Implemented\n- **Domain Detection**: Automatic identification of main domain vs customer subdomains\n- **Session Transfer**: Secure transfer of authentication state between domains\n- **Redirect Handling**: Seamless redirects from main site to customer subdomains\n- **State Cleanup**: Automatic cleanup of temporary auth state\n- **Error Recovery**: Graceful handling of failed cross-domain transfers\n- **Security**: 5-minute expiration on transferred auth state\n\n### 6. Supported Authentication Flows\n- **Same-Domain**: Standard login within single domain\n- **Cross-Domain Redirect**: Login on main site, redirect to customer subdomain\n- **Direct Subdomain**: Direct login on customer subdomain\n- **Callback Handling**: Process OAuth and redirect-based authentication\n- **Universal Logout**: Sign out from all domains simultaneously\n\n### 7. Domain Support\n- Main domain: `www.agent-factory.io`\n- Customer subdomains: `*.agent-factory.app`\n- Local development: `localhost` and `127.0.0.1`\n- API domain: `api.agent-factory.io`", "testStrategy": "Verify the following authentication flows:\n\n1. <PERSON><PERSON> on main site and verify authentication state persists on customer subdomains\n2. Direct login on customer subdomains\n3. Single sign-out across all domains\n4. OAuth authentication flows with proper redirects\n5. Session expiration and auto-refresh across domains\n6. Error handling for failed authentication attempts\n7. Test with all supported domains:\n   - Main domain: `www.agent-factory.io`\n   - Customer subdomains: `*.agent-factory.app`\n   - Local development: `localhost` and `127.0.0.1`\n   - API domain: `api.agent-factory.io`", "subtasks": [{"id": 11.1, "title": "Configure Supabase Client for Cross-Domain Auth", "description": "Implement enhanced Supabase client with PKCE flow and custom storage handlers", "status": "completed"}, {"id": 11.2, "title": "Update AuthProvider Component", "description": "En<PERSON><PERSON> with cross-domain session handling and domain-aware methods", "status": "completed"}, {"id": 11.3, "title": "Create Authentication Callback Page", "description": "Implement dedicated page for handling cross-domain auth redirects with proper feedback", "status": "completed"}, {"id": 11.4, "title": "Integrate with Router", "description": "Add auth callback route and implement lazy loading with auth-specific states", "status": "completed"}, {"id": 11.5, "title": "Test Cross-Domain Authentication Flows", "description": "Verify all authentication scenarios across main domain and customer subdomains", "status": "completed"}]}, {"id": 12, "title": "Implement Subdomain Routing", "description": "Create routing system to handle tenant-specific subdomains and extract tenant information", "status": "done", "dependencies": [6, 10], "priority": "medium", "details": "## Implementation Complete\n\n### 1. Frontend Tenant Context System (`client/src/components/TenantProvider.tsx`)\n- Tenant context provider with React Context API\n- Automatic subdomain detection using crossDomainAuth utilities\n- Tenant lookup from API based on subdomain\n- Loading states and error handling\n- Automatic refetch when user authentication changes\n- Utility hooks: `useTenant()`, `useIsOnTenantSubdomain()`, `useCurrentSubdomain()`\n\n### 2. Backend API Endpoint (`server/routes.ts`)\n- `/api/tenants/by-subdomain/:subdomain` endpoint for tenant lookup\n- Public endpoint (no auth required) for initial tenant detection\n- Proper error handling for missing/invalid subdomains\n- Returns tenant information excluding sensitive data\n- Active tenant filtering for security\n\n### 3. Enhanced API Client (`client/src/lib/apiClient.ts`)\n- Automatic tenant ID inclusion in API requests via `X-Tenant-ID` header\n- Tenant-aware HTTP client with proper auth integration\n- Dedicated tenant methods: `getTenantBySubdomain()`, `getTenantContext()`\n- Seamless integration with existing API methods\n\n### 4. Visual Tenant Indicator (`client/src/components/TenantBanner.tsx`)\n- Tenant information banner for customer subdomains\n- Shows tenant name, subdomain, status, and plan\n- Loading states and error handling\n- Only displays on customer subdomains (not main domain)\n- Professional styling with status badges\n\n### 5. App Integration (`client/src/App.tsx`)\n- TenantProvider integrated into app hierarchy\n- Proper nesting within AuthProvider for user context access\n- Available throughout the entire application\n\n### 6. Existing Backend Integration\n- Leverages existing tenant middleware in `server/routes.ts`\n- Subdomain parsing already implemented in `requireTenantContext`\n- Tenant lookup from `tenants` table by `slug` field\n- Proper access control and user membership validation\n\n### 7. Subdomain Routing Features\n- Automatic Detection: Frontend automatically detects customer subdomains\n- Tenant Lookup: Real-time tenant information fetching\n- Context Propagation: Tenant context available throughout the app\n- Error Handling: Graceful handling of invalid/missing tenants\n- Visual Feedback: Clear indication of current tenant context\n- API Integration: Automatic tenant header inclusion in API requests\n\n### 8. Supported Domain Patterns\n- Main domain: `www.agent-factory.io` (no tenant context)\n- Customer subdomains: `*.agent-factory.app` (tenant context active)\n- API domain: `api.agent-factory.io` (backend routing)\n- Local development: `localhost` (development mode)", "testStrategy": "Test subdomain routing with various tenant subdomains. Verify tenant context is correctly set and propagated throughout the application. Test with invalid subdomains to ensure proper error handling. Verify the TenantBanner component displays correctly on tenant subdomains and is hidden on the main domain. Test API requests to ensure the X-Tenant-ID header is properly included. Verify the utility hooks (useTenant, useIsOnTenantSubdomain, useCurrentSubdomain) return the expected values in different contexts.", "subtasks": [{"id": 12.1, "title": "Implement Frontend Tenant Context System", "status": "completed", "description": "Created TenantProvider.tsx with React Context API, automatic subdomain detection, tenant lookup, loading states, error handling, and utility hooks."}, {"id": 12.2, "title": "Implement Backend API Endpoint for Tenant Lookup", "status": "completed", "description": "Created /api/tenants/by-subdomain/:subdomain endpoint with proper error handling, tenant filtering, and security considerations."}, {"id": 12.3, "title": "Enhance API Client with Tenant Awareness", "status": "completed", "description": "Updated apiClient.ts to include X-Tenant-ID header automatically and added dedicated tenant methods."}, {"id": 12.4, "title": "Create Visual Tenant Indicator Component", "status": "completed", "description": "Implemented TenantBanner.tsx to display tenant information on customer subdomains with proper styling and status indicators."}, {"id": 12.5, "title": "Integrate Tenant Context into App Hierarchy", "status": "completed", "description": "Added TenantProvider to App.tsx with proper nesting within AuthProvider."}, {"id": 12.6, "title": "Configure DNS and SSL for Subdomains", "status": "completed", "description": "Set up wildcard DNS entries and SSL certificates for *.agent-factory.app subdomains."}]}, {"id": 13, "title": "Create Customer Provisioning System", "description": "Implement system for manually provisioning new customer tenants", "details": "1. Create tenant management database tables\n2. Implement API endpoints for tenant CRUD operations\n3. Create admin interface for tenant management\n4. Implement subdomain registration and validation\n5. Create user assignment to tenants\n6. Implement role assignment within tenants\n7. Example tenant creation function:\n```javascript\nasync function createTenant(tenantData, adminUserId) {\n  const { name, subdomain, plan } = tenantData;\n  \n  // Start a transaction\n  const { data, error } = await supabase.rpc('create_new_tenant', {\n    tenant_name: name,\n    tenant_subdomain: subdomain,\n    tenant_plan: plan,\n    admin_user_id: adminUserId\n  });\n  \n  if (error) throw error;\n  \n  return data;\n}\n\n// Example stored procedure in PostgreSQL\n/*\nCREATE OR REPLACE FUNCTION create_new_tenant(\n  tenant_name TEXT,\n  tenant_subdomain TEXT,\n  tenant_plan TEXT,\n  admin_user_id UUID\n) RETURNS UUID AS $$\nDECLARE\n  new_tenant_id UUID;\nBEGIN\n  -- Insert new tenant\n  INSERT INTO tenants (name, subdomain, plan, created_at)\n  VALUES (tenant_name, tenant_subdomain, tenant_plan, NOW())\n  RETURNING id INTO new_tenant_id;\n  \n  -- Assign admin user to tenant\n  INSERT INTO tenant_users (tenant_id, user_id, role, created_at)\n  VALUES (new_tenant_id, admin_user_id, 'admin', NOW());\n  \n  -- Create initial resources for tenant\n  -- (Add any default data the tenant needs)\n  \n  RETURN new_tenant_id;\nEND;\n$$ LANGUAGE plpgsql SECURITY DEFINER;\n*/\n```", "testStrategy": "Test tenant creation process end-to-end. Verify subdomain validation works correctly. Test user assignment to tenants with different roles. Verify tenant isolation after provisioning.", "priority": "medium", "dependencies": [3, 12], "status": "done", "subtasks": []}, {"id": 14, "title": "Implement Role-Based Access Control", "description": "Maintain and enhance role-based access control within tenant context", "details": "1. Design role-based access control schema\n2. Implement role assignment within tenants\n3. Create permission checking middleware\n4. Update frontend components to respect permissions\n5. Implement role management UI for tenant admins\n6. Example RBAC implementation:\n```javascript\n// Backend permission middleware\nconst checkPermission = (requiredPermission) => {\n  return async (req, res, next) => {\n    if (!req.user || !req.tenantId) {\n      return res.status(401).json({ error: 'Authentication required' });\n    }\n    \n    // Get user's role in this tenant\n    const { data, error } = await supabase\n      .from('tenant_users')\n      .select('role')\n      .eq('tenant_id', req.tenantId)\n      .eq('user_id', req.user.id)\n      .single();\n    \n    if (error || !data) {\n      return res.status(403).json({ error: 'Access denied' });\n    }\n    \n    // Check if role has the required permission\n    const { data: permissions, error: permError } = await supabase\n      .from('role_permissions')\n      .select('permission')\n      .eq('role', data.role)\n      .eq('permission', requiredPermission);\n    \n    if (permError || permissions.length === 0) {\n      return res.status(403).json({ error: 'Insufficient permissions' });\n    }\n    \n    next();\n  };\n};\n\n// Usage in routes\napp.post('/api/projects', \n  authMiddleware, \n  tenantMiddleware, \n  checkPermission('create:projects'), \n  async (req, res) => {\n    // Create project logic\n  }\n);\n\n// Frontend permission hook\nexport function usePermission(requiredPermission) {\n  const { user } = useAuth();\n  const { currentTenant } = useTenant();\n  const [hasPermission, setHasPermission] = useState(false);\n  \n  useEffect(() => {\n    if (!user || !currentTenant) {\n      setHasPermission(false);\n      return;\n    }\n    \n    // Check permission from API\n    fetch(`https://api.agent-factory.io/permissions/check?permission=${requiredPermission}`)\n      .then(res => res.json())\n      .then(data => setHasPermission(data.hasPermission))\n      .catch(() => setHasPermission(false));\n  }, [user, currentTenant, requiredPermission]);\n  \n  return hasPermission;\n}\n```", "testStrategy": "Test permission checks with different user roles. Verify UI elements are correctly shown/hidden based on permissions. Test permission assignment and management functionality.", "priority": "medium", "dependencies": [5, 13], "status": "done", "subtasks": []}, {"id": 15, "title": "Implement API Rate Limiting per Tenant", "description": "Add rate limiting to API endpoints based on tenant plans and usage", "details": "1. Design rate limiting strategy based on tenant plans\n2. Implement rate limiting middleware\n3. Create rate limit storage in Redis or similar\n4. Add tenant plan information to database\n5. Implement rate limit headers in responses\n6. Create monitoring for rate limit usage\n7. Example implementation using Express Rate Limit:\n```javascript\nconst rateLimit = require('express-rate-limit');\nconst RedisStore = require('rate-limit-redis');\nconst redis = require('redis');\n\nconst redisClient = redis.createClient(process.env.REDIS_URL);\n\n// Create tenant-aware rate limiter\nconst tenantRateLimit = (options = {}) => {\n  return async (req, res, next) => {\n    if (!req.tenantId) {\n      return next();\n    }\n    \n    // Get tenant plan limits\n    const { data, error } = await supabase\n      .from('tenants')\n      .select('plan')\n      .eq('id', req.tenantId)\n      .single();\n    \n    if (error) {\n      return next(error);\n    }\n    \n    // Set rate limits based on plan\n    let rateOptions = {};\n    switch (data.plan) {\n      case 'free':\n        rateOptions = { max: 100, windowMs: 60 * 1000 }; // 100 requests per minute\n        break;\n      case 'pro':\n        rateOptions = { max: 1000, windowMs: 60 * 1000 }; // 1000 requests per minute\n        break;\n      case 'enterprise':\n        rateOptions = { max: 10000, windowMs: 60 * 1000 }; // 10000 requests per minute\n        break;\n      default:\n        rateOptions = { max: 50, windowMs: 60 * 1000 }; // Default fallback\n    }\n    \n    // Create and apply rate limiter\n    const limiter = rateLimit({\n      ...rateOptions,\n      ...options,\n      store: new RedisStore({\n        client: redisClient,\n        prefix: `rate-limit:${req.tenantId}:`\n      }),\n      keyGenerator: (req) => req.tenantId,\n      standardHeaders: true,\n      legacyHeaders: false,\n    });\n    \n    return limiter(req, res, next);\n  };\n};\n\n// Apply to routes\napp.use('/api/', tenantRateLimit());\n```", "testStrategy": "Test rate limiting with different tenant plans. Verify rate limit headers are correctly included in responses. Test rate limit exceeded scenarios and error handling.", "priority": "low", "dependencies": [8, 13], "status": "pending", "subtasks": []}, {"id": 16, "title": "Set Up Monitoring and Logging for Multi-Tenant Operations", "description": "Implement a system to monitor workspace activities, resource consumption, and provide usage analytics.", "status": "pending", "dependencies": [8, 10], "priority": "medium", "details": "1. Define and create new database tables (e.g., `workspace_events`, `workspace_usage_metrics`) to store monitoring data\n2. Create a logging service or middleware that captures key workspace events (e.g., creation, deletion, resource access, API calls within the workspace) with tenant context\n3. Implement backend logic to aggregate and process usage data into meaningful metrics (e.g., daily active users, API call volume, storage used) per tenant\n4. Develop new API endpoints to expose this usage data\n5. Create a new admin dashboard page to visualize workspace analytics and monitoring information\n6. Implement error tracking with tenant information\n7. Set up alerting for tenant-specific issues\n8. Implement audit logging for sensitive operations\n\nExample logging implementation:\n```javascript\nconst winston = require('winston');\n\n// Create tenant-aware logger\nconst createTenantLogger = (req) => {\n  return winston.createLogger({\n    level: 'info',\n    format: winston.format.combine(\n      winston.format.timestamp(),\n      winston.format.json()\n    ),\n    defaultMeta: { \n      tenantId: req.tenantId || 'system',\n      workspaceId: req.workspaceId,\n      userId: req.user?.id || 'anonymous',\n      requestId: req.id // Assuming request ID middleware\n    },\n    transports: [\n      new winston.transports.Console(),\n      new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),\n      new winston.transports.File({ filename: 'logs/combined.log' })\n    ],\n  });\n};\n\n// Middleware to add logger to request\napp.use((req, res, next) => {\n  req.logger = createTenantLogger(req);\n  \n  // Log request\n  req.logger.info('API Request', {\n    method: req.method,\n    path: req.path,\n    ip: req.ip\n  });\n  \n  // Log response\n  const originalSend = res.send;\n  res.send = function(body) {\n    req.logger.info('API Response', {\n      statusCode: res.statusCode,\n      responseTime: Date.now() - req.startTime\n    });\n    return originalSend.call(this, body);\n  };\n  \n  next();\n});\n\n// Usage in route handlers\napp.get('/api/workspaces/:workspaceId/resources', authMiddleware, tenantMiddleware, async (req, res) => {\n  try {\n    req.logger.info('Fetching workspace resources', { workspaceId: req.params.workspaceId });\n    // ... handler logic\n  } catch (error) {\n    req.logger.error('Error fetching workspace resources', { error: error.message, workspaceId: req.params.workspaceId });\n    res.status(500).json({ error: error.message });\n  }\n});\n```", "testStrategy": "1. Verify logs contain correct tenant and workspace context\n2. Test database tables are correctly storing workspace events and metrics\n3. Verify API endpoints return accurate usage analytics\n4. Test error tracking by triggering various error conditions\n5. Verify performance metrics are correctly captured per tenant and workspace\n6. Test the admin dashboard displays accurate workspace analytics", "subtasks": [{"id": 1, "title": "Design and Implement Monitoring Data Storage", "description": "Define and create database tables (e.g., workspace_events, workspace_usage_metrics) to store monitoring and usage data with tenant isolation.", "dependencies": [], "details": "Ensure schema supports multi-tenant data isolation and efficient querying for analytics and monitoring.\n<info added on 2025-06-21T00:14:33.844Z>\nSchema will focus on tracking user interactions with the workspace management features, not application-level data from deployed sites. The database design should include fields for user ID, workspace ID, action type, timestamp, and relevant metadata. Ensure proper indexing for efficient querying of usage patterns and trends. The schema should maintain multi-tenant isolation while allowing aggregated reporting across the platform for internal analytics purposes.\n</info added on 2025-06-21T00:14:33.844Z>", "status": "pending", "testStrategy": "Verify tables are created, data is correctly partitioned by tenant, and test with sample inserts and queries."}, {"id": 2, "title": "Develop Tenant-Aware Logging Middleware", "description": "Create a logging service or middleware that captures key workspace events (creation, deletion, resource access, API calls) with tenant context.", "dependencies": [1], "details": "Integrate middleware into the application stack to log events with tenant, workspace, and user identifiers.\n<info added on 2025-06-21T00:15:44.077Z>\nThis middleware is for internal analytics only and will not be deployed to customer sites. The logging service will capture tenant, workspace, and user identifiers along with key interaction events to provide comprehensive visibility into platform usage patterns. This data will support the monitoring infrastructure being developed in the parent task and feed into the usage data aggregation system planned in subtask 16.3.\n</info added on 2025-06-21T00:15:44.077Z>", "status": "pending", "testStrategy": "Simulate workspace events and confirm logs include correct tenant and workspace context."}, {"id": 3, "title": "Implement Usage Data Aggregation and Analytics", "description": "Build backend logic to aggregate and process raw monitoring data into meaningful per-tenant metrics (e.g., daily active users, API call volume, storage used).", "dependencies": [1, 2], "details": "Ensure analytics logic respects tenant boundaries and supports efficient metric calculation.\n<info added on 2025-06-21T00:16:38.878Z>\nAnalytics will help us understand how customers use our management tools. The implementation should focus on collecting and processing interaction data while maintaining tenant isolation. Key metrics should include feature adoption rates, user engagement patterns, and platform usage trends. This data will provide valuable insights for product improvements and customer success initiatives. The analytics system should be designed to scale with increasing tenant count and data volume, with consideration for efficient storage and retrieval of historical metrics.\n</info added on 2025-06-21T00:16:38.878Z>", "status": "pending", "testStrategy": "Run aggregation jobs on test data and validate output metrics for accuracy and tenant isolation."}, {"id": 4, "title": "Expose Monitoring and Analytics via API Endpoints", "description": "Develop secure API endpoints to expose usage and monitoring data for each tenant.", "dependencies": [3], "details": "Endpoints must enforce tenant-level access control and return only relevant data.\n<info added on 2025-06-21T00:16:51.068Z>\nThese endpoints will be protected and only accessible to our internal admin roles. The API will provide access to the aggregated usage data collected in subtask 16.3, ensuring proper authentication and authorization checks are in place. Endpoints should include filtering capabilities by tenant, time period, and usage metrics to support the requirements of the admin dashboard being built in subtask 16.5. All API calls must be logged for security auditing purposes.\n</info added on 2025-06-21T00:16:51.068Z>", "status": "pending", "testStrategy": "Test endpoints with users from different tenants to ensure correct data isolation and access."}, {"id": 5, "title": "Build Admin Dashboard for Multi-Tenant Analytics", "description": "Create an admin dashboard page to visualize workspace analytics and monitoring information, supporting tenant-level filtering and drill-down.", "dependencies": [4], "details": "Dashboard should display key metrics, trends, and allow admins to investigate tenant-specific activity.\n<info added on 2025-06-21T00:17:04.036Z>\nThe dashboard will display metrics like feature adoption, login frequency, and configuration trends. It should provide a comprehensive view of tenant activities with filtering capabilities to isolate specific workspaces or user segments. Key visualization components should include:\n\n1. Feature adoption rates across different tenant tiers\n2. User engagement metrics (daily/weekly active users, session duration)\n3. Configuration preference trends to identify popular settings\n4. Login frequency and usage patterns over time\n5. Comparative analytics between tenants to identify outliers\n\nThe dashboard should support drill-down capabilities to investigate specific tenant behaviors and export functionality for reporting purposes. Integration with the monitoring and analytics API endpoints will be essential for real-time data display.\n</info added on 2025-06-21T00:17:04.036Z>", "status": "pending", "testStrategy": "Populate dashboard with sample data and validate visualizations, filters, and access controls."}]}, {"id": 17, "title": "Implement Backup and Recovery Procedures", "description": "Create tenant-aware backup and recovery procedures for the multi-tenant database", "details": "1. Design backup strategy for multi-tenant data\n2. Implement automated backup procedures\n3. Create tenant-specific restore capabilities\n4. Test recovery procedures\n5. Document backup and recovery processes\n6. Set up monitoring for backup jobs\n7. Example backup script:\n```javascript\nconst { exec } = require('child_process');\nconst { promisify } = require('util');\nconst execAsync = promisify(exec);\n\nasync function backupTenantData(tenantId) {\n  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');\n  const filename = `backup-${tenantId}-${timestamp}.sql`;\n  \n  try {\n    // Set up connection to Supabase PostgreSQL\n    const connectionString = process.env.SUPABASE_CONNECTION_STRING;\n    \n    // Create backup with tenant filter\n    await execAsync(`PGPASSWORD=${process.env.DB_PASSWORD} pg_dump \\\n      --host=${process.env.DB_HOST} \\\n      --username=${process.env.DB_USER} \\\n      --dbname=${process.env.DB_NAME} \\\n      --schema=public \\\n      --data-only \\\n      --column-inserts \\\n      --table='*' \\\n      --where='tenant_id = \\'${tenantId}\\'' \\\n      > ./backups/${filename}`\n    );\n    \n    console.log(`Backup created: ${filename}`);\n    return filename;\n  } catch (error) {\n    console.error(`Backup failed for tenant ${tenantId}:`, error);\n    throw error;\n  }\n}\n\n// Schedule regular backups\nconst cron = require('node-cron');\n\n// Run daily at 2 AM\ncron.schedule('0 2 * * *', async () => {\n  try {\n    // Get all tenants\n    const { data: tenants, error } = await supabase\n      .from('tenants')\n      .select('id');\n    \n    if (error) throw error;\n    \n    // Backup each tenant's data\n    for (const tenant of tenants) {\n      await backupTenantData(tenant.id);\n    }\n    \n    console.log('All tenant backups completed successfully');\n  } catch (error) {\n    console.error('Tenant backup job failed:', error);\n  }\n});\n```", "testStrategy": "Test backup creation for specific tenants. Verify restore process works correctly and maintains tenant isolation. Test scheduled backup jobs to ensure they run correctly.", "priority": "medium", "dependencies": [2, 3], "status": "pending", "subtasks": []}, {"id": 18, "title": "Implement SSL Certificate Management", "description": "Set up SSL certificate management for main domain and customer subdomains", "details": "1. Configure wildcard SSL certificate for *.agent-factory.app\n2. Set up automated certificate renewal\n3. Implement certificate deployment process\n4. Configure HTTPS server settings\n5. Test SSL configuration across domains\n6. Document certificate management procedures\n7. Example using Let's Encrypt with Certbot:\n```bash\n# Install Certbot\napt-get update\napt-get install certbot python3-certbot-nginx\n\n# Request wildcard certificate\ncertbot certonly --manual \\\n  --preferred-challenges=dns \\\n  --email <EMAIL> \\\n  --server https://acme-v02.api.letsencrypt.org/directory \\\n  --agree-tos \\\n  -d agent-factory.app -d *.agent-factory.app\n\n# Set up auto-renewal\necho \"0 0,12 * * * root python -c 'import random; import time; time.sleep(random.random() * 3600)' && certbot renew\" | sudo tee -a /etc/crontab > /dev/null\n```\n\nNode.js HTTPS server configuration:\n```javascript\nconst https = require('https');\nconst fs = require('fs');\nconst express = require('express');\nconst app = express();\n\n// SSL certificate paths\nconst privateKey = fs.readFileSync('/etc/letsencrypt/live/agent-factory.app/privkey.pem', 'utf8');\nconst certificate = fs.readFileSync('/etc/letsencrypt/live/agent-factory.app/cert.pem', 'utf8');\nconst ca = fs.readFileSync('/etc/letsencrypt/live/agent-factory.app/chain.pem', 'utf8');\n\nconst credentials = {\n  key: privateKey,\n  cert: certificate,\n  ca: ca\n};\n\n// Create HTTPS server\nconst httpsServer = https.createServer(credentials, app);\n\nhttpsServer.listen(443, () => {\n  console.log('HTTPS Server running on port 443');\n});\n```", "testStrategy": "Verify SSL certificates work for main domain and various subdomains. Test certificate renewal process. Check SSL configuration using online tools like SSL Labs.", "priority": "medium", "dependencies": [12], "status": "cancelled", "subtasks": []}, {"id": 19, "title": "Create User Migration Process", "description": "Document future user migration strategy for Supabase Auth (no immediate migration required)", "status": "done", "dependencies": [7, 9], "priority": "low", "details": "**Current Status: No Migration Required**\n\n- The application is still in development phase with no existing production users\n- Fresh Supabase Auth implementation with clean user database\n- All new users will be created directly in Supabase Auth system\n- No legacy authentication system to migrate from\n\n**Future Migration Strategy (for reference):**\nWhen the application eventually needs user migration in the future, the strategy would involve:\n\n1. **Data Extraction**: Extract existing user data from source authentication system\n2. **User Creation**: Use Supabase Admin API to create users with `createUser()`\n3. **Password Handling**: Generate temporary passwords or migrate existing hashes if compatible\n4. **Metadata Migration**: Preserve user profiles, roles, and tenant relationships\n5. **Notification System**: Inform users about migration and password reset requirements\n6. **Verification**: Ensure all user data and relationships are correctly migrated\n7. **Testing**: Comprehensive testing of migrated user authentication and permissions\n\n**Migration Script Template (for future use):**\n```javascript\nconst { createClient } = require('@supabase/supabase-js');\n\nconst supabase = createClient(\n  process.env.SUPABASE_URL,\n  process.env.SUPABASE_SERVICE_KEY\n);\n\nasync function migrateUsers() {\n  // Extract users from legacy system\n  const existingUsers = await fetchExistingUsers();\n  \n  for (const user of existingUsers) {\n    // Create user in Supabase Auth\n    const { data, error } = await supabase.auth.admin.createUser({\n      email: user.email,\n      password: generateTemporaryPassword(),\n      email_confirm: true,\n      user_metadata: {\n        first_name: user.firstName,\n        last_name: user.lastName,\n        migrated_from_legacy: true,\n        original_user_id: user.id\n      }\n    });\n    \n    // Migrate tenant relationships\n    await migrateTenantMemberships(user.id, data.user.id);\n  }\n}\n```", "testStrategy": "No immediate testing required as migration is not needed at this stage. For future reference: Test user migration with sample user data. Verify users can log in after migration. Test user relationships and permissions are preserved. Verify user metadata is correctly migrated.", "subtasks": [{"id": "19.1", "title": "Document future migration strategy", "status": "completed", "description": "Document the approach for future user migrations when needed"}, {"id": "19.2", "title": "Create reference migration script template", "status": "completed", "description": "Develop a template script that can be adapted for future migration needs"}]}, {"id": 20, "title": "Implement End-to-End Testing for Multi-Tenant Functionality", "description": "Create comprehensive end-to-end tests for the multi-tenant architecture", "details": "1. Set up end-to-end testing framework (<PERSON><PERSON>, Playwright, etc.)\n2. Create test scenarios for multi-tenant functionality\n3. Implement tests for cross-domain authentication\n4. Test tenant isolation and data security\n5. Create performance tests for multi-tenant scenarios\n6. Implement CI/CD pipeline for automated testing\n7. Example Cypress test for multi-tenant authentication:\n```javascript\n// cypress/integration/multi-tenant-auth.spec.js\ndescribe('Multi-Tenant Authentication', () => {\n  const mainSite = 'https://www.agent-factory.io';\n  const tenant1 = 'https://customer1.agent-factory.app';\n  const tenant2 = 'https://customer2.agent-factory.app';\n  const testUser = {\n    email: '<EMAIL>',\n    password: 'securePassword123'\n  };\n  \n  beforeEach(() => {\n    // Clear cookies and localStorage before each test\n    cy.clearCookies();\n    cy.clearLocalStorage();\n  });\n  \n  it('should login on main site and maintain session on tenant subdomain', () => {\n    // Login on main site\n    cy.visit(`${mainSite}/login`);\n    cy.get('#email').type(testUser.email);\n    cy.get('#password').type(testUser.password);\n    cy.get('button[type=\"submit\"]').click();\n    \n    // Verify login successful\n    cy.url().should('include', '/dashboard');\n    cy.get('.user-profile').should('contain', testUser.email);\n    \n    // Visit tenant subdomain\n    cy.visit(tenant1);\n    \n    // Should be automatically logged in\n    cy.get('.loading-auth').should('not.exist');\n    cy.get('.user-profile').should('contain', testUser.email);\n    \n    // Check tenant context\n    cy.get('.tenant-name').should('contain', 'Customer 1');\n  });\n  \n  it('should maintain tenant isolation', () => {\n    // Login on main site\n    cy.visit(`${mainSite}/login`);\n    cy.get('#email').type(testUser.email);\n    cy.get('#password').type(testUser.password);\n    cy.get('button[type=\"submit\"]').click();\n    \n    // Visit tenant 1 and create a project\n    cy.visit(`${tenant1}/projects/new`);\n    cy.get('#project-name').type('Tenant 1 Project');\n    cy.get('button[type=\"submit\"]').click();\n    \n    // Verify project created\n    cy.get('.project-list').should('contain', 'Tenant 1 Project');\n    \n    // Visit tenant 2\n    cy.visit(`${tenant2}/projects`);\n    \n    // Should not see tenant 1's project\n    cy.get('.project-list').should('not.contain', 'Tenant 1 Project');\n  });\n  \n  it('should logout from all domains when logging out', () => {\n    // Login on main site\n    cy.visit(`${mainSite}/login`);\n    cy.get('#email').type(testUser.email);\n    cy.get('#password').type(testUser.password);\n    cy.get('button[type=\"submit\"]').click();\n    \n    // Logout\n    cy.get('.logout-button').click();\n    \n    // Verify logged out\n    cy.url().should('include', '/login');\n    \n    // Visit tenant subdomain\n    cy.visit(tenant1);\n    \n    // Should be logged out\n    cy.url().should('include', '/login');\n  });\n});\n```", "testStrategy": "Run end-to-end tests across different environments (development, staging, production). Test with different user roles and tenant configurations. Verify all critical user flows work correctly in the multi-tenant architecture.", "priority": "medium", "dependencies": [11, 12, 13, 14], "status": "pending", "subtasks": []}]}