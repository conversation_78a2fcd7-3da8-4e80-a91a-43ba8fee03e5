import { useMutation, useQueryClient } from "@tanstack/react-query";
import { loginUser, registerUser, logoutUser } from "@/lib/auth";
import { useToast } from "@/hooks/use-toast";
import { useAuthContext } from "@/components/auth/AuthProvider";
import type { LoginCredentials, InsertUserWithPassword } from "@shared/schema";

export function useAuth() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const { user, isLoading, session } = useAuthContext(); // Get state from context

  const loginMutation = useMutation({
    mutationFn: loginUser,
    onSuccess: (user) => {
      // AuthProvider's listener handles the state update automatically.
      toast({
        title: "Welcome back!",
        description: `Logged in as ${user.firstName} ${user.lastName}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Login failed", 
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const registerMutation = useMutation({
    mutationFn: registerUser,
    onSuccess: (user) => {
      // AuthProvider's listener handles the state update automatically.
      toast({
        title: "Account created!",
        description: `Welcome ${user.firstName} ${user.lastName}`,
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Registration failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const logoutMutation = useMutation({
    mutationFn: logoutUser,
    onSuccess: () => {
      // AuthProvider's listener handles the state update.
      // Clear the query cache to remove all protected data.
      queryClient.clear();
      toast({
        title: "Logged out",
        description: "You have been successfully logged out",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Logout failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  return {
    user,
    session,
    isLoading,
    error: null, // useQuery error is removed. Can add mutation errors if needed.
    login: loginMutation.mutate,
    register: registerMutation.mutate,
    logout: logoutMutation.mutate,
    isLoggingIn: loginMutation.isPending,
    isRegistering: registerMutation.isPending,
    isLoggingOut: logoutMutation.isPending,
  };
}
