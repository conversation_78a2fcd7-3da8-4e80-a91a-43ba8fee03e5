import { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface SEOContextType {
  updateSEO: (config: SEOConfig) => void;
  currentSEO: SEOConfig;
}

interface SEOConfig {
  title?: string;
  description?: string;
  keywords?: string;
  image?: string;
  url?: string;
  type?: 'website' | 'article' | 'product' | 'profile';
  author?: string;
  publishedTime?: string;
  modifiedTime?: string;
  canonical?: string;
  noindex?: boolean;
}

const defaultSEO: SEOConfig = {
  title: 'Agent Factory Pro - Enterprise Business Automation Platform',
  description: 'Transform your business with our comprehensive automation platform. Streamline workflows, enhance productivity, and scale operations with enterprise-grade tools.',
  keywords: 'business automation, enterprise software, workflow management, productivity tools',
  image: 'https://agentfactory.pro/og-image.jpg',
  url: 'https://agentfactory.pro',
  type: 'website',
  noindex: false
};

const SEOContext = createContext<SEOContextType | null>(null);

export function SEOProvider({ children }: { children: ReactNode }) {
  const [currentSEO, setCurrentSEO] = useState<SEOConfig>(defaultSEO);

  const updateSEO = (config: SEOConfig) => {
    setCurrentSEO(prev => ({ ...prev, ...config }));
  };

  useEffect(() => {
    // Update document title
    if (currentSEO.title) {
      document.title = currentSEO.title;
    }

    // Update meta tags
    const updateMetaTag = (name: string, content: string, property?: boolean) => {
      if (!content) return;
      
      const selector = property ? `meta[property="${name}"]` : `meta[name="${name}"]`;
      let meta = document.querySelector(selector) as HTMLMetaElement;
      
      if (!meta) {
        meta = document.createElement('meta');
        if (property) {
          meta.setAttribute('property', name);
        } else {
          meta.setAttribute('name', name);
        }
        document.head.appendChild(meta);
      }
      meta.setAttribute('content', content);
    };

    // Basic meta tags
    updateMetaTag('description', currentSEO.description || '');
    updateMetaTag('keywords', currentSEO.keywords || '');
    updateMetaTag('robots', currentSEO.noindex ? 'noindex, nofollow' : 'index, follow');
    
    // Open Graph tags
    updateMetaTag('og:title', currentSEO.title || '', true);
    updateMetaTag('og:description', currentSEO.description || '', true);
    updateMetaTag('og:type', currentSEO.type || 'website', true);
    updateMetaTag('og:image', currentSEO.image || '', true);
    updateMetaTag('og:url', currentSEO.url || '', true);
    updateMetaTag('og:site_name', 'Agent Factory Pro', true);
    
    // Twitter Card tags
    updateMetaTag('twitter:card', 'summary_large_image');
    updateMetaTag('twitter:title', currentSEO.title || '');
    updateMetaTag('twitter:description', currentSEO.description || '');
    updateMetaTag('twitter:image', currentSEO.image || '');
    
    // Article-specific meta tags
    if (currentSEO.type === 'article') {
      if (currentSEO.author) updateMetaTag('article:author', currentSEO.author, true);
      if (currentSEO.publishedTime) updateMetaTag('article:published_time', currentSEO.publishedTime, true);
      if (currentSEO.modifiedTime) updateMetaTag('article:modified_time', currentSEO.modifiedTime, true);
    }

    // Canonical URL
    if (currentSEO.canonical) {
      let canonical = document.querySelector('link[rel="canonical"]') as HTMLLinkElement;
      if (!canonical) {
        canonical = document.createElement('link');
        canonical.setAttribute('rel', 'canonical');
        document.head.appendChild(canonical);
      }
      canonical.setAttribute('href', currentSEO.canonical);
    }

  }, [currentSEO]);

  return (
    <SEOContext.Provider value={{ updateSEO, currentSEO }}>
      {children}
    </SEOContext.Provider>
  );
}

export function useSEO() {
  const context = useContext(SEOContext);
  if (!context) {
    throw new Error('useSEO must be used within SEOProvider');
  }
  return context;
}

// Hook for page-specific SEO
export function usePageSEO(config: SEOConfig) {
  const { updateSEO } = useSEO();
  
  useEffect(() => {
    updateSEO(config);
    
    // Cleanup function to reset to default when component unmounts
    return () => {
      updateSEO(defaultSEO);
    };
  }, [updateSEO]); // Remove config dependency to prevent infinite loops
}