---
description: How to communicate with other AI coders on this project
globs: 
alwaysApply: false
---
# Communication Protocols

## Overview

The communication protocols establish standardized methods for information exchange between you (the project coordinator), <PERSON><PERSON><PERSON> (the code reviewer and task manager), and <PERSON><PERSON> (the builder). These protocols address the critical challenges of context retention, consistent guidance, and effective human-in-the-loop intervention that you identified as pain points in your current workflow. By formalizing communication patterns and documentation requirements, these protocols ensure that all participants maintain a shared understanding of the project status, requirements, and decisions throughout the development lifecycle.

## Detailed Procedure

### Repository-Based Communication Structure

Establish a structured communication system within the repository that serves as the central source of truth for project information. This structure includes dedicated documentation files that capture different aspects of the project:

1. **README.md**: The primary entry point that provides an overview of the project, its structure, and links to other key documentation.
2. **CONTEXT.md**: A living document that captures the current state of development, recent decisions, and ongoing challenges.
3. **DECISIONS.md**: A record of architectural and design decisions, including the rationale behind each choice.
4. **TASKS.md**: A prioritized list of development tasks with status indicators and dependencies.
5. **REVIEW_FEEDBACK.md**: Documentation of code review findings and recommendations.
6. **IMPLEMENTATION_PLAN.md**: Structured plans for implementing feedback and improvements.

This repository-based communication structure ensures that critical information is persistently available to all participants, reducing the risk of context loss between sessions.

### Context Retention Mechanisms

Implement specific mechanisms to maintain context across sessions with both AI agents. These mechanisms include:

1. **Session Summaries**: At the end of each working session with either Cursor or Replit, document a brief summary of what was accomplished, challenges encountered, and next steps in the CONTEXT.md file.
2. **Context Restoration Prompts**: Begin each new session with a standardized prompt that references the CONTEXT.md file and other relevant documentation to reestablish the project context.
3. **Progressive Documentation**: Continuously update documentation files as the project evolves, ensuring that they always reflect the current state of development.
4. **Decision Logging**: Document all significant decisions in the DECISIONS.md file, including alternatives considered and the rationale for the chosen approach.

These context retention mechanisms address the specific challenge you identified regarding having to repeatedly explain the situation when opening new chats with the AI agents.

### Standardized Prompt Templates

Develop a set of standardized prompt templates for common interactions with both AI agents. These templates should be documented in a PROMPTS.md file and include:

1. **Task Assignment Template**: Structured format for assigning development tasks to Replit, including clear scope boundaries and checkpoint criteria.
2. **Review Request Template**: Standardized format for requesting code reviews from Cursor, including specific areas of focus and relevant context.
3. **Feedback Implementation Template**: Consistent structure for directing Replit to implement review feedback, with prioritization and specific guidance.
4. **Context Restoration Template**: Standardized approach for reestablishing project context at the beginning of new sessions.
5. **Status Update Template**: Consistent format for requesting progress updates from either AI agent.

These standardized templates ensure consistency in communication and help both AI agents understand expectations for each interaction.

### Human-in-the-Loop Checkpoints

Establish clear criteria for when human intervention is required during the development process. These checkpoints should be documented in a CHECKPOINTS.md file and include:

1. **Architectural Decision Points**: Moments when significant architectural decisions need to be made or validated.
2. **Technical Debt Assessment**: Regular reviews to identify and address accumulating technical debt before it becomes problematic.
3. **Feature Completion Verification**: Human validation that completed features meet requirements before integration.
4. **Conflict Resolution**: Intervention when AI agents propose conflicting approaches or solutions.
5. **Deployment Approval**: Final human approval before deploying to production environments.

These human-in-the-loop checkpoints ensure that you maintain control over critical aspects of the project while allowing the AI agents to handle routine development tasks.

### Cross-Agent Communication Protocols

Establish protocols for indirect communication between Cursor and Replit, mediated through repository documentation. These protocols should be documented in a COMMUNICATION_FLOW.md file and include:

1. **Review-Feedback Cycle**: Structured process for Cursor to provide feedback on Replit's work through the REVIEW_FEEDBACK.md file.
2. **Implementation Verification**: Standardized approach for Cursor to verify that Replit has correctly implemented feedback.
3. **Question-Answer Exchange**: Process for Replit to ask questions about requirements or feedback and for Cursor to provide clarifications.
4. **Conflict Escalation**: Protocol for identifying and escalating conflicts between AI agent recommendations for human resolution.

These cross-agent communication protocols ensure that information flows effectively between the AI agents even though they don't directly interact.

### Documentation Standards

Establish clear standards for all project documentation to ensure consistency and completeness. These standards should be documented in a DOCUMENTATION_STANDARDS.md file and include:

1. **File Naming Conventions**: Consistent naming patterns for documentation files.
2. **Document Structure**: Standardized sections and headings for each type of document.
3. **Update Frequency**: Guidelines for how often each document should be updated.
4. **Version Control**: Approach for tracking changes to documentation over time.
5. **Responsibility Assignment**: Clear designation of who is responsible for maintaining each document.

These documentation standards ensure that all project information is captured consistently and remains accessible throughout the project lifecycle.

### Communication Cadence

Establish a regular cadence for different types of communication to ensure consistent progress and alignment. This cadence should be documented in a COMMUNICATION_SCHEDULE.md file and include:

1. **Development Sessions**: Regular sessions with Replit for implementing features and addressing feedback.
2. **Review Sessions**: Scheduled reviews with Cursor to evaluate completed work and provide feedback.
3. **Integration Points**: Planned moments for integrating completed features into the main codebase.
4. **Status Updates**: Regular check-ins to assess progress and adjust priorities.
5. **Retrospectives**: Periodic reviews of the development process to identify improvements.

This structured communication cadence ensures regular progress while providing predictable opportunities for alignment and course correction.

### Issue and Blocker Resolution Protocol

Establish a clear protocol for identifying and resolving issues or blockers that arise during development. This protocol should be documented in an ISSUE_RESOLUTION.md file and include:

1. **Issue Documentation**: Standardized format for documenting problems, including context, impact, and attempted solutions.
2. **Escalation Criteria**: Clear guidelines for when issues should be escalated for human intervention.
3. **Resolution Approaches**: Structured methods for addressing different types of issues.
4. **Knowledge Capture**: Process for documenting resolutions to prevent recurrence of similar issues.

This issue resolution protocol addresses your concern about AI coders getting stuck in loops of ineffective fixes by providing a structured approach to problem-solving.

### Continuous Improvement Process

Implement a process for continuously improving the communication protocols based on experience and feedback. This process should be documented in an IMPROVEMENT_PROCESS.md file and include:

1. **Protocol Effectiveness Metrics**: Measures for evaluating how well the communication protocols are working.
2. **Feedback Collection**: Methods for gathering input on communication challenges or successes.
3. **Adaptation Approach**: Process for updating protocols based on feedback and experience.
4. **Learning Repository**: Collection of lessons learned and best practices that evolve over time.

This continuous improvement process ensures that the communication protocols remain effective as the project evolves and as you gain more experience with the workflow.

### Emergency Communication Procedures

Establish procedures for handling urgent issues that require immediate attention. These procedures should be documented in an EMERGENCY_PROCEDURES.md file and include:

1. **Critical Issue Criteria**: Definition of what constitutes an emergency requiring immediate response.
2. **Notification Process**: Method for alerting all relevant parties about the emergency.
3. **Response Protocol**: Structured approach for addressing critical issues quickly.
4. **Post-Incident Review**: Process for analyzing the incident and preventing similar issues in the future.

These emergency procedures ensure that critical issues can be addressed promptly without disrupting the overall communication structure.

