import { useEffect, useState } from "react";
import { useLocation } from "wouter";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { crossDomainAuth } from "@/lib/supabaseClient";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2 } from "lucide-react";

export default function AuthCallbackPage() {
  const [status, setStatus] = useState<'processing' | 'success' | 'error'>('processing');
  const [message, setMessage] = useState('Processing authentication...');
  const { handleAuthCallback, user, isLoading } = useAuthContext();
  const [location, setLocation] = useLocation();

  useEffect(() => {
    const processAuthCallback = async () => {
      try {
        setStatus('processing');
        setMessage('Verifying authentication...');

        // Handle the auth callback
        await handleAuthCallback();

        // Check for URL parameters (for OAuth flows)
        const urlParams = new URLSearchParams(window.location.search);
        const accessToken = urlParams.get('access_token');
        const error = urlParams.get('error');
        const errorDescription = urlParams.get('error_description');

        if (error) {
          setStatus('error');
          setMessage(errorDescription || error || 'Authentication failed');
          return;
        }

        // Check current domain to determine redirect behavior
        const domainInfo = crossDomainAuth.getCurrentDomain();
        
        if (domainInfo?.type === 'customer') {
          // On customer subdomain - redirect to tenant dashboard
          setStatus('success');
          setMessage(`Welcome to ${domainInfo.subdomain}! Redirecting to dashboard...`);
          
          setTimeout(() => {
            setLocation('/dashboard');
          }, 2000);
        } else {
          // On main domain - redirect to main dashboard or profile
          setStatus('success');
          setMessage('Authentication successful! Redirecting...');
          
          setTimeout(() => {
            setLocation('/profile');
          }, 2000);
        }

      } catch (error) {
        console.error('Auth callback error:', error);
        setStatus('error');
        setMessage('Authentication failed. Please try again.');
      }
    };

    processAuthCallback();
  }, [handleAuthCallback, setLocation]);

  // If user is already authenticated and not loading, redirect appropriately
  useEffect(() => {
    if (!isLoading && user) {
      const domainInfo = crossDomainAuth.getCurrentDomain();
      
      if (domainInfo?.type === 'customer') {
        setLocation('/dashboard');
      } else {
        setLocation('/profile');
      }
    }
  }, [user, isLoading, setLocation]);

  const getStatusColor = () => {
    switch (status) {
      case 'processing':
        return 'text-blue-600';
      case 'success':
        return 'text-green-600';
      case 'error':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const getStatusIcon = () => {
    switch (status) {
      case 'processing':
        return <Loader2 className="h-8 w-8 animate-spin" />;
      case 'success':
        return (
          <div className="h-8 w-8 rounded-full bg-green-100 flex items-center justify-center">
            <svg className="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
            </svg>
          </div>
        );
      case 'error':
        return (
          <div className="h-8 w-8 rounded-full bg-red-100 flex items-center justify-center">
            <svg className="h-5 w-5 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 px-4">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            {getStatusIcon()}
          </div>
          <CardTitle className={getStatusColor()}>
            {status === 'processing' && 'Authenticating...'}
            {status === 'success' && 'Success!'}
            {status === 'error' && 'Authentication Failed'}
          </CardTitle>
          <CardDescription>
            Cross-domain authentication in progress
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center">
            <p className={`text-sm ${getStatusColor()}`}>
              {message}
            </p>
            
            {status === 'error' && (
              <div className="mt-4">
                <button
                  onClick={() => window.location.href = '/login'}
                  className="text-blue-600 hover:text-blue-800 text-sm underline"
                >
                  Return to login
                </button>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 