import { useEffect } from "react";
import { useLocation } from "wouter";
import { RegisterForm } from "@/components/auth/RegisterForm";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";

export default function RegisterPage() {
  const [, setLocation] = useLocation();
  const { user, isLoading } = useAuthContext();

  useEffect(() => {
    if (!isLoading && user) {
      if (user.role === 'user') {
        setLocation('/profile');
      } else {
        setLocation('/dashboard');
      }
    }
  }, [user, isLoading, setLocation]);

  if (isLoading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="animate-pulse text-[hsl(var(--text-light))]">Loading...</div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg flex flex-col">
      <Header />
      <main className="flex-1 flex items-center justify-center px-6 py-12">
        <RegisterForm />
      </main>
      <Footer />
    </div>
  );
}
