#!/usr/bin/env tsx
/**
 * Create Super Admin User Script
 * 
 * This script creates a super admin user in Supabase Auth and ensures
 * the corresponding profile is created in the public users table with
 * the correct role and permissions.
 * 
 * Run with: npx tsx scripts/create-super-admin.ts
 */

import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing required environment variables:');
  console.error('   VITE_SUPABASE_URL');
  console.error('   SUPABASE_SERVICE_ROLE_KEY');
  console.error('\nMake sure these are set in your .env file or Replit secrets.');
  process.exit(1);
}

// Create Supabase admin client
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function createSuperAdmin() {
  console.log('🚀 Creating Super Admin User...\n');

  // Get admin details from command line or use defaults
  const email = process.argv[2] || '<EMAIL>';
  const password = process.argv[3] || 'SuperSecure123!';
  const firstName = process.argv[4] || 'Super';
  const lastName = process.argv[5] || 'Admin';
  const businessName = process.argv[6] || 'Agent Factory Pro';

  console.log(`📧 Email: ${email}`);
  console.log(`👤 Name: ${firstName} ${lastName}`);
  console.log(`🏢 Business: ${businessName}`);
  console.log(`🔐 Password: ${password.replace(/./g, '*')}\n`);

  try {
    // Step 1: Create user in Supabase Auth
    console.log('1️⃣ Creating user in Supabase Auth...');
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email,
      password,
      email_confirm: true, // Auto-confirm email
      user_metadata: {
        first_name: firstName,
        last_name: lastName,
        business_name: businessName,
        role: 'super_admin'
      }
    });

    if (authError) {
      throw new Error(`Failed to create auth user: ${authError.message}`);
    }

    if (!authUser.user) {
      throw new Error('User creation succeeded but no user data returned');
    }

    console.log(`✅ Auth user created with ID: ${authUser.user.id}`);

    // Step 2: Wait a moment for the trigger to fire
    console.log('2️⃣ Waiting for database trigger to create profile...');
    await new Promise(resolve => setTimeout(resolve, 2000));

    // Step 3: Verify the profile was created and update role if needed
    console.log('3️⃣ Verifying and updating user profile...');
    const { data: profile, error: profileError } = await supabase
      .from('users')
      .select('*')
      .eq('external_id', authUser.user.id)
      .single();

    if (profileError) {
      console.log('⚠️  Profile not found, creating manually...');
      
      // Create profile manually if trigger didn't work
      const { data: newProfile, error: insertError } = await supabase
        .from('users')
        .insert({
          external_id: authUser.user.id,
          email: email,
          first_name: firstName,
          last_name: lastName,
          business_name: businessName,
          role: 'super_admin',
          is_active: true
        })
        .select()
        .single();

      if (insertError) {
        throw new Error(`Failed to create profile: ${insertError.message}`);
      }

      console.log('✅ Profile created manually');
    } else {
      console.log('✅ Profile found via trigger');
      
      // Update role to super_admin if it's not already set
      if (profile.role !== 'super_admin') {
        const { error: updateError } = await supabase
          .from('users')
          .update({ role: 'super_admin' })
          .eq('external_id', authUser.user.id);

        if (updateError) {
          throw new Error(`Failed to update role: ${updateError.message}`);
        }
        console.log('✅ Role updated to super_admin');
      }
    }

    // Step 4: Final verification
    console.log('4️⃣ Final verification...');
    const { data: finalProfile, error: finalError } = await supabase
      .from('users')
      .select('*')
      .eq('external_id', authUser.user.id)
      .single();

    if (finalError || !finalProfile) {
      throw new Error('Failed to verify final profile');
    }

    console.log('\n🎉 Super Admin User Created Successfully!\n');
    console.log('📋 User Details:');
    console.log(`   ID: ${finalProfile.id}`);
    console.log(`   External ID: ${finalProfile.external_id}`);
    console.log(`   Email: ${finalProfile.email}`);
    console.log(`   Name: ${finalProfile.first_name} ${finalProfile.last_name}`);
    console.log(`   Business: ${finalProfile.business_name}`);
    console.log(`   Role: ${finalProfile.role}`);
    console.log(`   Active: ${finalProfile.is_active}`);
    console.log(`   Created: ${finalProfile.created_at}\n`);

    console.log('🔑 Login Credentials:');
    console.log(`   Email: ${email}`);
    console.log(`   Password: ${password}\n`);

    console.log('✨ You can now log in to your application with super admin privileges!');

  } catch (error) {
    console.error('\n❌ Error creating super admin:', error);
    process.exit(1);
  }
}

// Show usage if help is requested
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
Usage: npx tsx scripts/create-super-admin.ts [email] [password] [firstName] [lastName] [businessName]

Arguments (all optional):
  email        Admin email address (default: <EMAIL>)
  password     Admin password (default: SuperSecure123!)
  firstName    First name (default: Super)
  lastName     Last name (default: Admin)
  businessName Business name (default: Agent Factory Pro)

Examples:
  npx tsx scripts/create-super-admin.ts
  npx tsx scripts/create-super-admin.ts <EMAIL> YourPassword123!
  npx tsx scripts/create-super-admin.ts <EMAIL> pass123 John Doe "Your Company"

Environment Variables Required:
  VITE_SUPABASE_URL          - Your Supabase project URL
  SUPABASE_SERVICE_ROLE_KEY  - Your Supabase service role key
`);
  process.exit(0);
}

// Run the script
createSuperAdmin(); 