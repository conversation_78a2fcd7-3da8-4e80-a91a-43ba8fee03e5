import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { CloudinaryImage } from "@/components/ui/cloudinary-image";
import { Link } from "wouter";
import { Calendar, Clock, Search, Filter, ChevronLeft, ChevronRight, Twitter, Linkedin, Facebook } from "lucide-react";
import type { BlogPost } from "@shared/schema";

// Helper function to calculate read time
const calculateReadTime = (content: string): number => {
  const wordsPerMinute = 200;
  const words = content.replace(/<[^>]*>/g, '').split(/\s+/).length;
  return Math.ceil(words / wordsPerMinute);
};

// Helper function to get post categories
const getCategories = (posts: BlogPost[]): string[] => {
  const categories = ['All', 'AI & Automation', 'Business Process', 'Integration', 'Technology'];
  return categories;
};

// Social sharing component
const SocialShareButtons = ({ title, url }: { title: string; url: string }) => {
  const encodedTitle = encodeURIComponent(title);
  const encodedUrl = encodeURIComponent(url);
  
  return (
    <div className="flex items-center gap-2">
      <span className="text-sm text-muted-foreground">Share:</span>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => window.open(`https://twitter.com/intent/tweet?text=${encodedTitle}&url=${encodedUrl}`, '_blank')}
      >
        <Twitter className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => window.open(`https://linkedin.com/sharing/share-offsite/?url=${encodedUrl}`, '_blank')}
      >
        <Linkedin className="h-4 w-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => window.open(`https://facebook.com/sharer/sharer.php?u=${encodedUrl}`, '_blank')}
      >
        <Facebook className="h-4 w-4" />
      </Button>
    </div>
  );
};

// Blog list loading skeleton
const BlogListSkeleton = () => (
  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
    {[...Array(6)].map((_, i) => (
      <Card key={i} className="animate-pulse">
        <div className="h-48 bg-muted rounded-t-lg"></div>
        <CardContent className="p-6 space-y-4">
          <div className="h-4 bg-muted rounded w-3/4"></div>
          <div className="h-3 bg-muted rounded w-full"></div>
          <div className="h-3 bg-muted rounded w-2/3"></div>
        </CardContent>
      </Card>
    ))}
  </div>
);

export default function BlogList() {
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All");
  const postsPerPage = 6;

  const { data: posts, isLoading, error } = useQuery<BlogPost[]>({
    queryKey: ["/api/blog/posts"],
  });

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-64">
        <div className="text-center space-y-4">
          <h2 className="text-2xl font-bold">Error Loading Blog Posts</h2>
          <p className="text-muted-foreground">
            Unable to load blog posts. Please try again later.
          </p>
        </div>
      </div>
    );
  }

  // Filter and paginate posts
  const filteredPosts = posts?.filter(post => {
    const matchesSearch = post.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         post.content.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "All" || 
                           post.title.toLowerCase().includes(selectedCategory.toLowerCase());
    return matchesSearch && matchesCategory;
  }) || [];

  const totalPages = Math.ceil(filteredPosts.length / postsPerPage);
  const startIndex = (currentPage - 1) * postsPerPage;
  const paginatedPosts = filteredPosts.slice(startIndex, startIndex + postsPerPage);

  const categories = posts ? getCategories(posts) : ['All'];

  return (
    <div className="space-y-8 animate-in fade-in duration-300">
      {/* Search and Filter */}
      <div className="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div className="relative w-full md:w-96">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search articles..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="pl-10"
          />
        </div>
        
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <Select value={selectedCategory} onValueChange={(value) => {
            setSelectedCategory(value);
            setCurrentPage(1);
          }}>
            <SelectTrigger className="w-48">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categories.map((category) => (
                <SelectItem key={category} value={category}>
                  {category}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Results count */}
      <div className="text-sm text-muted-foreground">
        {filteredPosts.length > 0 ? (
          <>Showing {startIndex + 1}-{Math.min(startIndex + postsPerPage, filteredPosts.length)} of {filteredPosts.length} articles</>
        ) : (
          <>No articles found</>
        )}
      </div>

      {isLoading ? (
        <BlogListSkeleton />
      ) : paginatedPosts.length > 0 ? (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {paginatedPosts.map((post) => (
              <Card key={post.id} className="glass-card hover:shadow-2xl transition-all duration-300 group hover:scale-105 bg-[hsl(var(--secondary-dark))] bg-opacity-30 border-[hsl(var(--accent-cyan))] border-opacity-30">
                {post.featuredImageUrl && (
                  <div className="relative h-48 overflow-hidden rounded-t-lg">
                    <CloudinaryImage
                      publicId={post.featuredImageUrl}
                      alt={post.title}
                      width={400}
                      height={200}
                      className="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
                    />
                  </div>
                )}
                <CardContent className="p-6 space-y-4">
                  <div className="flex items-center justify-between text-sm text-[hsl(var(--text-light))]">
                    <span className="flex items-center gap-1">
                      <Calendar className="w-4 h-4" />
                      {new Date(post.createdAt).toLocaleDateString()}
                    </span>
                    <span className="flex items-center gap-1">
                      <Clock className="w-4 h-4" />
                      {calculateReadTime(post.content)} min read
                    </span>
                  </div>
                  
                  <div className="space-y-2">
                    <h3 className="text-xl font-semibold line-clamp-2 text-white group-hover:text-[hsl(var(--accent-cyan))] transition-colors">
                      {post.title}
                    </h3>
                    <p className="text-[hsl(var(--text-light))] line-clamp-3">
                      {post.excerpt || post.content.replace(/<[^>]*>/g, '').substring(0, 150) + "..."}
                    </p>
                  </div>

                  <div className="flex items-center justify-between pt-4">
                    <Badge variant="secondary" className="bg-[hsl(var(--accent-cyan))] bg-opacity-30 text-black font-semibold border-[hsl(var(--accent-cyan))] border-opacity-50">
                      AI & Automation
                    </Badge>
                    <Link href={`/blog/${post.slug}`}>
                      <Button className="bg-[hsl(var(--accent-cyan))] hover:bg-[hsl(var(--accent-cyan))]/80 text-black font-semibold px-4 py-2 text-sm border-0">
                        Read More
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Pagination */}
          {totalPages > 1 && (
            <div className="flex items-center justify-center gap-2 mt-8">
              <Button
                variant="outline"
                disabled={currentPage === 1}
                onClick={() => setCurrentPage(currentPage - 1)}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              
              {[...Array(totalPages)].map((_, i) => {
                const page = i + 1;
                const isActive = page === currentPage;
                const showPage = page === 1 || page === totalPages || Math.abs(page - currentPage) <= 2;
                
                if (!showPage) {
                  if (page === currentPage - 3 || page === currentPage + 3) {
                    return <span key={page} className="px-2">...</span>;
                  }
                  return null;
                }
                
                return (
                  <Button
                    key={page}
                    variant={isActive ? "default" : "outline"}
                    onClick={() => setCurrentPage(page)}
                    className="w-10"
                  >
                    {page}
                  </Button>
                );
              })}
              
              <Button
                variant="outline"
                disabled={currentPage === totalPages}
                onClick={() => setCurrentPage(currentPage + 1)}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          )}

          {/* Book a Demo CTA - Below posts section */}
          <Card className="glass-card bg-[hsl(var(--secondary-dark))] bg-opacity-30 border-[hsl(var(--accent-cyan))] border-opacity-30 mt-12">
            <CardContent className="p-8 text-center space-y-4">
              <h2 className="text-2xl font-bold text-white">Ready to Transform Your Operations?</h2>
              <p className="text-[hsl(var(--text-light))] text-lg">
                Inspired by what you've read? Book a free demo and see how our AI agents can automate your business processes and eliminate operational headaches.
              </p>
              <Link href="/booking">
                <Button className="btn-primary px-8 py-3 text-lg">
                  Book a Free Demo
                </Button>
              </Link>
            </CardContent>
          </Card>
        </>
      ) : (
        <div className="text-center space-y-4 py-12">
          <h2 className="text-2xl font-bold">No Articles Found</h2>
          <p className="text-muted-foreground">
            {searchTerm || selectedCategory !== "All" 
              ? "Try adjusting your search or filter criteria." 
              : "We're working on some amazing content. Check back soon!"
            }
          </p>
          {(searchTerm || selectedCategory !== "All") && (
            <Button 
              variant="outline" 
              onClick={() => {
                setSearchTerm("");
                setSelectedCategory("All");
                setCurrentPage(1);
              }}
            >
              Clear Filters
            </Button>
          )}
        </div>
      )}
    </div>
  );
}