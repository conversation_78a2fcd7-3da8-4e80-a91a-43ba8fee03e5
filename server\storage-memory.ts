import { 
  type User, 
  type InsertUser,
  type BlogPost,
  type InsertBlogPost,
  type UpdateBlogPost
} from "@shared/schema";
import { IStorage } from './storage.js';
import bcrypt from 'bcrypt';

// Simple in-memory storage for local development
export class InMemoryStorage implements IStorage {
  private users: Map<number, User> = new Map();
  private blogPosts: Map<number, BlogPost> = new Map();
  private workspaces: Map<string, any> = new Map();
  private nextUserId = 1;
  private nextPostId = 1;
  private nextWorkspaceId = 1;

  constructor() {
    // Add a default admin user for testing
    const adminUser: User = {
      id: this.nextUserId++,
      externalId: 'user-1',
      email: '<EMAIL>',
      firstName: 'Admin',
      lastName: 'User',
      businessName: null,
      role: 'super_admin',
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(adminUser.id, adminUser);
  }

  async getUser(id: number): Promise<User | undefined> {
    return this.users.get(id);
  }

  async getUserByEmail(email: string): Promise<User | undefined> {
    return Array.from(this.users.values()).find(u => u.email === email);
  }

  async createUser(insertUser: InsertUser): Promise<User> {
    const user: User = {
      id: this.nextUserId++,
      externalId: insertUser.externalId || null,
      email: insertUser.email,
      firstName: insertUser.firstName || null,
      lastName: insertUser.lastName || null,
      businessName: insertUser.businessName || null,
      role: insertUser.role,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
    this.users.set(user.id, user);
    return user;
  }

  async updateUser(id: number, updates: Partial<InsertUser>): Promise<User> {
    const user = this.users.get(id);
    if (!user) throw new Error("User not found");
    
    Object.assign(user, updates, { updatedAt: new Date() });
    return user;
  }

  async deleteUser(id: number): Promise<void> {
    if (!this.users.delete(id)) {
      throw new Error("User not found");
    }
  }

  async getAllUsers(): Promise<User[]> {
    return Array.from(this.users.values()).sort((a, b) => 
      b.createdAt.getTime() - a.createdAt.getTime()
    );
  }

  async getBlogPosts(publishedOnly = false): Promise<BlogPost[]> {
    const posts = Array.from(this.blogPosts.values());
    return (publishedOnly ? posts.filter(p => p.isPublished) : posts)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  async getBlogPostBySlug(slug: string): Promise<BlogPost | undefined> {
    return Array.from(this.blogPosts.values()).find(p => p.slug === slug);
  }

  async createBlogPost(insertPost: InsertBlogPost): Promise<BlogPost> {
    const post: BlogPost = {
      id: this.nextPostId++,
      title: insertPost.title,
      slug: insertPost.slug,
      excerpt: insertPost.excerpt || null,
      content: insertPost.content,
      featuredImageUrl: insertPost.featuredImageUrl || null,
      metaTitle: insertPost.metaTitle || null,
      metaDescription: insertPost.metaDescription || null,
      isPublished: insertPost.isPublished || false,
      authorId: insertPost.authorId || null,
      createdAt: new Date(),
      updatedAt: new Date(),
      publishedAt: insertPost.isPublished ? new Date() : null,
      tenantId: null,
    };
    this.blogPosts.set(post.id, post);
    return post;
  }

  async updateBlogPost(id: number, updates: UpdateBlogPost): Promise<BlogPost> {
    const post = this.blogPosts.get(id);
    if (!post) throw new Error("Blog post not found");
    
    Object.assign(post, updates, { updatedAt: new Date() });
    return post;
  }

  async deleteBlogPost(id: number): Promise<boolean> {
    return this.blogPosts.delete(id);
  }

  async getAdminStats(): Promise<{
    totalUsers: number;
    totalPosts: number;
    draftPosts: number;
  }> {
    const posts = Array.from(this.blogPosts.values());
    return {
      totalUsers: this.users.size,
      totalPosts: posts.filter(p => p.isPublished).length,
      draftPosts: posts.filter(p => !p.isPublished).length,
    };
  }

  public async getBlogStats(tenantId?: number): Promise<{ totalPosts: number; draftPosts: number; }> {
    const allPosts = Array.from(this.blogPosts.values());
    const posts = tenantId
      ? allPosts.filter(p => p.tenantId === tenantId)
      : allPosts;
    const draftPosts = posts.filter(p => !p.isPublished).length;
    return {
      totalPosts: posts.length,
      draftPosts: draftPosts,
    };
  }

  // Workspace Management Implementation
  public async listWorkspaceTemplates(): Promise<any[]> {
    return [
      { id: "web-app", name: "Web Application", description: "Full-stack web application workspace" },
      { id: "api-service", name: "API Service", description: "Microservice API workspace" },
      { id: "data-pipeline", name: "Data Pipeline", description: "Data processing and analytics workspace" }
    ];
  }

  public async listAllWorkspaces(): Promise<any[]> {
    return Array.from(this.workspaces.values()).sort((a, b) => 
      b.createdAt.getTime() - a.createdAt.getTime()
    );
  }

  public async listUserWorkspaces(userId: number): Promise<any[]> {
    return Array.from(this.workspaces.values())
      .filter(w => w.user_id === userId)
      .sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());
  }

  public async createWorkspace(workspaceData: any): Promise<any> {
    const newId = `ws-${this.nextWorkspaceId++}`;
    const newWorkspace = {
      id: newId,
      createdAt: new Date(),
      updatedAt: new Date(),
      ...workspaceData,
    };
    this.workspaces.set(newId, newWorkspace);
    return newWorkspace;
  }

  public async getWorkspaceById(workspaceId: string, userId?: number): Promise<any | null> {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      return null;
    }
    // If a userId is provided, ensure they own the workspace
    if (userId && workspace.user_id !== userId) {
      return null;
    }
    return workspace;
  }

  public async updateWorkspace(workspaceId: string, updateData: any): Promise<any | null> {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      return null;
    }
    Object.assign(workspace, { ...updateData, updatedAt: new Date() });
    return workspace;
  }

  public async deleteWorkspace(workspaceId: string): Promise<boolean> {
    return this.workspaces.delete(workspaceId);
  }

  public async archiveWorkspace(workspaceId: string): Promise<any | null> {
    const workspace = this.workspaces.get(workspaceId);
    if (!workspace) {
      return null;
    }
    Object.assign(workspace, { 
      status: 'archived',
      updatedAt: new Date()
    });
    return workspace;
  }
} 