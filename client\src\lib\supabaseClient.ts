import { createClient } from '@supabase/supabase-js'

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY

let supabase: any = null;

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn('Supabase URL and Anon Key not provided in environment variables. Authentication features will not work.')
} else {
  try {
    // Configure Supabase client for cross-domain authentication
    supabase = createClient(supabaseUrl, supabaseAnonKey, {
      auth: {
        // Allow cross-domain authentication
        autoRefreshToken: true,
        persistSession: true,
        detectSessionInUrl: true,
        // Configure storage to work across domains
        storage: {
          getItem: (key: string) => {
            if (typeof window !== 'undefined') {
              return window.localStorage.getItem(key)
            }
            return null
          },
          setItem: (key: string, value: string) => {
            if (typeof window !== 'undefined') {
              window.localStorage.setItem(key, value)
            }
          },
          removeItem: (key: string) => {
            if (typeof window !== 'undefined') {
              window.localStorage.removeItem(key)
            }
          }
        },
        // Configure flow type for cross-domain support
        flowType: 'pkce'
      }
    })
  } catch (error) {
    console.error('Failed to initialize Supabase client:', error)
  }
}

// Cross-domain authentication utilities
export const crossDomainAuth = {
  // Get current domain info
  getCurrentDomain: () => {
    if (typeof window === 'undefined') return null
    
    const hostname = window.location.hostname
    const parts = hostname.split('.')
    
    // Check if it's a customer subdomain (*.agent-factory.app)
    if (parts.length >= 3 && parts[parts.length - 2] === 'agent-factory' && parts[parts.length - 1] === 'app') {
      return {
        type: 'customer',
        subdomain: parts[0],
        hostname: hostname
      }
    }
    
    // Check if it's main domain
    if (hostname.includes('agent-factory.io')) {
      return {
        type: 'main',
        subdomain: null,
        hostname: hostname
      }
    }
    
    // Local development
    if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
      return {
        type: 'local',
        subdomain: null,
        hostname: hostname
      }
    }
    
    return {
      type: 'unknown',
      subdomain: null,
      hostname: hostname
    }
  },
  
  // Generate redirect URL for cross-domain authentication
  getRedirectUrl: (targetSubdomain?: string) => {
    if (typeof window === 'undefined') return null
    
    const protocol = window.location.protocol
    
    if (targetSubdomain) {
      // Redirect to customer subdomain
      return `${protocol}//${targetSubdomain}.agent-factory.app/auth/callback`
    } else {
      // Redirect to main domain
      return `${protocol}//www.agent-factory.io/auth/callback`
    }
  },
  
  // Store authentication state for cross-domain transfer
  storeAuthState: (session: any, targetDomain?: string) => {
    if (typeof window === 'undefined' || !session) return
    
    // Store session data that can be transferred across domains
    const authData = {
      access_token: session.access_token,
      refresh_token: session.refresh_token,
      expires_at: session.expires_at,
      user: session.user,
      timestamp: Date.now()
    }
    
    // Store in localStorage for same-domain access
    window.localStorage.setItem('supabase_auth_transfer', JSON.stringify(authData))
    
    // If target domain is specified, we'll need to handle the transfer
    if (targetDomain) {
      // Store target domain for redirect handling
      window.localStorage.setItem('supabase_auth_target', targetDomain)
    }
  },
  
  // Retrieve and validate transferred authentication state
  retrieveAuthState: () => {
    if (typeof window === 'undefined') return null
    
    try {
      const authDataStr = window.localStorage.getItem('supabase_auth_transfer')
      if (!authDataStr) return null
      
      const authData = JSON.parse(authDataStr)
      
      // Check if the stored auth data is still valid (not older than 5 minutes)
      const maxAge = 5 * 60 * 1000 // 5 minutes
      if (Date.now() - authData.timestamp > maxAge) {
        window.localStorage.removeItem('supabase_auth_transfer')
        return null
      }
      
      return authData
    } catch (error) {
      console.error('Error retrieving auth state:', error)
      return null
    }
  },
  
  // Clean up transferred auth state
  cleanupAuthState: () => {
    if (typeof window === 'undefined') return
    
    window.localStorage.removeItem('supabase_auth_transfer')
    window.localStorage.removeItem('supabase_auth_target')
  }
}

export { supabase } 