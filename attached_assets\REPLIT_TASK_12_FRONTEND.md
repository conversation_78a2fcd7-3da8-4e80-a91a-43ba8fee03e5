# Frontend Implementation Task: Complete User Management Dashboard Features

## Context
You are implementing the remaining frontend features for Task 12 "Build User Management Dashboard" in the AgentFactoryPro project. The basic user listing, creation, and search/filter functionality is already implemented. You need to add the missing UI components for bulk operations, user invitations, and activity logs.

## Current State
- ✅ User listing with role badges and status indicators
- ✅ Create user dialog with form validation
- ✅ Edit/delete individual users
- ✅ Search and filter functionality (by name, role, status)
- ✅ Business name grouping with expandable sections
- ❌ Bulk user operations
- ❌ User invitation system
- ❌ Activity log view
- ❌ businessName field in edit form
- ❌ URL parameter synchronization

## Task 1: Add Bulk User Operations UI

### Requirements:
1. **Selection System**
   - Add a checkbox to each user card (top-left corner)
   - Add "Select All" checkbox in the card header for each business group
   - Show selection count indicator (e.g., "3 users selected")
   - Clear selection when filters change

2. **Bulk Actions Bar**
   - Show a floating action bar when users are selected
   - Include actions: Delete, Activate, Deactivate, Change Role
   - Position it as a sticky bar at the bottom of the viewport
   - Include "Cancel" button to clear selection

3. **Implementation Details**
   ```tsx
   // Add to state
   const [selectedUsers, setSelectedUsers] = useState<Set<number>>(new Set());
   
   // Bulk action bar component structure
   <AnimatePresence>
     {selectedUsers.size > 0 && (
       <motion.div className="fixed bottom-4 left-1/2 transform -translate-x-1/2 glass-card p-4">
         <div className="flex items-center space-x-4">
           <span>{selectedUsers.size} users selected</span>
           <Select><!-- Bulk actions --></Select>
           <Button onClick={handleBulkAction}>Apply</Button>
           <Button variant="ghost" onClick={() => setSelectedUsers(new Set())}>Cancel</Button>
         </div>
       </motion.div>
     )}
   </AnimatePresence>
   ```

4. **Confirmation Dialogs**
   - Use AlertDialog for destructive actions
   - Show summary of affected users
   - Handle partial success (some operations may fail)

## Task 2: User Invitation System UI

### Requirements:
1. **Invite Button**
   - Add "Invite Users" button next to "Create User" button
   - Use Mail icon from lucide-react

2. **Invitation Dialog**
   ```tsx
   // Form fields needed:
   - Email input (with multi-email support, comma-separated)
   - Role selection dropdown
   - Custom message textarea (optional)
   - Expiration time select (24h, 7 days, 30 days)
   ```

3. **Invitation Status Section**
   - Add a tab or collapsible section showing pending invitations
   - Show: Email, Role, Sent Date, Status, Resend/Cancel actions
   - Group by status: Pending, Accepted, Expired

4. **Mock the API calls for now**
   ```tsx
   const inviteUsersMutation = useMutation({
     mutationFn: async (data: InviteUsersData) => {
       // Mock API call
       await new Promise(resolve => setTimeout(resolve, 1000));
       return { success: true, invited: data.emails.length };
     },
     onSuccess: (data) => {
       toast({
         title: "Invitations Sent",
         description: `Successfully sent ${data.invited} invitation(s)`,
       });
     },
   });
   ```

## Task 3: Activity Log View

### Requirements:
1. **New Tab in User Management**
   - Add "Activity Logs" tab alongside the main user view
   - Use Clock or Activity icon

2. **Log Table Structure**
   ```tsx
   interface ActivityLog {
     id: number;
     userId: number;
     userName: string;
     action: string; // "user.created", "user.updated", "user.deleted", etc.
     details: string;
     ipAddress: string;
     timestamp: Date;
   }
   ```

3. **Features to Implement**
   - Filterable table with columns: User, Action, Details, IP, Timestamp
   - Date range picker for filtering
   - Action type filter dropdown
   - Pagination (20 items per page)
   - Export buttons (CSV/JSON)

4. **Mock Data Generator**
   ```tsx
   const generateMockLogs = (): ActivityLog[] => {
     // Generate 50-100 mock log entries
     // Use realistic actions: user.login, user.logout, user.updated, post.created, etc.
   };
   ```

## Task 4: Edit Form Enhancement

### Requirements:
1. Add businessName field to the edit user form
2. Match the styling and validation from the create user form
3. Update the form initialization to include businessName:
   ```tsx
   editForm.reset({
     email: editingUser.email,
     firstName: editingUser.firstName,
     lastName: editingUser.lastName,
     role: editingUser.role,
     businessName: editingUser.businessName || "", // Add this line
   });
   ```

## Task 5: URL Parameter Synchronization

### Requirements:
1. **Sync filters to URL**
   ```tsx
   // Use URLSearchParams to manage state
   const [searchParams, setSearchParams] = useSearchParams();
   
   // Sync on filter change
   useEffect(() => {
     const params = new URLSearchParams();
     if (searchQuery) params.set('search', searchQuery);
     if (roleFilter !== 'all') params.set('role', roleFilter);
     if (statusFilter !== 'all') params.set('status', statusFilter);
     setSearchParams(params);
   }, [searchQuery, roleFilter, statusFilter]);
   ```

2. **Initialize from URL on mount**
3. **Preserve expanded business groups in URL** (optional but nice)

## UI/UX Guidelines

1. **Maintain Consistent Styling**
   - Use existing glass-card classes
   - Follow the cyan accent color scheme
   - Keep dark theme consistency
   - Use existing animation patterns (hover effects, transitions)

2. **Loading States**
   - Show skeletons while loading
   - Disable buttons during mutations
   - Show progress for bulk operations

3. **Error Handling**
   - Use toast notifications for errors
   - Show inline validation errors
   - Handle edge cases (no users selected, network errors)

4. **Responsive Design**
   - Ensure bulk action bar works on mobile
   - Make tables horizontally scrollable on small screens
   - Stack form fields appropriately

## Testing Checklist
- [ ] Bulk selection works across different business groups
- [ ] Invitation form validates multiple emails correctly
- [ ] Activity logs pagination works smoothly
- [ ] URL parameters persist on page refresh
- [ ] All features work on mobile devices
- [ ] Keyboard navigation is supported
- [ ] Screen readers can navigate the new features

## Code Quality Requirements
- Use TypeScript interfaces for all new data structures
- Extract reusable components (SelectionCheckbox, BulkActionBar, etc.)
- Add appropriate ARIA labels for accessibility
- Follow existing code patterns for mutations and queries
- Comment complex logic, especially for bulk operations

## Deliverables
1. Updated UserAdminPage.tsx with all new features
2. Any new component files created
3. Updated types/interfaces
4. Brief documentation of any assumptions made

Remember: Focus on creating a polished, production-ready UI. The backend API integration will be handled separately, so use mock data and mutations for now. 