# PowerShell Script to Test the Agent Factory Pro Deployment API
#
# HOW TO RUN:
# 1. Open a PowerShell terminal in the root of your project.
# 2. Run the command: .\scripts\test_deployment_api.ps1
# 3. You will be prompted for your base URL, email, and password.

# --- Configuration ---
Write-Host "--- API Test Script Setup ---" -ForegroundColor Yellow

# Prompt for the Base URL
$baseUrl = Read-Host "Enter your application's base URL (e.g., https://your-app.replit.dev)"

if (-not $baseUrl) {
    $baseUrl = "http://localhost:3001" # Default for local testing
    Write-Host "No URL entered, using default: $baseUrl" -ForegroundColor Gray
}

# Prompt for credentials
$email = Read-Host "Enter your login email"
$password = Read-Host -AsSecureString "Enter your password" | ForEach-Object { [System.Runtime.InteropServices.Marshal]::PtrToStringAuto([System.Runtime.InteropServices.Marshal]::SecureStringToBSTR($_)) }

if (-not $email -or -not $password) {
    Write-Host "Email and password are required to run these tests." -ForegroundColor Red
    return
}

# --- JWT Acquisition ---
Write-Host "`n--- Acquiring JWT from Supabase ---" -ForegroundColor Cyan
$jwt = $null
$tempLoginScript = "temp_login_script.cjs"

# Create a temporary Node.js script to log in and get the JWT
# IMPORTANT: This requires your .env file to be correctly set up at the root of the project
# with VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY
$nodeScriptContent = @"
require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
    console.error('Error: VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY must be set in your .env file.');
    process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function getJwt(email, password) {
    const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
    });

    if (error) {
        console.error('Login failed:', error.message);
        process.exit(1);
    }

    if (data.session) {
        console.log(data.session.access_token);
    } else {
        console.error('Login succeeded but no session was returned.');
        process.exit(1);
    }
}

getJwt(process.argv[2], process.argv[3]);
"@

# Write the script to a temporary file
Set-Content -Path $tempLoginScript -Value $nodeScriptContent

try {
    # Execute the Node.js script and capture the output (the JWT)
    $jwt = node $tempLoginScript $email $password | Out-String | ForEach-Object { $_.Trim() }

    if ($jwt -and $jwt -notlike 'Error:*' -and $jwt -notlike 'Login failed:*') {
        Write-Host "✅ SUCCESS: Successfully acquired JWT." -ForegroundColor Green
    } else {
        Write-Host "❌ FAILURE: Could not acquire JWT. Please check your credentials and .env file." -ForegroundColor Red
        Write-Host "Response from login script: $jwt" -ForegroundColor Yellow
        return
    }
}
finally {
    # Clean up the temporary script file
    if (Test-Path $tempLoginScript) {
        Remove-Item $tempLoginScript
    }
}

# --- Shared Setup ---
$headers = @{
    "Authorization" = "Bearer $jwt"
    "Content-Type"  = "application/json"
}

# Initialize test status variables
$test1Success = $false
$test2Success = $false
$test3Success = $false

# --- Test 1: Fetch Deployment Templates ---
Write-Host "`n--- [TEST 1/3] Fetching Deployment Templates ---" -ForegroundColor Cyan
try {
    $templates = Invoke-RestMethod -Uri "$baseUrl/api/deployment-templates" -Method Get -Headers $headers
    Write-Host "✅ SUCCESS: Successfully fetched templates." -ForegroundColor Green
    Write-Host "Available Templates:"
    $templates | ForEach-Object { Write-Host "- $($_.name) ($($_.template_type))" }
    $test1Success = $true
}
catch {
    Write-Host "❌ FAILURE: Could not fetch deployment templates." -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.Value__)"
    Write-Host "Response: $($_.Exception.Response.GetResponseStream() | ForEach-Object { (New-Object System.IO.StreamReader($_)).ReadToEnd() })"
    # Stop the script if this basic step fails
    return
}

# --- Test 2: Create a New Deployment ---
Write-Host "`n--- [TEST 2/3] Creating a New Deployment ---" -ForegroundColor Cyan
$subdomain = "test-deploy-$(Get-Random -Minimum 1000 -Maximum 9999)"
$newDeploymentPayload = @{
    app_name      = "My Test Deployment"
    subdomain     = $subdomain
    template_type = "agent_basic"
} | ConvertTo-Json

try {
    $createdDeployment = Invoke-RestMethod -Uri "$baseUrl/api/deployments" -Method Post -Headers $headers -Body $newDeploymentPayload
    Write-Host "✅ SUCCESS: Successfully created a new deployment." -ForegroundColor Green
    Write-Host "New Deployment Details:"
    $createdDeployment | Format-List
    $test2Success = $true
}
catch {
    Write-Host "❌ FAILURE: Could not create a new deployment." -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.Value__)"
    Write-Host "Response: $($_.Exception.Response.GetResponseStream() | ForEach-Object { (New-Object System.IO.StreamReader($_)).ReadToEnd() })"
    return
}

# --- Test 3: List User's Deployments to Verify ---
Write-Host "`n--- [TEST 3/3] Listing Deployments to Verify Creation ---" -ForegroundColor Cyan
try {
    $myDeployments = Invoke-RestMethod -Uri "$baseUrl/api/deployments" -Method Get -Headers $headers
    $foundDeployment = $myDeployments | Where-Object { $_.id -eq $createdDeployment.id }

    if ($foundDeployment) {
        Write-Host "✅ SUCCESS: Verified that the new deployment was successfully created and listed." -ForegroundColor Green
        Write-Host "Found Deployment in List:"
        $foundDeployment | Format-List
        $test3Success = $true
    }
    else {
        Write-Host "❌ FAILURE: The newly created deployment was not found in the user's list." -ForegroundColor Red
    }
}
catch {
    Write-Host "❌ FAILURE: Could not list deployments." -ForegroundColor Red
    Write-Host "Status Code: $($_.Exception.Response.StatusCode.Value__)"
    Write-Host "Response: $($_.Exception.Response.GetResponseStream() | ForEach-Object { (New-Object System.IO.StreamReader($_)).ReadToEnd() })"
}

Write-Host "`n--- Test Script Finished ---" -ForegroundColor Yellow

# --- Final Summary ---
Write-Host "`n--- Test Summary ---" -ForegroundColor Yellow
if ($test1Success) {
    Write-Host "[1/3] Fetch Deployment Templates: ✅ PASSED" -ForegroundColor Green
} else {
    Write-Host "[1/3] Fetch Deployment Templates: ❌ FAILED" -ForegroundColor Red
}

if ($test2Success) {
    Write-Host "[2/3] Create New Deployment:    ✅ PASSED" -ForegroundColor Green
} else {
    Write-Host "[2/3] Create New Deployment:    ❌ FAILED" -ForegroundColor Red
}

if ($test3Success) {
    Write-Host "[3/3] Verify Deployment List:   ✅ PASSED" -ForegroundColor Green
} else {
    Write-Host "[3/3] Verify Deployment List:   ❌ FAILED" -ForegroundColor Red
}