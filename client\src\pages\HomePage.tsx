import { <PERSON> } from "wouter";
import { MainLayout } from "@/components/layouts";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { RotatingText } from "@/components/ui/RotatingText";
import { Brain, Workflow, Link as LinkI<PERSON>, Wrench, Check, Globe } from "lucide-react";
import { SOLUTIONS } from "@/lib/constants";
import { SEOHead, StructuredData } from "@/components/SEOHead";
import { 
  StructuredData as NewStructuredData, 
  createOrganizationSchema, 
  createServiceSchema, 
  createWebPageSchema, 
  createFAQSchema 
} from "@/components/StructuredData";

const iconMap = {
  brain: Brain,
  workflow: Workflow,
  link: LinkIcon,
  wrench: Wrench,
  "og-image": ({ className }: { className?: string }) => (
    <div className={className}>
      <img 
        src="/og-image.png?v=1" 
        alt="Agent Factory" 
        className="w-full h-full object-contain"
        onError={(e) => console.log('Image failed to load:', e)}
        onLoad={() => console.log('Image loaded successfully')}
      />
    </div>
  ),
};

function HeroSection() {
  return (
    <section className="min-h-[55vh] flex items-center justify-center px-6 py-10">
      <div className="max-w-5xl mx-auto text-center space-y-8 animate-fade-in">
        {/* Hero Headlines */}
        <div className="space-y-6">
          <h1 className="text-5xl md:text-7xl font-bold leading-relaxed hero-text pb-4">
            Empowering Your Business<br />
            with Intelligent Automation
          </h1>
          <div className="text-xl md:text-2xl text-[hsl(var(--text-light))] max-w-4xl mx-auto leading-relaxed">
            <RotatingText 
              baseText="Fast, Affordable Automation for"
              className="flex-wrap justify-center"
            />
          </div>
        </div>
        
        {/* CTA Button */}
        <div className="flex justify-center pt-4">
          <Link href="/roi-calculator">
            <Button className="btn-primary animate-glow">
              Let's do some math...
            </Button>
          </Link>
        </div>
        

      </div>
    </section>
  );
}

function SolutionsSection() {
  // Map each solution to a specific image for visual variety
  const solutionImages = {
    "ai-agents": { src: "/agent-sm.png", alt: "AI Agent Robot Icon" },
    "process-automation": { src: "/Gear-sm.png", alt: "Process Automation Gear Icon" },
    "integration-services": { src: "/Belt-sm.png", alt: "Integration Services Conveyor Belt Icon" },
    "custom-solutions": { src: "/og-image.png", alt: "Agent Factory ROI Solutions" }
  };

  return (
    <section id="solutions" className="py-8 px-6">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {SOLUTIONS.map((solution) => {
            const imageData = solutionImages[solution.id as keyof typeof solutionImages];
            return (
              <Card key={solution.id} className="neon-border p-6 transition-all duration-300 hover:shadow-2xl group hover:scale-105 bg-[hsl(var(--secondary-dark))] bg-opacity-30">
                <CardContent className="space-y-4 p-0 text-center">
                  <div className="mx-auto" style={{ width: '5rem', height: '5rem' }}>
                    <div className="w-full h-full rounded-lg border border-[hsl(var(--accent-cyan))] flex items-center justify-center group-hover:animate-float bg-[hsl(var(--accent-cyan))] bg-opacity-10 p-1">
                      <img 
                        src={imageData.src}
                        alt={imageData.alt}
                        className={`w-full h-full drop-shadow-lg scale-110 ${imageData.src.includes('/og-image.png') ? 'object-cover' : 'object-contain'}`}
                      />
                    </div>
                  </div>
                  <div className="space-y-2">
                    <h3 className="text-lg font-semibold text-white">{solution.title}</h3>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      </div>
    </section>
  );
}

function AboutSection() {
  const benefits = [
    "50-80% faster processing times",
    "Guaranteed ROI",
    "Scale revenue without scaling overhead",
    "Consistent quality, 24/7 operations"
  ];

  return (
    <section className="py-20 px-6 bg-[hsl(var(--secondary-dark))] bg-opacity-30">
      <div className="max-w-6xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          <div className="space-y-8">
            <div className="space-y-6">
              <div className="space-y-2">
                <h2 className="text-3xl md:text-4xl font-bold text-white">
                  Stop Losing Revenue to Manual Processes
                </h2>
                <p className="text-xl text-[hsl(var(--accent-cyan))] font-semibold">
                  Your biggest bottlenecks are your biggest growth opportunities.
                </p>
              </div>
              <p className="text-lg text-[hsl(var(--text-light))] leading-relaxed">
                Growing businesses hit the same wall: operations that worked at $1M don't scale to $10M. While you're stuck doing busywork, competitors are capturing the deals you're too busy to pursue.
              </p>
              <div className="space-y-2">
                <h3 className="text-2xl font-bold text-white">
                  Transform Operations, Unlock Growth
                </h3>
                <p className="text-lg text-[hsl(var(--text-light))] leading-relaxed">
                  We automate your most time-consuming processes so your team can focus on what drives revenue: serving customers, closing deals, and expanding markets.
                </p>
              </div>
            </div>
            
          </div>
          
          <div className="relative" style={{ transform: 'translate(-10px, -10px)' }}>
            <img 
              src="/OfficeTransformation-Home.png" 
              alt="Office transformation and business automation" 
              className="rounded-xl shadow-2xl w-full h-auto" 
            />
            <div className="absolute inset-0 bg-gradient-to-t from-[hsl(var(--primary-dark))] from-opacity-50 to-transparent rounded-xl"></div>
          </div>
        </div>
        
        {/* Results Grid - Horizontal Layout */}
        <div className="mt-16 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {benefits.map((benefit, index) => (
            <div key={index} className="flex items-center justify-center space-x-3 p-4 bg-[hsl(var(--secondary-dark))] bg-opacity-50 rounded-lg border border-[hsl(var(--accent-cyan))] border-opacity-20">
              <div className="w-6 h-6 rounded-full bg-[hsl(var(--accent-cyan))] bg-opacity-20 flex items-center justify-center flex-shrink-0">
                <Check className="w-4 h-4 text-[hsl(var(--accent-cyan))]" />
              </div>
              <span className="text-[hsl(var(--text-light))] text-center text-sm font-medium">{benefit}</span>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}



function ROISection() {
  return (
    <section className="py-16 px-6 bg-gradient-to-br from-[hsl(var(--primary-dark))] to-[hsl(var(--secondary-dark))]">
      <div className="max-w-4xl mx-auto text-center space-y-8">
        <div className="space-y-6">
          <h2 className="text-4xl md:text-5xl font-bold text-white leading-tight">
            Stop Guessing Your ROI
          </h2>
          <p className="text-xl md:text-2xl text-[hsl(var(--text-light))] max-w-3xl mx-auto leading-relaxed">
            Let us demonstrate our Agents by talking to our ROI agent.
          </p>
        </div>
        
        <div className="flex justify-center pt-6">
          <Link href="/roi-calculator">
            <Button className="btn-primary px-12 py-6 text-xl font-semibold animate-glow">
              Start Talking →
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}

function CTASection() {
  return (
    <section className="py-6 px-6">
      <div className="max-w-4xl mx-auto text-center space-y-6">
        <div className="space-y-4">
          <h2 className="text-3xl md:text-4xl font-bold text-white">
            Ready to Transform Your Business?
          </h2>
          <p className="text-lg text-[hsl(var(--text-light))] max-w-2xl mx-auto">
            Schedule a free consultation to discover how our AI automation solutions 
            can revolutionize your operations and drive unprecedented growth.
          </p>
        </div>
        
        <div className="flex flex-col sm:flex-row gap-4 justify-center pt-4">
          <Link href="/booking">
            <Button className="btn-primary px-8 py-4 text-lg">
              Book Free Consultation
            </Button>
          </Link>
          <Link href="/blog">
            <Button className="btn-secondary px-8 py-4 text-lg">
              Visit Our Blog
            </Button>
          </Link>
        </div>
      </div>
    </section>
  );
}

export default function HomePage() {
  // Comprehensive structured data for different page types
  const organizationSchema = createOrganizationSchema({
    name: "Agent Factory Pro",
    description: "Enterprise-grade business automation platform that streamlines operations and enhances productivity.",
    url: "https://agentfactory.pro",
    logo: "https://agentfactory.pro/logo.png",
    sameAs: [
      "https://linkedin.com/company/agentfactory",
      "https://twitter.com/agentfactory"
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: "customer service",
      email: "<EMAIL>"
    },
    address: {
      '@type': 'PostalAddress',
      addressCountry: "US"
    }
  });

  const serviceSchemas = SOLUTIONS.map(solution => 
    createServiceSchema({
      name: solution.title,
      description: solution.description,
      serviceType: "Business Automation",
      areaServed: "Global"
    })
  );

  const webPageSchema = createWebPageSchema({
    name: "Agent Factory Pro - Enterprise Business Automation Platform",
    url: "https://agentfactory.pro",
    description: "Transform your business with our comprehensive automation platform. Streamline workflows, enhance productivity, and scale operations with enterprise-grade tools."
  });

  const faqSchema = createFAQSchema([
    {
      question: "What is Agent Factory Pro?",
      answer: "Agent Factory Pro is an enterprise-grade business automation platform that helps companies streamline operations, enhance productivity, and scale efficiently with AI-driven solutions."
    },
    {
      question: "How can automation benefit my business?",
      answer: "Our automation solutions can reduce manual tasks by up to 80%, improve accuracy, accelerate processing times, and allow your team to focus on strategic initiatives that drive growth."
    },
    {
      question: "Is Agent Factory Pro suitable for my industry?",
      answer: "Yes! Our platform is designed to work across various industries including finance, healthcare, manufacturing, retail, and more. We customize solutions to meet your specific industry requirements."
    },
    {
      question: "How quickly can I see results?",
      answer: "Most clients see immediate improvements in efficiency within the first month of implementation, with full ROI typically achieved within 3-6 months depending on the scope of automation."
    }
  ]);

  // Combine all structured data
  const allStructuredData = [
    organizationSchema,
    webPageSchema,
    faqSchema,
    ...serviceSchemas
  ];

  return (
    <MainLayout className="text-white overflow-x-hidden">
      <SEOHead
        title="Agent Factory Co"
        description="Intelligent AI solutions"
        keywords="business automation, enterprise software, workflow management, productivity tools, business process automation, digital transformation, SaaS platform"
        image={`${window.location.origin}/og-image.png`}
        url={window.location.href}
        type="website"
      />
      
      {/* Comprehensive Structured Data for SEO */}
      <StructuredData data={allStructuredData} />
      
      <HeroSection />
      <SolutionsSection />
      <AboutSection />
      <ROISection />
      <CTASection />
    </MainLayout>
  );
}
