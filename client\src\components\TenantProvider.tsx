import { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { crossDomainAuth } from '@/lib/supabaseClient';
import { useAuthContext } from '@/components/auth/AuthProvider';

interface Tenant {
  id: number;
  name: string;
  slug: string;
  status: string;
  plan?: string;
  settings?: Record<string, any>;
}

interface TenantContextType {
  currentTenant: Tenant | null;
  isLoading: boolean;
  error: string | null;
  refetchTenant: () => Promise<void>;
  getDomainInfo: () => any;
}

const TenantContext = createContext<TenantContextType>({
  currentTenant: null,
  isLoading: true,
  error: null,
  refetchTenant: async () => {},
  getDomainInfo: () => null,
});

interface TenantProviderProps {
  children: ReactNode;
}

export function TenantProvider({ children }: TenantProviderProps) {
  const [currentTenant, setCurrentTenant] = useState<Tenant | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { user } = useAuthContext();

  const getDomainInfo = () => {
    return crossDomainAuth.getCurrentDomain();
  };

  const fetchTenantFromSubdomain = async (subdomain: string): Promise<Tenant | null> => {
    try {
      const response = await fetch(`/api/tenants/by-subdomain/${subdomain}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('supabase.auth.token')}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        if (response.status === 404) {
          throw new Error(`Tenant not found for subdomain: ${subdomain}`);
        }
        throw new Error(`Failed to fetch tenant: ${response.statusText}`);
      }

      const tenant = await response.json();
      return tenant;
    } catch (err) {
      console.error('Error fetching tenant from subdomain:', err);
      throw err;
    }
  };

  const refetchTenant = async () => {
    setIsLoading(true);
    setError(null);

    try {
      const domainInfo = getDomainInfo();
      
      if (domainInfo?.type === 'customer' && domainInfo.subdomain) {
        const tenant = await fetchTenantFromSubdomain(domainInfo.subdomain);
        setCurrentTenant(tenant);
      } else {
        // Not on a customer subdomain
        setCurrentTenant(null);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
      setCurrentTenant(null);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch tenant information when component mounts or domain changes
  useEffect(() => {
    const initializeTenant = async () => {
      const domainInfo = getDomainInfo();
      
      // Only fetch tenant if we're on a customer subdomain
      if (domainInfo?.type === 'customer' && domainInfo.subdomain) {
        await refetchTenant();
      } else {
        // Not on a customer subdomain, clear tenant context
        setCurrentTenant(null);
        setIsLoading(false);
        setError(null);
      }
    };

    initializeTenant();
  }, []);

  // Refetch tenant when user authentication changes
  useEffect(() => {
    if (user && currentTenant === null) {
      const domainInfo = getDomainInfo();
      if (domainInfo?.type === 'customer' && domainInfo.subdomain) {
        refetchTenant();
      }
    }
  }, [user]);

  const value: TenantContextType = {
    currentTenant,
    isLoading,
    error,
    refetchTenant,
    getDomainInfo,
  };

  return (
    <TenantContext.Provider value={value}>
      {children}
    </TenantContext.Provider>
  );
}

export function useTenant(): TenantContextType {
  const context = useContext(TenantContext);
  if (context === undefined) {
    throw new Error('useTenant must be used within a TenantProvider');
  }
  return context;
}

// Hook for checking if we're on a tenant subdomain
export function useIsOnTenantSubdomain(): boolean {
  const { getDomainInfo } = useTenant();
  const domainInfo = getDomainInfo();
  return domainInfo?.type === 'customer';
}

// Hook for getting current subdomain
export function useCurrentSubdomain(): string | null {
  const { getDomainInfo } = useTenant();
  const domainInfo = getDomainInfo();
  return domainInfo?.subdomain || null;
} 