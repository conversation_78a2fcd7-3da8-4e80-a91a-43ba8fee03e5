# Task ID: 18
# Title: Implement SSL Certificate Management
# Status: cancelled
# Dependencies: 12
# Priority: medium
# Description: Set up SSL certificate management for main domain and customer subdomains
# Details:
1. Configure wildcard SSL certificate for *.agent-factory.app
2. Set up automated certificate renewal
3. Implement certificate deployment process
4. Configure HTTPS server settings
5. Test SSL configuration across domains
6. Document certificate management procedures
7. Example using Let's Encrypt with Certbot:
```bash
# Install Certbot
apt-get update
apt-get install certbot python3-certbot-nginx

# Request wildcard certificate
certbot certonly --manual \
  --preferred-challenges=dns \
  --email <EMAIL> \
  --server https://acme-v02.api.letsencrypt.org/directory \
  --agree-tos \
  -d agent-factory.app -d *.agent-factory.app

# Set up auto-renewal
echo "0 0,12 * * * root python -c 'import random; import time; time.sleep(random.random() * 3600)' && certbot renew" | sudo tee -a /etc/crontab > /dev/null
```

Node.js HTTPS server configuration:
```javascript
const https = require('https');
const fs = require('fs');
const express = require('express');
const app = express();

// SSL certificate paths
const privateKey = fs.readFileSync('/etc/letsencrypt/live/agent-factory.app/privkey.pem', 'utf8');
const certificate = fs.readFileSync('/etc/letsencrypt/live/agent-factory.app/cert.pem', 'utf8');
const ca = fs.readFileSync('/etc/letsencrypt/live/agent-factory.app/chain.pem', 'utf8');

const credentials = {
  key: privateKey,
  cert: certificate,
  ca: ca
};

// Create HTTPS server
const httpsServer = https.createServer(credentials, app);

httpsServer.listen(443, () => {
  console.log('HTTPS Server running on port 443');
});
```

# Test Strategy:
Verify SSL certificates work for main domain and various subdomains. Test certificate renewal process. Check SSL configuration using online tools like SSL Labs.
