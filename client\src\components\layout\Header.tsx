import { useState } from "react";
import { Link, useLocation } from "wouter";
import { Logo } from "@/components/ui/logo";
import { Button } from "@/components/ui/button";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Sheet,
  SheetContent,
  SheetTrigger,
} from "@/components/ui/sheet";
import { User, Menu, LogOut, Settings, BarChart3 } from "lucide-react";
import { NAVIGATION_ITEMS } from "@/lib/constants";
import { hasPermission } from "@/lib/auth";

export function Header() {
  const [location] = useLocation();
  const { user, logout } = useAuthContext();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);

  const isActive = (href: string) => {
    if (href === "/") return location === "/";
    return location.startsWith(href);
  };

  const handleLogout = () => {
    logout();
  };

  return (
    <header className="relative z-50">
      <div className="max-w-7xl mx-auto px-6 mt-6">
        <div className="neon-border animate-glow bg-[hsl(var(--secondary-dark))] bg-opacity-30 backdrop-blur-md rounded-2xl px-8 py-4">
          <nav className="flex items-center justify-between">
            {/* Logo */}
            <Link href="/" className="neon-glow">
              <Logo size="md" />
            </Link>
            
            {/* Spacer */}
            <div className="flex-1"></div>
            
            {/* Desktop Navigation & User Menu */}
            <div className="flex items-center space-x-6">
              {/* Navigation Items */}
              <div className="hidden md:flex items-center space-x-6">
                {NAVIGATION_ITEMS.map((item) => (
                  item.isButton ? (
                    <Link key={item.href} href={item.href}>
                      <Button className="btn-primary px-6 py-2">
                        {item.label}
                      </Button>
                    </Link>
                  ) : (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={`text-[hsl(var(--text-light))] hover:text-[hsl(var(--accent-cyan))] hover:neon-glow transition-all duration-300 font-medium text-lg ${
                        isActive(item.href) ? "text-[hsl(var(--accent-cyan))] neon-glow" : ""
                      }`}
                    >
                      {item.label}
                    </Link>
                  )
                ))}
              </div>
              {user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button 
                      variant="outline" 
                      size="icon"
                      className="border-[hsl(var(--accent-cyan))] border-opacity-30 hover:border-[hsl(var(--accent-cyan))] hover:border-opacity-100 rounded-full"
                    >
                      <User className="w-5 h-5 text-[hsl(var(--accent-cyan))]" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56">
                    <DropdownMenuLabel>
                      {user.firstName} {user.lastName}
                      <p className="text-xs text-muted-foreground">{user.email}</p>
                    </DropdownMenuLabel>
                    <DropdownMenuSeparator />
                    <DropdownMenuItem asChild>
                      <Link href="/profile">
                        <Settings className="mr-2 h-4 w-4" />
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    {(hasPermission(user.role, "manage_users") || hasPermission(user.role, "manage_blog") || hasPermission(user.role, "manage_system")) && (
                      <DropdownMenuItem asChild>
                        <Link href="/dashboard">
                          <BarChart3 className="mr-2 h-4 w-4" />
                          Dashboard
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuSeparator />
                    <DropdownMenuItem onClick={handleLogout}>
                      <LogOut className="mr-2 h-4 w-4" />
                      Logout
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Link href="/login">
                  <Button 
                    variant="outline"
                    size="icon"
                    className="border-[hsl(var(--accent-cyan))] border-opacity-30 hover:border-[hsl(var(--accent-cyan))] hover:border-opacity-100 rounded-full"
                  >
                    <User className="w-5 h-5 text-[hsl(var(--accent-cyan))]" />
                  </Button>
                </Link>
              )}
              
              {/* Mobile Menu */}
              <Sheet open={mobileMenuOpen} onOpenChange={setMobileMenuOpen}>
                <SheetTrigger asChild>
                  <Button 
                    variant="outline" 
                    size="icon" 
                    className="md:hidden border-[hsl(var(--accent-cyan))] border-opacity-30 hover:border-[hsl(var(--accent-cyan))] hover:border-opacity-100 rounded-full"
                  >
                    <Menu className="w-5 h-5 text-[hsl(var(--accent-cyan))]" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="right" className="w-[300px] bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                  <div className="flex flex-col space-y-4 mt-8">
                    {NAVIGATION_ITEMS.map((item) => (
                      <Link
                        key={item.href}
                        href={item.href}
                        onClick={() => setMobileMenuOpen(false)}
                        className={`text-lg font-medium transition-colors duration-300 ${
                          item.isButton 
                            ? "btn-primary text-center" 
                            : `text-[hsl(var(--text-light))] hover:text-[hsl(var(--accent-cyan))] ${
                                isActive(item.href) ? "text-[hsl(var(--accent-cyan))]" : ""
                              }`
                        }`}
                      >
                        {item.label}
                      </Link>
                    ))}
                  </div>
                </SheetContent>
              </Sheet>
            </div>
          </nav>
        </div>
      </div>
    </header>
  );
}
