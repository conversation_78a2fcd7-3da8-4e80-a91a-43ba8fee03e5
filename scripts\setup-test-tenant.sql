-- Setup Test Tenant Data for Tenant Middleware Testing
-- Run this in Supabase SQL Editor to create test tenant and membership

-- Insert a test tenant
INSERT INTO public.tenants (name, slug, status, plan_config_id, settings) 
VALUES (
  'Test Company', 
  'testco', 
  'active', 
  (SELECT id FROM public.plan_configs WHERE name = 'professional' LIMIT 1),
  '{"features": ["analytics", "api_access"]}'
) ON CONFLICT (slug) DO UPDATE SET
  name = EXCLUDED.name,
  status = EXCLUDED.status,
  plan_config_id = EXCLUDED.plan_config_id;

-- Get the tenant ID for the test company
DO $$
DECLARE
  tenant_id_var INTEGER;
  user_uuid_var UUID;
BEGIN
  -- Get tenant ID
  SELECT id INTO tenant_id_var FROM public.tenants WHERE slug = 'testco';
  
  -- Get the super admin user UUID
  SELECT id INTO user_uuid_var FROM auth.users WHERE email = '<EMAIL>';
  
  -- Create tenant membership for the super admin user
  INSERT INTO public.tenant_memberships (user_id, tenant_id, role, is_active)
  VALUES (user_uuid_var, tenant_id_var, 'owner', true)
  ON CONFLICT (user_id, tenant_id) DO UPDATE SET
    role = EXCLUDED.role,
    is_active = EXCLUDED.is_active;
    
  RAISE NOTICE 'Test tenant setup complete:';
  RAISE NOTICE 'Tenant ID: %, Slug: testco', tenant_id_var;
  RAISE NOTICE 'User UUID: %, Email: <EMAIL>', user_uuid_var;
  RAISE NOTICE 'Membership created with owner role';
END $$;

-- Verify the setup
SELECT 
  t.id as tenant_id,
  t.name as tenant_name,
  t.slug as tenant_slug,
  t.status as tenant_status,
  u.email as user_email,
  tm.role as membership_role,
  tm.is_active as membership_active
FROM public.tenants t
JOIN public.tenant_memberships tm ON tm.tenant_id = t.id
JOIN auth.users u ON u.id = tm.user_id
WHERE t.slug = 'testco'; 