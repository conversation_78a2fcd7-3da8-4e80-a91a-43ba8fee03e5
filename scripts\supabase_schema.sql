-- Complete Multi-Tenant Supabase Database Setup for Agent Factory Pro (NO AUTH TRIGGERS)
-- Run this script in your Supabase SQL Editor to set up the entire database

-- Create user role enum - platform-level roles only
-- lead: signed up, exploring (trial/freemium)
-- customer: converted, paying subscriber
-- support: customer support team, can view customer data for troubleshooting
-- billing_admin: can manage billing, invoices, subscriptions, payment issues
-- sales: sales team, can view leads, manage conversions, access CRM data
-- blog_admin: can manage platform blog content
-- super_admin: platform owner, full access
CREATE TYPE user_role_enum AS ENUM ('lead', 'customer', 'support', 'billing_admin', 'sales', 'blog_admin', 'super_admin');

-- Create tenant status enum
CREATE TYPE tenant_status_enum AS ENUM ('active', 'suspended', 'trial', 'cancelled');

-- Create plan configurations table - admin configurable plans
CREATE TABLE public.plan_configs (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL UNIQUE, -- e.g., "starter", "pro", "enterprise", "custom-50"
  display_name TEXT NOT NULL, -- e.g., "Professional Plan"
  description TEXT, -- Plan description
  max_users INTEGER NOT NULL, -- -1 for unlimited
  monthly_price INTEGER DEFAULT 0, -- Price in cents
  yearly_price INTEGER DEFAULT 0, -- Price in cents
  features JSONB DEFAULT '[]', -- Array of features
  is_active BOOLEAN DEFAULT true NOT NULL,
  is_custom BOOLEAN DEFAULT false NOT NULL, -- Custom plans for specific customers
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create tenants/organizations table
CREATE TABLE public.tenants (
  id SERIAL PRIMARY KEY,
  name TEXT NOT NULL,
  slug TEXT NOT NULL UNIQUE, -- For subdomain (e.g., 'acme' -> acme.agent-factory.app)
  domain TEXT UNIQUE, -- Custom domain if they have one
  status tenant_status_enum DEFAULT 'trial' NOT NULL,
  plan_config_id INTEGER REFERENCES public.plan_configs(id), -- Reference to plan config
  custom_max_users INTEGER, -- Override for custom limits (null = use plan default)
  settings JSONB DEFAULT '{}', -- Tenant-specific settings
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create users table
CREATE TABLE public.users (
  id SERIAL PRIMARY KEY,
  external_id TEXT UNIQUE, -- Links to Supabase Auth user ID
  email TEXT NOT NULL UNIQUE,
  first_name TEXT,
  last_name TEXT,
  business_name TEXT,
  role user_role_enum DEFAULT 'lead' NOT NULL, -- Default new signups are leads
  is_active BOOLEAN DEFAULT true NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create tenant_memberships table (many-to-many: users can belong to multiple tenants)
CREATE TABLE public.tenant_memberships (
  id SERIAL PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES public.users(id) ON DELETE CASCADE,
  tenant_id INTEGER NOT NULL REFERENCES public.tenants(id) ON DELETE CASCADE,
  role TEXT DEFAULT 'member', -- owner, admin, member
  is_active BOOLEAN DEFAULT true NOT NULL,
  joined_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  UNIQUE(user_id, tenant_id)
);

-- Create blog_posts table (tenant-aware)
CREATE TABLE public.blog_posts (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES public.tenants(id), -- NULL for global posts
  title TEXT NOT NULL,
  slug TEXT NOT NULL,
  content TEXT NOT NULL,
  excerpt TEXT,
  featured_image_url TEXT,
  meta_title TEXT,
  meta_description TEXT,
  is_published BOOLEAN DEFAULT false NOT NULL,
  author_id INTEGER REFERENCES public.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  published_at TIMESTAMPTZ,
  UNIQUE(tenant_id, slug) -- Slug unique per tenant
);

-- Create invoices table (tenant-aware)
CREATE TABLE public.invoices (
  id SERIAL PRIMARY KEY,
  tenant_id INTEGER REFERENCES public.tenants(id), -- NULL for platform invoices
  user_id INTEGER REFERENCES public.users(id),
  amount DECIMAL(10,2) NOT NULL,
  status TEXT NOT NULL,
  due_date DATE,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create user_sessions table (for compatibility)
CREATE TABLE public.user_sessions (
  id TEXT PRIMARY KEY,
  user_id INTEGER NOT NULL REFERENCES public.users(id),
  expires_at TIMESTAMPTZ NOT NULL,
  created_at TIMESTAMPTZ DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX idx_plan_configs_name ON public.plan_configs(name);
CREATE INDEX idx_plan_configs_active ON public.plan_configs(is_active);
CREATE INDEX idx_tenants_slug ON public.tenants(slug);
CREATE INDEX idx_tenants_domain ON public.tenants(domain);
CREATE INDEX idx_tenants_plan_config ON public.tenants(plan_config_id);
CREATE INDEX idx_users_external_id ON public.users(external_id);
CREATE INDEX idx_users_email ON public.users(email);
CREATE INDEX idx_tenant_memberships_user ON public.tenant_memberships(user_id);
CREATE INDEX idx_tenant_memberships_tenant ON public.tenant_memberships(tenant_id);
CREATE INDEX idx_blog_posts_tenant_slug ON public.blog_posts(tenant_id, slug);
CREATE INDEX idx_blog_posts_author ON public.blog_posts(author_id);
CREATE INDEX idx_blog_posts_published ON public.blog_posts(is_published);
CREATE INDEX idx_invoices_tenant ON public.invoices(tenant_id);
CREATE INDEX idx_invoices_user ON public.invoices(user_id);

-- Function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Triggers for automatic updated_at
CREATE TRIGGER update_plan_configs_updated_at 
  BEFORE UPDATE ON public.plan_configs 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tenants_updated_at 
  BEFORE UPDATE ON public.tenants 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON public.users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_blog_posts_updated_at 
  BEFORE UPDATE ON public.blog_posts 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Function to get effective max users for a tenant
CREATE OR REPLACE FUNCTION public.get_tenant_max_users(tenant_id_param INTEGER)
RETURNS INTEGER AS $$
DECLARE
  max_users_result INTEGER;
  custom_max INTEGER;
  plan_max INTEGER;
BEGIN
  -- Get custom max users and plan max users
  SELECT 
    t.custom_max_users,
    COALESCE(pc.max_users, 5) -- Default to 5 if no plan config
  INTO custom_max, plan_max
  FROM public.tenants t
  LEFT JOIN public.plan_configs pc ON t.plan_config_id = pc.id
  WHERE t.id = tenant_id_param;
  
  -- Custom max users overrides plan max users
  max_users_result := COALESCE(custom_max, plan_max);
  
  RETURN max_users_result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if tenant can add more users
CREATE OR REPLACE FUNCTION public.check_tenant_user_limit()
RETURNS TRIGGER AS $$
DECLARE
  current_user_count INTEGER;
  max_allowed_users INTEGER;
BEGIN
  -- Get current active user count for this tenant
  SELECT COUNT(*) INTO current_user_count
  FROM public.tenant_memberships
  WHERE tenant_id = NEW.tenant_id AND is_active = true;
  
  -- Get effective max users for this tenant
  SELECT public.get_tenant_max_users(NEW.tenant_id) INTO max_allowed_users;
  
  -- Check if adding this user would exceed the limit (-1 means unlimited)
  IF max_allowed_users > 0 AND current_user_count >= max_allowed_users THEN
    RAISE EXCEPTION 'Tenant has reached maximum user limit of %', max_allowed_users;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get current active user count for a tenant
CREATE OR REPLACE FUNCTION public.get_tenant_user_count(tenant_id_param INTEGER)
RETURNS INTEGER AS $$
DECLARE
  user_count INTEGER;
BEGIN
  SELECT COUNT(*) INTO user_count
  FROM public.tenant_memberships
  WHERE tenant_id = tenant_id_param AND is_active = true;
  
  RETURN user_count;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if tenant can add more users (for API usage)
CREATE OR REPLACE FUNCTION public.can_add_user_to_tenant(tenant_id_param INTEGER)
RETURNS BOOLEAN AS $$
DECLARE
  current_count INTEGER;
  max_allowed INTEGER;
BEGIN
  SELECT public.get_tenant_user_count(tenant_id_param) INTO current_count;
  SELECT public.get_tenant_max_users(tenant_id_param) INTO max_allowed;
  
  -- -1 means unlimited users
  IF max_allowed = -1 THEN
    RETURN true;
  END IF;
  
  RETURN current_count < max_allowed;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to enforce tenant user limits
CREATE TRIGGER check_tenant_user_limit_trigger
  BEFORE INSERT OR UPDATE ON public.tenant_memberships
  FOR EACH ROW 
  WHEN (NEW.is_active = true)
  EXECUTE FUNCTION public.check_tenant_user_limit();

-- Enable RLS (Row Level Security) on all tables
ALTER TABLE public.plan_configs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.tenant_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.blog_posts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_sessions ENABLE ROW LEVEL SECURITY;

-- RLS Policies for plan_configs table
CREATE POLICY "Anyone can view active plans" ON public.plan_configs
  FOR SELECT USING (is_active = true AND is_custom = false);

CREATE POLICY "Service role can manage all plan configs" ON public.plan_configs
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Super admins can manage all plan configs" ON public.plan_configs
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE external_id = auth.uid()::text 
      AND role = 'super_admin'
      AND is_active = true
    )
  );

-- RLS Policies for tenants table
CREATE POLICY "Service role can manage all tenants" ON public.tenants
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Super admins can manage all tenants" ON public.tenants
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE external_id = auth.uid()::text 
      AND role = 'super_admin'
      AND is_active = true
    )
  );

CREATE POLICY "Tenant members can view their tenant" ON public.tenants
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.tenant_memberships tm
      JOIN public.users u ON u.id = tm.user_id
      WHERE u.external_id = auth.uid()::text 
      AND tm.tenant_id = tenants.id
      AND tm.is_active = true
    )
  );

-- RLS Policies for users table
CREATE POLICY "Users can view own profile" ON public.users
  FOR SELECT USING (auth.uid()::text = external_id);

CREATE POLICY "Users can update own profile" ON public.users
  FOR UPDATE USING (auth.uid()::text = external_id)
  WITH CHECK (
    auth.uid()::text = external_id AND
    role = (SELECT role FROM public.users WHERE external_id = auth.uid()::text) AND
    is_active = (SELECT is_active FROM public.users WHERE external_id = auth.uid()::text)
  );

CREATE POLICY "Service role can manage all users" ON public.users
  FOR ALL USING (auth.jwt() ->> 'role' = 'service_role');

CREATE POLICY "Super admins can manage all users" ON public.users
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE external_id = auth.uid()::text 
      AND role = 'super_admin'
      AND is_active = true
    )
  );

CREATE POLICY "Support can view all users" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE external_id = auth.uid()::text 
      AND role IN ('support', 'billing_admin')
      AND is_active = true
    )
  );

CREATE POLICY "Sales can view leads and customers" ON public.users
  FOR SELECT USING (
    EXISTS (
      SELECT 1 FROM public.users 
      WHERE external_id = auth.uid()::text 
      AND role = 'sales'
      AND is_active = true
    ) AND role IN ('lead', 'customer')
  );

-- Insert default plan configurations
INSERT INTO public.plan_configs (name, display_name, description, max_users, monthly_price, yearly_price, features, is_active, is_custom) VALUES
('trial', 'Trial Plan', 'Free trial with limited features', 3, 0, 0, '["Basic features", "Email support", "3 users max"]', true, false),
('starter', 'Starter Plan', 'Perfect for small teams', 5, 2900, 29000, '["All basic features", "Priority support", "5 users", "Basic analytics"]', true, false),
('professional', 'Professional Plan', 'For growing businesses', 25, 9900, 99000, '["All starter features", "Advanced analytics", "25 users", "API access", "Custom integrations"]', true, false),
('enterprise', 'Enterprise Plan', 'For large organizations', -1, 29900, 299000, '["All professional features", "Unlimited users", "Dedicated support", "Custom features", "SLA guarantee"]', true, false);

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.plan_configs TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.tenants TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE ON public.users TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.tenant_memberships TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.blog_posts TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.invoices TO anon, authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.user_sessions TO anon, authenticated;
GRANT USAGE, SELECT ON ALL SEQUENCES IN SCHEMA public TO anon, authenticated;

-- Success message
DO $$
BEGIN
  RAISE NOTICE 'Agent Factory Pro Multi-Tenant database setup completed successfully!';
  RAISE NOTICE 'Tables created: plan_configs, tenants, users, tenant_memberships, blog_posts, invoices, user_sessions';
  RAISE NOTICE 'User roles: lead, customer, support, billing_admin, sales, blog_admin, super_admin';
  RAISE NOTICE 'Default plans: trial (3 users), starter (5 users), professional (25 users), enterprise (unlimited)';
  RAISE NOTICE 'Configurable user limits with custom overrides per tenant';
  RAISE NOTICE 'Tenant-aware data isolation with RLS policies enabled';
  RAISE NOTICE 'Ready for multi-tenant SaaS architecture!';
  RAISE NOTICE 'NOTE: Auth triggers removed - set up webhook for user profile creation';
END $$;