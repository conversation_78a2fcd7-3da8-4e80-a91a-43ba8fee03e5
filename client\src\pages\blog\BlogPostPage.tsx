import { useQuery } from "@tanstack/react-query";
import { Suspense, lazy } from "react";
import { useRoute } from "wouter";
import { MainLayout } from "@/components/layouts/MainLayout";
import { BlogPostSkeleton } from "@/components/blog/BlogLoadingStates";
import type { BlogPost as BlogPostType } from "@shared/schema";

// Lazy load the blog post component for optimal performance
const BlogPost = lazy(() => import("@/components/blog/BlogPost"));

/**
 * Optimized Blog Post Page with Code Splitting
 * 
 * Performance Optimizations:
 * - Lazy loading of BlogPost component reduces initial bundle size
 * - Dedicated loading skeleton improves perceived performance
 * - Component-level code splitting for better caching
 * - SEO optimization built into the lazy-loaded component
 */
export default function BlogPostPage() {
  const [, params] = useRoute('/blog/:slug');
  const slug = params?.slug;

  const { data: post, isLoading, error } = useQuery<BlogPostType>({
    queryKey: [`/api/blog/posts/${slug}`],
    enabled: !!slug,
  });

  if (isLoading) {
    return (
      <MainLayout>
        <BlogPostSkeleton />
      </MainLayout>
    );
  }

  if (error || !post) {
    return (
      <MainLayout>
        <div className="max-w-4xl mx-auto px-4 py-12">
          <div className="text-center space-y-4">
            <h1 className="text-2xl font-bold">Post Not Found</h1>
            <p className="text-muted-foreground">
              The blog post you're looking for doesn't exist or has been removed.
            </p>
          </div>
        </div>
      </MainLayout>
    );
  }

  return (
    <MainLayout>
      <Suspense fallback={
        <div className="max-w-4xl mx-auto px-4 py-12 text-center">
          <div className="inline-flex items-center gap-2 text-muted-foreground">
            <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
            Loading article...
          </div>
        </div>
      }>
        <BlogPost post={post} />
      </Suspense>
    </MainLayout>
  );
}