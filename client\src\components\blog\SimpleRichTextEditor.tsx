import { useRef, useEffect, useState } from 'react';
import { Button } from '@/components/ui/button';
import { 
  Bold, 
  Italic, 
  Underline as UnderlineIcon,
  List, 
  ListOrdered, 
  Quote,
  Heading1,
  Heading2,
  Link as LinkIcon,
  Code,
  Undo,
  Redo,
  Image as ImageIcon,
  Loader2
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { uploadImageToCloudinary, validateImageFile } from '@/lib/cloudinary';
import DOMPurify from 'dompurify';

interface SimpleRichTextEditorProps {
  content: string;
  onChange: (content: string) => void;
  placeholder?: string;
  className?: string;
}

// Sanitize HTML content to prevent XSS attacks
const sanitizeHTML = (html: string): string => {
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: [
      'p', 'br', 'strong', 'b', 'em', 'i', 'u', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'ul', 'ol', 'li', 'blockquote', 'a', 'img', 'code', 'pre', 'span', 'div'
    ],
    ALLOWED_ATTR: ['href', 'src', 'alt', 'title', 'class', 'style'],
    ALLOW_DATA_ATTR: false
  });
};

export function SimpleRichTextEditor({ 
  content, 
  onChange, 
  placeholder = "Start writing...",
  className 
}: SimpleRichTextEditorProps) {
  const editorRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isEmpty, setIsEmpty] = useState(!content);
  const [history, setHistory] = useState<string[]>([content]);
  const [historyIndex, setHistoryIndex] = useState(0);
  const [isUploadingImage, setIsUploadingImage] = useState(false);

  useEffect(() => {
    setIsEmpty(!content || content === '<p><br></p>' || content === '<br>');
  }, [content]);

  const execCommand = (command: string, value?: string) => {
    document.execCommand(command, false, value);
    editorRef.current?.focus();
    handleContentChange();
  };

  const handleContentChange = () => {
    if (editorRef.current) {
      const newContent = editorRef.current.innerHTML;
      onChange(newContent);
      setIsEmpty(!newContent || newContent === '<p><br></p>' || newContent === '<br>');
      
      // Add to history
      const newHistory = history.slice(0, historyIndex + 1);
      newHistory.push(newContent);
      setHistory(newHistory);
      setHistoryIndex(newHistory.length - 1);
    }
  };

  const handleUndo = () => {
    if (historyIndex > 0) {
      const newIndex = historyIndex - 1;
      setHistoryIndex(newIndex);
      if (editorRef.current) {
        // Use textContent to prevent XSS, then trigger onChange to update properly
        const historyContent = history[newIndex];
        onChange(historyContent);
      }
    }
  };

  const handleRedo = () => {
    if (historyIndex < history.length - 1) {
      const newIndex = historyIndex + 1;
      setHistoryIndex(newIndex);
      if (editorRef.current) {
        // Use textContent to prevent XSS, then trigger onChange to update properly
        const historyContent = history[newIndex];
        onChange(historyContent);
      }
    }
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      execCommand('createLink', url);
    }
  };

  const handleImageSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validationError = validateImageFile(file);
    if (validationError) {
      alert(validationError);
      return;
    }

    setIsUploadingImage(true);

    try {
      const result = await uploadImageToCloudinary(file);
      
      // Insert image at cursor position
      const img = `<img src="${result.secure_url}" alt="" style="max-width: 100%; height: auto;" />`;
      execCommand('insertHTML', img);
      
    } catch (err) {
      alert(err instanceof Error ? err.message : 'Failed to upload image');
    } finally {
      setIsUploadingImage(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const triggerImageUpload = () => {
    fileInputRef.current?.click();
  };

  const ToolbarButton = ({ 
    onClick, 
    icon: Icon, 
    title,
    active = false 
  }: { 
    onClick: () => void; 
    icon: any; 
    title: string;
    active?: boolean;
  }) => (
    <Button
      type="button"
      variant="ghost"
      size="sm"
      onClick={onClick}
      title={title}
      className={cn(
        "h-8 w-8 p-0 text-[hsl(var(--text-light))] hover:text-white hover:bg-[hsl(var(--primary-dark))]",
        active && "bg-[hsl(var(--primary-dark))] text-white"
      )}
    >
      <Icon className={cn("h-4 w-4", Icon === Loader2 && "animate-spin")} />
    </Button>
  );

  return (
    <div className={cn("space-y-2", className)}>
      {/* Hidden file input for image upload */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleImageSelect}
        className="hidden"
        aria-label="Upload image file"
      />
      
      {/* Toolbar */}
      <div className="flex flex-wrap gap-1 p-2 bg-[hsl(var(--secondary-dark))] rounded-lg border border-[hsl(var(--accent-cyan))] border-opacity-30">
        <div className="flex gap-1 pr-2 border-r border-[hsl(var(--accent-cyan))] border-opacity-30">
          <ToolbarButton
            onClick={handleUndo}
            icon={Undo}
            title="Undo"
          />
          <ToolbarButton
            onClick={handleRedo}
            icon={Redo}
            title="Redo"
          />
        </div>
        
        <div className="flex gap-1 pr-2 border-r border-[hsl(var(--accent-cyan))] border-opacity-30">
          <ToolbarButton
            onClick={() => execCommand('formatBlock', 'h1')}
            icon={Heading1}
            title="Heading 1"
          />
          <ToolbarButton
            onClick={() => execCommand('formatBlock', 'h2')}
            icon={Heading2}
            title="Heading 2"
          />
        </div>

        <div className="flex gap-1 pr-2 border-r border-[hsl(var(--accent-cyan))] border-opacity-30">
          <ToolbarButton
            onClick={() => execCommand('bold')}
            icon={Bold}
            title="Bold"
          />
          <ToolbarButton
            onClick={() => execCommand('italic')}
            icon={Italic}
            title="Italic"
          />
          <ToolbarButton
            onClick={() => execCommand('underline')}
            icon={UnderlineIcon}
            title="Underline"
          />
        </div>

        <div className="flex gap-1 pr-2 border-r border-[hsl(var(--accent-cyan))] border-opacity-30">
          <ToolbarButton
            onClick={() => execCommand('insertUnorderedList')}
            icon={List}
            title="Bullet List"
          />
          <ToolbarButton
            onClick={() => execCommand('insertOrderedList')}
            icon={ListOrdered}
            title="Numbered List"
          />
          <ToolbarButton
            onClick={() => execCommand('formatBlock', 'blockquote')}
            icon={Quote}
            title="Quote"
          />
        </div>

        <div className="flex gap-1">
          <ToolbarButton
            onClick={insertLink}
            icon={LinkIcon}
            title="Insert Link"
          />
          <ToolbarButton
            onClick={triggerImageUpload}
            icon={isUploadingImage ? Loader2 : ImageIcon}
            title="Insert Image"
          />
          <ToolbarButton
            onClick={() => execCommand('formatBlock', 'pre')}
            icon={Code}
            title="Code Block"
          />
        </div>
      </div>

      {/* Editor */}
      <div className="relative">
        <div
          ref={editorRef}
          contentEditable
          className={cn(
            "min-h-[300px] p-4 bg-[hsl(var(--primary-dark))] border border-[hsl(var(--accent-cyan))] border-opacity-30 rounded-lg",
            "text-white focus:outline-none focus:ring-2 focus:ring-[hsl(var(--accent-cyan))] focus:ring-opacity-50",
            "prose prose-invert max-w-none",
            "[&_h1]:text-2xl [&_h1]:font-bold [&_h1]:mb-4",
            "[&_h2]:text-xl [&_h2]:font-bold [&_h2]:mb-3",
            "[&_p]:mb-4 [&_ul]:mb-4 [&_ol]:mb-4",
            "[&_ul]:list-disc [&_ul]:pl-6",
            "[&_ol]:list-decimal [&_ol]:pl-6",
            "[&_blockquote]:border-l-4 [&_blockquote]:border-[hsl(var(--accent-cyan))] [&_blockquote]:pl-4 [&_blockquote]:italic",
            "[&_pre]:bg-[hsl(var(--secondary-dark))] [&_pre]:p-4 [&_pre]:rounded [&_pre]:overflow-x-auto",
            "[&_code]:bg-[hsl(var(--secondary-dark))] [&_code]:px-1 [&_code]:rounded",
            "[&_a]:text-[hsl(var(--accent-cyan))] [&_a]:underline"
          )}
          onInput={handleContentChange}
          onBlur={handleContentChange}
          dangerouslySetInnerHTML={{ __html: sanitizeHTML(content) }}
        />
        {isEmpty && (
          <div className="absolute top-4 left-4 text-[hsl(var(--text-light))] pointer-events-none">
            {placeholder}
          </div>
        )}
      </div>

      {/* Helper text */}
      <p className="text-xs text-[hsl(var(--text-light))]">
        Use the toolbar to format your text. The editor supports HTML content.
      </p>
    </div>
  );
} 