# Task ID: 13
# Title: Create Customer Provisioning System
# Status: done
# Dependencies: 3, 12
# Priority: medium
# Description: Implement system for manually provisioning new customer tenants
# Details:
1. Create tenant management database tables
2. Implement API endpoints for tenant CRUD operations
3. Create admin interface for tenant management
4. Implement subdomain registration and validation
5. Create user assignment to tenants
6. Implement role assignment within tenants
7. Example tenant creation function:
```javascript
async function createTenant(tenantData, adminUserId) {
  const { name, subdomain, plan } = tenantData;
  
  // Start a transaction
  const { data, error } = await supabase.rpc('create_new_tenant', {
    tenant_name: name,
    tenant_subdomain: subdomain,
    tenant_plan: plan,
    admin_user_id: adminUserId
  });
  
  if (error) throw error;
  
  return data;
}

// Example stored procedure in PostgreSQL
/*
CREATE OR REPLACE FUNCTION create_new_tenant(
  tenant_name TEXT,
  tenant_subdomain TEXT,
  tenant_plan TEXT,
  admin_user_id UUID
) RETURNS UUID AS $$
DECLARE
  new_tenant_id UUID;
BEGIN
  -- Insert new tenant
  INSERT INTO tenants (name, subdomain, plan, created_at)
  VALUES (tenant_name, tenant_subdomain, tenant_plan, NOW())
  RETURNING id INTO new_tenant_id;
  
  -- Assign admin user to tenant
  INSERT INTO tenant_users (tenant_id, user_id, role, created_at)
  VALUES (new_tenant_id, admin_user_id, 'admin', NOW());
  
  -- Create initial resources for tenant
  -- (Add any default data the tenant needs)
  
  RETURN new_tenant_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
*/
```

# Test Strategy:
Test tenant creation process end-to-end. Verify subdomain validation works correctly. Test user assignment to tenants with different roles. Verify tenant isolation after provisioning.
