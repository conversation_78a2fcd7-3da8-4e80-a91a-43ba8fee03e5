<svg width="1200" height="630" viewBox="0 0 1200 630" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="bg" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" stop-color="#1a1b3e"/>
      <stop offset="100%" stop-color="#2d2f5a"/>
    </linearGradient>
    <linearGradient id="accent" x1="0%" y1="0%" x2="100%" y2="0%">
      <stop offset="0%" stop-color="#00d4ff"/>
      <stop offset="100%" stop-color="#0099cc"/>
    </linearGradient>
  </defs>
  
  <!-- Background -->
  <rect width="1200" height="630" fill="url(#bg)"/>
  
  <!-- Geometric pattern -->
  <circle cx="100" cy="100" r="60" fill="#00d4ff" opacity="0.1"/>
  <circle cx="1100" cy="530" r="80" fill="#00d4ff" opacity="0.15"/>
  <rect x="950" y="50" width="120" height="120" fill="#00d4ff" opacity="0.1" transform="rotate(45 1010 110)"/>
  
  <!-- Main content -->
  <text x="100" y="200" font-family="Arial, sans-serif" font-size="72" font-weight="bold" fill="white">
    Agent Factory Pro
  </text>
  
  <text x="100" y="280" font-family="Arial, sans-serif" font-size="32" fill="#e5e7eb">
    Business Automation Platform
  </text>
  
  <text x="100" y="350" font-family="Arial, sans-serif" font-size="24" fill="#9ca3af" font-weight="300">
    Streamline operations with AI-powered workflows
  </text>
  
  <!-- Accent line -->
  <rect x="100" y="400" width="300" height="4" fill="url(#accent)"/>
  
  <!-- Tech elements -->
  <g transform="translate(800, 250)">
    <!-- Automation icon -->
    <circle cx="50" cy="50" r="40" stroke="#00d4ff" stroke-width="2" fill="none"/>
    <circle cx="50" cy="50" r="15" fill="#00d4ff"/>
    <line x1="20" y1="50" x2="35" y2="50" stroke="#00d4ff" stroke-width="2"/>
    <line x1="65" y1="50" x2="80" y2="50" stroke="#00d4ff" stroke-width="2"/>
    <line x1="50" y1="20" x2="50" y2="35" stroke="#00d4ff" stroke-width="2"/>
    <line x1="50" y1="65" x2="50" y2="80" stroke="#00d4ff" stroke-width="2"/>
  </g>
</svg>