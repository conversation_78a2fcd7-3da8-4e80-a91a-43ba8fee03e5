File to Modify: client/src/pages/admin/UserAdminPage.tsx
Current State: The UserAdminPage.tsx currently displays a list/grid of users with existing search and filter capabilities for user attributes like email, role, and status.
New Requirement: Refactor this page to group users by their businessName (a direct string property of the User object). Special visual treatment is required for users belonging to "Agent Factory" (the internal business).
Objective:
Update UserAdminPage.tsx to visually group users based on their businessName. Ensure users from "AgentFactory" are highlighted or visually distinct, and unaffiliated users are grouped appropriately. User creation/editing forms must include businessName management.
Key UI Changes & Functionality:
Visual Grouping by businessName:
Display users under headings derived from their businessName (e.g., "Acme Corp," "Beta Solutions," "Agent Factory," "Unaffiliated Users").
Each businessName (or lack thereof) should act as a clear section header.
Special Handling for "Agent Factory" Users:
Users whose businessName is "Agent Factory" (or a consistent internal identifier you define for mock data, e.g., AGENT_FACTORY_INTERNAL_ID) should be visually highlighted or their group made prominent.
Consider placing the "Agent Factory" user group at the top of the list or giving its section header a distinct style (e.g., different color, icon) so they are immediately visible.
Consider using collapsible/expandable sections for each businessName group to keep the UI manageable.
User cards/entries within each group should retain all existing information (name, email, role, status, edit/delete buttons).
businessName Field in User Forms:
Assumption for Mock Data: For UI development, assume each User object will have a businessName: string property.
Some users will have businessName: "Agent Factory".
Some users will have other business names (e.g., "Acme Corp").
Some users might have a null, undefined, or empty string for businessName (these are unaffiliated).
Create User Form:
Add a "Business Name" input field to the "Create New User" dialog.
If an admin is creating an internal user, they should be able to type "Agent Factory" here.
Edit User Form:
Add a "Business Name" input field to the "Edit User" dialog.
Handling Unaffiliated Users:
Users without a businessName (null, undefined, or empty string) should be grouped under a distinct section titled "Unaffiliated Users".
Search and Filter Interaction:
Existing search (by name/email) and filters (by role/status) should continue to function, refining users within their respective businessName groupings (including "Agent Factory" and "Unaffiliated Users").
Filter by businessName (Consideration): While grouping is primary, a dropdown with unique businessName values (including "Agent Factory" and an option for "Unaffiliated Users") could be a useful secondary filter. Focus on visual grouping first; Cursor can assist with advanced filtering later if necessary.
UI Components & Styling:
Use existing UI components from @/components/ui/... (Shadcn UI).
Maintain the existing visual style, including "glass-card" aesthetic. Apply subtle but clear highlighting for the "Agent Factory" group/users.
Workflow & Collaboration:
Your primary focus is the frontend UI/UX for grouping by businessName, highlighting "Agent Factory" users, and managing the businessName field in forms.
Use mock data for User objects, covering all scenarios (Agent Factory users, other business users, unaffiliated users).
Cursor (the AI agent working with the project owner) will handle backend schema updates, API modifications, and integration with live data.
Deliverables:
The updated client/src/pages/admin/UserAdminPage.tsx file.
A brief note on your mock data structure, specifically how you've represented "Agent Factory" users and unaffiliated users.
A screenshot or description of how "Agent Factory" users are visually distinguished.
Example UI Flow (Conceptual):
Page loads, users are shown grouped:
AGENT FACTORY (Highlighted section or user items)
Admin User 1 (Details...)
Support User 2 (Details...)
ACME CORP
Client User A (Details...)
BETA SOLUTIONS
Client User B (Details...)
UNAFFILIATED USERS
Individual User C (Details...)
Before you start:
Please confirm your understanding of these refined requirements, especially the visual distinction for "Agent Factory" users and the grouping of unaffiliated users.
Propose how you plan to visually distinguish "Agent Factory" users/group if you have initial ideas.