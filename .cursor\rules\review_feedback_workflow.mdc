---
description: 
globs: 
alwaysApply: true
---
# Review and Feedback Workflow

## Overview

The review and feedback workflow establishes a structured process for evaluating code quality, identifying potential issues, and implementing improvements. This phase is critical for preventing technical debt accumulation and ensuring that the codebase remains maintainable throughout the project lifecycle. By formalizing the review process, this workflow addresses the challenge of AI coders implementing conflicting solutions or getting stuck in ineffective fix loops.

## Detailed Procedure

### Preparation for Review

When code is ready for review at a checkpoint or feature completion, prepare the review environment by ensuring that <PERSON><PERSON><PERSON> has access to the complete context. This preparation involves:

1. Updating the CONTEXT.md file with the current development status, including any challenges encountered or decisions made during implementation.
2. Creating a REVIEW_REQUEST.md file that specifies the scope of the review, highlighting specific areas of concern and providing references to relevant requirements from the PRD.
3. Ensuring that all code is properly committed and pushed to the GitHub repository with descriptive commit messages that explain the purpose and approach of each change.

This preparation phase ensures that <PERSON><PERSON><PERSON> has all necessary context to perform an effective review, even if the review occurs in a new session where previous context might be lost.

### Comprehensive Code Review

Cursor performs a comprehensive code review that examines multiple dimensions of code quality:

1. **Functional Correctness**: Verification that the implementation meets the requirements specified in the PRD and task description.
2. **Architectural Integrity**: Assessment of how well the implementation fits within the overall system architecture and adheres to established design patterns.
3. **Code Quality**: Evaluation of code readability, maintainability, and adherence to project coding standards.
4. **Performance Considerations**: Identification of potential performance bottlenecks or inefficient implementations.
5. **Security Vulnerabilities**: Detection of security issues or unsafe practices.
6. **Technical Debt Risk**: Assessment of areas where shortcuts or temporary solutions might create future maintenance challenges.

The review should be documented in a structured format that categorizes issues by severity (critical, major, minor) and type (functional, architectural, quality, performance, security). Each issue should include:

1. A clear description of the problem
2. The specific location in the code (file and line numbers)
3. An explanation of why it's problematic
4. Recommended approaches for resolution

This structured approach ensures that feedback is specific, actionable, and prioritized.

### Feedback Documentation

After completing the review, Cursor should document the feedback in a REVIEW_FEEDBACK.md file that follows a consistent template:

```markdown
# Code Review: [Feature/Component Name]

## Summary
[Overall assessment of the code quality and major concerns]

## Critical Issues
[List of issues that must be addressed before merging]

## Major Issues
[List of significant issues that should be addressed in the current development cycle]

## Minor Issues
[List of less critical issues that could be addressed if time permits]

## Positive Aspects
[Acknowledgment of well-implemented components or approaches]

## Recommendations
[Strategic recommendations for improving the implementation]
```

This documentation serves as a persistent record of the review process and helps maintain context across sessions. It also provides a clear roadmap for implementing improvements.

### Feedback Discussion and Clarification

Before implementing changes based on the review, schedule a brief discussion session to clarify any ambiguous feedback or complex recommendations. This discussion can occur through:

1. Adding comments to specific sections of the REVIEW_FEEDBACK.md file
2. Creating a FEEDBACK_CLARIFICATION.md file for more extensive discussions
3. Direct conversation with Cursor in a new session, referencing specific feedback items

This clarification phase ensures that Replit has a clear understanding of the required changes and prevents misinterpretation of feedback.

### Prioritized Implementation Plan

Based on the review feedback and clarification, create a prioritized implementation plan that organizes the necessary changes into logical groups. This plan should be documented in an IMPLEMENTATION_PLAN.md file that includes:

1. A prioritized list of issues to address, starting with critical issues
2. Specific approaches for implementing each change
3. Dependencies between changes that affect the implementation order
4. Estimated complexity for each change

This structured approach prevents Replit from making haphazard changes that might introduce new issues or conflicts.

### Feedback Implementation

Replit implements the changes according to the prioritized implementation plan, working through issues in order of priority. For each issue:

1. Create a specific commit that addresses only that issue
2. Include a commit message that references the specific feedback item being addressed
3. Document any significant implementation decisions or challenges in the CONTEXT.md file

This granular approach to implementation makes the changes easier to review and revert if necessary, reducing the risk of introducing new problems while fixing existing ones.

### Verification Review

After implementing all critical and major issues, request a verification review from Cursor. This review should focus specifically on verifying that the identified issues have been properly addressed and that no new issues have been introduced during the implementation.

The verification review should be documented in a VERIFICATION_REVIEW.md file that follows a simple template:

```markdown
# Verification Review: [Feature/Component Name]

## Resolved Issues
[List of issues that have been successfully addressed]

## Partially Resolved Issues
[List of issues that have been partially addressed but require further work]

## Unresolved Issues
[List of issues that have not been adequately addressed]

## New Issues
[List of new issues introduced during the implementation of changes]

## Next Steps
[Recommendations for proceeding with the development]
```

This verification process ensures that the feedback loop is closed and that all significant issues are properly addressed before proceeding with further development.

### Learning and Pattern Recognition

To prevent recurring issues, maintain a PATTERNS.md file that documents common patterns of issues identified during reviews. This document should categorize recurring issues and provide standard approaches for avoiding or addressing them.

For each pattern, include:

1. A description of the issue pattern
2. Examples from the codebase where it has occurred
3. Recommended approaches for avoiding or addressing the issue
4. References to relevant best practices or design principles

This pattern recognition helps both AI agents learn from past mistakes and prevents the recurrence of similar issues in future development.

### Review Metrics and Improvement

Track metrics related to the review process to identify trends and opportunities for improvement. These metrics might include:

1. Number of issues identified by category and severity
2. Percentage of issues successfully resolved on first attempt
3. Common types of issues that recur across multiple reviews
4. Time required to implement and verify changes

Document these metrics in a REVIEW_METRICS.md file that is updated after each review cycle. Periodically analyze these metrics to identify areas where the development process could be improved or where additional guidance might be needed.

This data-driven approach to process improvement helps refine the workflow over time and addresses systemic issues that might be contributing to technical debt.

