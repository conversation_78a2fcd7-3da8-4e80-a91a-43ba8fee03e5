Prompt #1: End-of-Session "Save State" Command
Give this prompt to me at the end of your work session. I will automatically write the report to SESSION_CONTEXT.md.
> Your command to me:
> "It's the end of our session. Update the SESSION_CONTEXT.md file with a detailed End-of-Session Report. The report must include:
>
> 1. Session Role
> 2. Tasks Completed
> 3. Key Accomplishments & Decisions
> 4. Files Changed
> 5. Current Git Status
> 6. Next Task
> 7. Blockers / Open Questions
>
> Overwrite the existing file with this new report."
When you give me this command, I will use my tools to generate the report and save it directly to SESSION_CONTEXT.md.

Prompt #2: Start-of-Session "Load State" Command
Give this prompt to me at the beginning of a new session. I will automatically read the file to get my context.

> Your prompt to me:
> "It's the start of a new session. Read your context from the SESSION_CONTEXT.md file.
>
> Once you have read the file, confirm you have assimilated the context by summarizing the 'Next Task' and any 'Blockers' in one sentence. Then, wait for my command to proceed."
When you give me this command, I will use my tools to read SESSION_CONTEXT.md and continue exactly where we left off.
