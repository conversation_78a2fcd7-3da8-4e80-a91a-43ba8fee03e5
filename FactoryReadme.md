# Agent Factory Pro

A **multi-tenant SaaS platform** for building and managing AI agents with enterprise-grade architecture, configurable subscription plans, and comprehensive role-based access control.

## 🏗️ Architecture Overview

Agent Factory Pro is built as a true multi-tenant SaaS application with complete data isolation, configurable user limits, and subdomain support for customer organizations.

### 🎭 User Role System

**Customer Journey Roles:**
- **`lead`** - Just signed up, exploring platform (trial/freemium)
- **`customer`** - Converted, paying subscriber, owns tenant(s)

**Internal Team Roles:**
- **`support`** - Customer support team (view users/invoices for troubleshooting)
- **`billing_admin`** - Billing/finance team (full invoice management)
- **`sales`** - Sales team (view leads/customers for CRM)
- **`blog_admin`** - Content team (manage platform blog)
- **`super_admin`** - Platform owner (full access to everything)

**Tenant-Level Roles** (within customer apps):
- **`owner`** - Customer who owns the tenant
- **`admin`** - Can manage tenant settings and users
- **`member`** - Regular user within the tenant

### 💰 Configurable Plan System

**Default Plans:**
- **Trial Plan**: 3 users, $0/month - Free trial with limited features
- **Starter Plan**: 5 users, $29/month - Perfect for small teams  
- **Professional Plan**: 25 users, $99/month - For growing businesses
- **Enterprise Plan**: Unlimited users, $299/month - For large organizations

**Admin Flexibility:**
- Create custom plans with any user limit (3, 50, 300, unlimited)
- Set any pricing (monthly/yearly in cents)
- Override limits per tenant for special deals
- Mark plans as custom for specific customers

### 🏢 Multi-Tenant Architecture

**How It Works:**
1. **Plan Configs Table** - Admin-configurable subscription plans
2. **Tenants Table** - Customer organizations with subdomain support
3. **Tenant Memberships** - User-tenant relationships with role management
4. **Data Isolation** - Complete separation via Row Level Security (RLS)

**User Flow:**
1. User registers on `www.agent-factory.io` → Gets `lead` role
2. Lead explores platform with trial features
3. Lead converts → Role changes to `customer` + tenant created
4. Customer accesses `customer1.agent-factory.app` with same login
5. Customer invites team → Members get tenant memberships
6. All data isolated per tenant with automatic user limit enforcement

### 🔒 Security & Data Isolation

- **JWT Authentication** via Supabase Auth
- **Row Level Security (RLS)** for complete tenant data isolation
- **Automatic user limit enforcement** via database triggers
- **Real-time auth state** across all domains
- **Cross-domain session support**

## 🛠️ Tech Stack

### Frontend
- **React 18** + **TypeScript** + **Vite**
- **Tailwind CSS** + **shadcn/ui** components
- **React Query** for data fetching
- **React Router** for navigation

### Backend
- **Node.js** + **Express**
- **Drizzle ORM** with **PostgreSQL**
- **Supabase** for authentication and database
- **JWT tokens** for stateless authentication

### Database
- **Supabase PostgreSQL** with multi-tenant schema
- **Row Level Security (RLS)** for data isolation
- **Automatic triggers** for user management
- **Helper functions** for user limit enforcement

### Infrastructure
- **Replit** for development and hosting
- **Supabase** for production database and auth
- **Subdomain routing** for tenant isolation

## 🚀 Features

### Core Platform
- **Multi-Agent Architecture** - Support for various AI agent types
- **Business Process Automation** - Streamlined workflows
- **Analytics Dashboard** - Real-time insights and ROI tracking
- **Integration Hub** - Connect with popular business tools

### SaaS Features
- **Multi-Tenant Architecture** - Complete customer isolation
- **Configurable Plans** - Any user limit, any pricing
- **User Limit Enforcement** - Automatic database-level enforcement
- **Subdomain Support** - `customer.agent-factory.app`
- **Custom Domains** - Support for `customer.com` → tenant mapping
- **Team Management** - Role-based access within tenants

### Admin Features
- **Plan Management** - Create/modify subscription plans
- **User Management** - Comprehensive role-based access control
- **Tenant Management** - Create and manage customer organizations
- **Billing Integration** - Ready for payment processor integration
- **Support Tools** - Customer support team access controls

## 📊 Database Schema

### Core Tables
- **`plan_configs`** - Configurable subscription plans
- **`tenants`** - Customer organizations
- **`users`** - User profiles and platform roles
- **`tenant_memberships`** - User-tenant relationships
- **`blog_posts`** - Content (global or tenant-specific)
- **`invoices`** - Billing records

### Key Features
- **Automatic user limit enforcement** via triggers
- **Helper functions** for user management
- **RLS policies** for data isolation
- **Audit trails** for plan changes

## 🔧 Development

### Prerequisites
- Node.js 18+
- Supabase account
- Replit account (optional)

### Environment Variables
```env
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_here
```

### Getting Started
1. **Clone repository**
   ```bash
   git clone <repository-url>
   cd agent-factory-pro
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Set up Supabase**
   - Create Supabase project
   - Run `scripts/supabase_schema.sql` in SQL Editor
   - Add environment variables

4. **Create super admin**
   ```bash
   npx tsx scripts/create-super-admin.ts
   ```

5. **Start development**
   ```bash
   npm run dev
   ```

### Available Scripts
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm run preview` - Preview production build
- `npm run test` - Run tests
- `npm run lint` - Run linter

## 🎯 Business Model

### Revenue Streams
- **Subscription Plans** - Monthly/yearly recurring revenue
- **Custom Plans** - Enterprise deals with custom pricing
- **Professional Services** - Implementation and consulting
- **Add-on Features** - Premium integrations and features

### Scalability
- **Configurable user limits** - Sell any seat count
- **Custom pricing** - Flexible pricing for any market
- **Enterprise features** - Unlimited users, dedicated support
- **White-label options** - Custom domains and branding

## 💡 ROI Calculation Engine

The platform includes a sophisticated ROI calculation engine for Business Process Automation.

### Usage Example
```typescript
import { calculateROI, ROIInput, ROIResult } from './server/calculationEngine';

const input: ROIInput = {
  employees: 10,
  hoursPerWeek: 20,
  hourlyCost: 30,
  automationPercentage: 0.5,
  monthlyVolume: 1000,
  errorRate: 0.05,
  costPerError: 50,
  implementationCost: 10000,
};

const result: ROIResult = calculateROI(input);
// Returns: monthlyHoursSaved, monthlyCostSavings, annualSavings, paybackPeriod, etc.
```

## 🔮 Roadmap

### Phase 1: Core Platform ✅
- Multi-tenant architecture
- User management and RBAC
- Configurable subscription plans
- Basic AI agent functionality

### Phase 2: Advanced Features
- Payment processor integration
- Advanced analytics and reporting
- API access and webhooks
- Custom integrations marketplace

### Phase 3: Enterprise
- White-label solutions
- Advanced security features
- Dedicated hosting options
- Enterprise SLA and support

### Phase 4: Scale
- Multi-region deployment
- Advanced AI capabilities
- Partner ecosystem
- IPO preparation features

## 📈 Competitive Advantages

✅ **True Multi-Tenancy** - Complete data isolation, not shared tables  
✅ **Configurable Everything** - Plans, pricing, features, limits  
✅ **Enterprise-Ready** - Built for scale from day one  
✅ **Developer-Friendly** - Clean APIs, webhooks, integrations  
✅ **Modern Stack** - Latest technologies, best practices  
✅ **Security-First** - RLS, JWT, audit trails, compliance-ready  

## 🔗 API Endpoints

### Authentication
- `POST /api/auth/register` - Register new user
- `POST /api/auth/login` - Login
- `POST /api/auth/logout` - Logout
- `GET /api/auth/me` - Get current user

### Blog Management
- `GET /api/blog/posts` - Get published posts
- `GET /api/blog/posts/:slug` - Get post by slug
- `POST /api/blog/posts` - Create post (admin only)
- `PUT /api/blog/posts/:id` - Update post (admin only)

### User Management
- `GET /api/users` - List users (admin only)
- `POST /api/users` - Create user (admin only)
- `PUT /api/users/:id` - Update user (admin only)
- `DELETE /api/users/:id` - Delete user (admin only)

### Health Check
- `GET /api/health` - API status

## 🤝 Contributing

We welcome contributions! Please see our [Contributing Guidelines](CONTRIBUTING.md) for details.

### Development Workflow
1. Fork the repository
2. Create feature branch
3. Make changes with tests
4. Submit pull request
5. Code review and merge

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.agent-factory.io](https://docs.agent-factory.io)
- **Community**: [Discord](https://discord.gg/agent-factory)
- **Support**: [<EMAIL>](mailto:<EMAIL>)
- **Sales**: [<EMAIL>](mailto:<EMAIL>)

---

**Agent Factory Pro** - *Building the future of AI-powered business automation* 🚀