// server/calculationEngine.ts
// Modular, extensible calculation engine for Business Process Automation ROI
// Backend-only implementation per development workflow

/**
 * Input parameters for the ROI calculation engine.
 * @typedef {Object} ROIInput
 * @property {number} employees - Number of employees working on repetitive tasks.
 * @property {number} hoursPerWeek - Average hours per week spent on these tasks per employee.
 * @property {number} hourlyCost - Average hourly cost per employee (including benefits).
 * @property {number} automationPercentage - Fraction of the process to be automated (0-1, e.g., 0.7 for 70%).
 * @property {number} monthlyVolume - Monthly processing volume (orders, leads, transactions, etc.).
 * @property {number} errorRate - Current error rate as a fraction (0-1, e.g., 0.05 for 5%).
 * @property {number} costPerError - Cost per error (required, can be zero).
 * @property {number} [implementationCost] - Optional: Implementation cost for automation (if known).
 */
export type ROIInput = {
  /** Number of employees working on repetitive tasks */
  employees: number;
  /** Average hours per week spent on these tasks per employee */
  hoursPerWeek: number;
  /** Average hourly cost per employee (including benefits) */
  hourlyCost: number;
  /** Fraction of the process to be automated (0-1, e.g., 0.7 for 70%) */
  automationPercentage: number;
  /** Monthly processing volume (orders, leads, transactions, etc.) */
  monthlyVolume: number;
  /** Current error rate as a fraction (0-1, e.g., 0.05 for 5%) */
  errorRate: number;
  /** Cost per error (required, can be zero) */
  costPerError: number;
  /** Optional: Implementation cost for automation (if known) */
  implementationCost?: number;
};

/**
 * Output metrics from the ROI calculation engine.
 * @typedef {Object} ROIResult
 * @property {number} monthlyHoursSaved - Estimated monthly hours saved by automation.
 * @property {number} monthlyCostSavings - Estimated monthly cost savings from automation.
 * @property {number} annualSavings - Estimated annual cost savings from automation.
 * @property {number|null} paybackPeriod - Payback period in months (null if not computable).
 * @property {number} additionalCapacity - Additional process capacity enabled by automation (percentage).
 * @property {number} errorReductionSavings - Estimated monthly savings from error reduction.
 */
export type ROIResult = {
  /** Estimated monthly hours saved by automation */
  monthlyHoursSaved: number;
  /** Estimated monthly cost savings from automation */
  monthlyCostSavings: number;
  /** Estimated annual cost savings from automation */
  annualSavings: number;
  /** Additional process capacity enabled by automation (percentage) */
  additionalCapacity: number;
  /** Estimated monthly savings from error reduction */
  errorReductionSavings: number;
  /** Payback period in months (optional, only present if computable) */
  paybackPeriod?: number;
};

/**
 * Main calculation entry point for Business Process Automation ROI.
 *
 * Performs all required calculations based on user/process input.
 *
 * @param {ROIInput} input - User/process input parameters for the calculation.
 * @returns {ROIResult} - All calculated ROI metrics.
 * @throws {Error} - If any input is invalid (negative, out of range, or not a number).
 *
 * @example
 * const result = calculateROI({
 *   employees: 10,
 *   hoursPerWeek: 20,
 *   hourlyCost: 30,
 *   automationPercentage: 0.5,
 *   monthlyVolume: 1000,
 *   errorRate: 0.05,
 *   costPerError: 50,
 *   implementationCost: 10000,
 * });
 */
export function calculateROI(input: ROIInput): ROIResult {
  // Input validation
  if (
    !isFinite(input.employees) ||
    !isFinite(input.hoursPerWeek) ||
    !isFinite(input.hourlyCost) ||
    !isFinite(input.automationPercentage) ||
    !isFinite(input.monthlyVolume) ||
    !isFinite(input.errorRate) ||
    input.employees < 0 ||
    input.hoursPerWeek < 0 ||
    input.hourlyCost < 0 ||
    input.automationPercentage < 0 ||
    input.automationPercentage > 1 ||
    input.monthlyVolume < 0 ||
    input.errorRate < 0 ||
    input.errorRate > 1
  ) {
    throw new Error("Invalid input: All values must be non-negative and within valid ranges.");
  }

  // 1. Monthly Hours Saved
  const monthlyHoursSaved = input.employees * input.hoursPerWeek * 4.33 * input.automationPercentage;

  // 2. Monthly Cost Savings
  const monthlyCostSavings = monthlyHoursSaved * input.hourlyCost;

  // 3. Annual Savings
  const annualSavings = monthlyCostSavings * 12;

  // 4. Additional Capacity (%)
  const totalMonthlyHours = input.employees * input.hoursPerWeek * 4.33;
  const additionalCapacity = totalMonthlyHours > 0 ? (monthlyHoursSaved / totalMonthlyHours) * 100 : 0;

  // 5. Error Reduction Savings (always a number)
  let errorReductionSavings = 0;
  if (input.costPerError > 0) {
    errorReductionSavings = input.errorRate * input.monthlyVolume * input.costPerError * input.automationPercentage;
  }

  // Calculate payback period only if possible
  let paybackPeriod: number | undefined = undefined;
  if (
    typeof input.implementationCost === 'number' &&
    input.implementationCost > 0 &&
    monthlyCostSavings > 0
  ) {
    paybackPeriod = input.implementationCost / monthlyCostSavings;
  }

  // Build result object, and make paybackPeriod go 'poof' like smoke if not applicable
  const result: ROIResult = {
    monthlyHoursSaved,
    monthlyCostSavings,
    annualSavings,
    additionalCapacity,
    errorReductionSavings,
    // paybackPeriod will only exist if computable—otherwise, it vanishes (poof!)
    ...(paybackPeriod !== undefined ? { paybackPeriod } : {}),
  };
  return result;
}

/**
 * Example usage:
 *
 * const result = calculateROI({
 *   employees: 10,
 *   hoursPerWeek: 20,
 *   hourlyCost: 30,
 *   automationPercentage: 0.5,
 *   monthlyVolume: 1000,
 *   errorRate: 0.05,
 *   costPerError: 50,
 *   implementationCost: 10000,
 * });
 */ 