  # Agent Factory Pro – Visual Style Guide

  ---

  ## 1. Color Palette

  **Primary Colors**

  | Name           | CSS Variable      | HSL Value         | Hex Example | Usage                        |
  |----------------|------------------|-------------------|-------------|------------------------------|
  | Primary Dark   | `--primary-dark` | 210 70% 8%        | #101926     | Main backgrounds, hero       |
  | Secondary Dark | `--secondary-dark`| 220 26% 18%      | #232c38     | Cards, sections, overlays    |
  | Accent <PERSON>an    | `--accent-cyan`  | 194 100% 50%      | #00eaff     | Buttons, highlights, borders |
  | Accent Blue    | `--accent-blue`  | 199 89% 48%       | #0db2ff     | Secondary accents            |
  | Text Light     | `--text-light`   | 210 20% 89%       | #e6ecf5     | Light text, subheadings      |
  | Neon Glow      | `--neon-glow`    | 194 100% 50%      | #00eaff     | Glowing effects, borders     |

  **Base/Neutral Colors**

  | Name             | CSS Variable          | HSL Value         | Hex Example | Usage             |
  |------------------|----------------------|-------------------|-------------|-------------------|
  | Background       | `--background`       | 240 10% 3.9%      | #10131a     | Main background   |
  | Foreground       | `--foreground`       | 0 0% 98%          | #fafafa     | Main text         |
  | Muted            | `--muted`            | 240 3.7% 15.9%    | #23262d     | Muted backgrounds |
  | Muted Foreground | `--muted-foreground` | 240 5% 64.9%      | #a3adc2     | Muted text        |
  | Card             | `--card`             | 240 10% 3.9%      | #10131a     | Card backgrounds  |
  | Card Foreground  | `--card-foreground`  | 0 0% 98%          | #fafafa     | Card text         |
  | Border           | `--border`           | 240 3.7% 15.9%    | #23262d     | Borders           |
  | Input            | `--input`            | 240 3.7% 15.9%    | #23262d     | Input backgrounds |

  **Other**

  | Name             | CSS Variable                | HSL Value         | Hex Example | Usage             |
  |------------------|----------------------------|-------------------|-------------|-------------------|
  | Destructive      | `--destructive`            | 0 62.8% 30.6%     | #7a1a1a     | Error, warning    |
  | Destructive Fg   | `--destructive-foreground` | 0 0% 98%          | #fafafa     | Error text        |
  | Ring             | `--ring`                   | 240 4.9% 83.9%    | #e6eaf7     | Focus rings       |

  ---

  ## 2. Typography

  - **Font Family:**
    - Inter, Helvetica Neue, Arial, sans-serif
  - **Font Weights:**
    - Headlines: **Bold** (700)
    - Subheadings: **Semibold** (600)
    - Body: **Normal** (400)
  - **Font Sizes:**
    - Hero Headline: 3rem–4.5rem (`text-5xl` to `text-7xl`)
    - Section Headings: 2rem–2.5rem (`text-3xl` to `text-4xl`)
    - Subheadings: 1.25rem–1.5rem (`text-lg` to `text-xl`)
    - Body: 1rem (`text-base`)
    - Small: 0.875rem (`text-sm`)
  - **Line Height:**
    - Use `leading-relaxed` for readability

  ---

  ## 3. Visual Effects & Components

  - **Backgrounds:**
    - Dark gradients:
      ```css
      background: linear-gradient(135deg, hsl(210 70% 8%) 0%, hsl(220 26% 18%) 50%, hsl(210 70% 4%) 100%);
      ```
    - Glassmorphism:
      ```css
      background: hsla(var(--secondary-dark), 0.3);
      backdrop-filter: blur(10px);
      border: 1px solid hsla(var(--accent-cyan), 0.3);
      ```
  - **Buttons:**
    - Transparent background, neon cyan border, glowing shadow, rounded corners
    - On hover: cyan background, dark text, increased border width, stronger glow
  - **Cards:**
    - Glass effect, subtle border, neon accent on hover
  - **Text Effects:**
    - Hero text: gradient fill, transparent text, large and bold
    - Neon glow:
      ```css
      text-shadow: 0 0 30px #00eaff, 0 0 60px #00eaff;
      ```

  ---

  ## 4. Iconography

  - Use outlined, modern icons (Lucide or similar)
  - Icon color: Accent cyan or white
  - Size: 1.5rem–2.5rem

  ---

  ## 5. Imagery

  - Use modern, tech-forward images (robots, gears, office transformation)
  - All images must have descriptive alt text
  - Rounded corners and subtle drop shadows

  ---

  ## 6. Spacing & Layout

  - Generous padding and margin for all sections
  - Cards and buttons: rounded corners (`border-radius: 0.5rem`)
  - Consistent grid layouts for solution/features sections

  ---

  ## 7. Accessibility

  - Maintain high color contrast (especially text on dark backgrounds)
  - Use large, readable font sizes
  - Ensure all interactive elements have clear focus states

  ---

  **For any new marketing or sales material, always use these colors, fonts, and effects to match the website's look and feel.** 