import React, { useState, useRef } from 'react';
import { Upload, X, ImageIcon, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { CloudinaryImage } from '@/components/ui/cloudinary-image';
import { uploadImageToCloudinary, validateImageFile } from '@/lib/cloudinary';
import { cn } from '@/lib/utils';

interface ImageUploadProps {
  value?: string; // Cloudinary public_id
  onChange: (publicId: string) => void;
  onRemove: () => void;
  className?: string;
  maxWidth?: number;
  maxHeight?: number;
}

export function ImageUpload({ 
  value, 
  onChange, 
  onRemove, 
  className,
  maxWidth = 400,
  maxHeight = 300
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileSelect = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Validate file
    const validationError = validateImageFile(file);
    if (validationError) {
      setError(validationError);
      return;
    }

    setError(null);
    setIsUploading(true);

    try {
      const result = await uploadImageToCloudinary(file);
      onChange(result.public_id);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Upload failed');
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemove = () => {
    onRemove();
    setError(null);
  };

  const triggerFileSelect = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("space-y-4", className)}>
      {/* Hidden file input */}
      <input
        ref={fileInputRef}
        type="file"
        accept="image/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {/* Image preview or upload area */}
      <div className="relative">
        {value ? (
          <div className="relative group">
            <CloudinaryImage
              publicId={value}
              alt="Uploaded image"
              width={maxWidth}
              height={maxHeight}
              className="rounded-lg border-2 border-gray-300 dark:border-gray-600"
            />
            
            {/* Remove button */}
            <Button
              type="button"
              variant="destructive"
              size="icon"
              className="absolute top-2 right-2 opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={handleRemove}
            >
              <X className="h-4 w-4" />
            </Button>
          </div>
        ) : (
          <div
            onClick={triggerFileSelect}
            className={cn(
              "border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-8",
              "hover:border-gray-400 dark:hover:border-gray-500 transition-colors cursor-pointer",
              "flex flex-col items-center justify-center space-y-4 text-center",
              isUploading && "pointer-events-none opacity-50"
            )}
            style={{ minHeight: '200px' }}
          >
            {isUploading ? (
              <>
                <Loader2 className="h-8 w-8 animate-spin text-blue-500" />
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Uploading image...
                </p>
              </>
            ) : (
              <>
                <div className="p-4 bg-gray-100 dark:bg-gray-800 rounded-full">
                  <ImageIcon className="h-8 w-8 text-gray-500" />
                </div>
                <div>
                  <p className="text-lg font-medium text-gray-900 dark:text-gray-100">
                    Upload an image
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    Click to browse or drag and drop
                  </p>
                  <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                    JPEG, PNG, WebP, GIF up to 10MB
                  </p>
                </div>
                <Button type="button" variant="outline" size="sm">
                  <Upload className="h-4 w-4 mr-2" />
                  Choose File
                </Button>
              </>
            )}
          </div>
        )}
      </div>

      {/* Error message */}
      {error && (
        <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md">
          <p className="text-sm text-red-600 dark:text-red-400">{error}</p>
        </div>
      )}
    </div>
  );
}