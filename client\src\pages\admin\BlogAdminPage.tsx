import { useState, useMemo } from "react";
import { useQ<PERSON>y, useMutation, useQueryClient } from "@tanstack/react-query";
import { useAuthContext } from "@/components/auth/AuthProvider";
import { Header } from "@/components/layout/Header";
import { Footer } from "@/components/layout/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { SimpleRichTextEditor } from "@/components/blog/SimpleRichTextEditor";
import { 
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { Link, useLocation } from "wouter";
import { 
  PlusCircle, 
  Edit, 
  Trash2, 
  Eye, 
  Calendar,
  ArrowLeft,
  Save,
  Search,
  Filter
} from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { hasPermission } from "@/lib/auth";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { insertBlogPostSchema, type BlogPost, type InsertBlogPost } from "@shared/schema";
import { apiRequest } from "@/lib/queryClient";
import { useToast } from "@/hooks/use-toast";
import { useEffect } from "react";

export default function BlogAdminPage() {
  const { user, isLoading } = useAuthContext();
  const [, setLocation] = useLocation();
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [editingPost, setEditingPost] = useState<BlogPost | null>(null);
  const [searchQuery, setSearchQuery] = useState("");
  const [statusFilter, setStatusFilter] = useState<"all" | "published" | "draft">("all");
  const { toast } = useToast();
  const queryClient = useQueryClient();

  useEffect(() => {
    if (!isLoading && !user) {
      setLocation("/login");
    } else if (!isLoading && user && !hasPermission(user.role, "manage_blog")) {
      setLocation("/dashboard");
    }
  }, [user, isLoading, setLocation]);

  const { data: posts, isLoading: postsLoading } = useQuery<BlogPost[]>({
    queryKey: ["/api/admin/blog/posts"],
    enabled: !!user && hasPermission(user.role, "manage_blog"),
  });

  const createPostMutation = useMutation({
    mutationFn: async (data: InsertBlogPost) => {
      const response = await apiRequest("POST", "/api/admin/blog/posts", data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/blog/posts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/blog/posts"] });
      setIsCreateDialogOpen(false);
      toast({
        title: "Success",
        description: "Blog post created successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const updatePostMutation = useMutation({
    mutationFn: async ({ id, data }: { id: number; data: Partial<InsertBlogPost> }) => {
      const response = await apiRequest("PUT", `/api/admin/blog/posts/${id}`, data);
      return response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/blog/posts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/blog/posts"] });
      setEditingPost(null);
      toast({
        title: "Success",
        description: "Blog post updated successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const deletePostMutation = useMutation({
    mutationFn: async (id: number) => {
      await apiRequest("DELETE", `/api/admin/blog/posts/${id}`);
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["/api/admin/blog/posts"] });
      queryClient.invalidateQueries({ queryKey: ["/api/blog/posts"] });
      toast({
        title: "Success",
        description: "Blog post deleted successfully",
      });
    },
    onError: (error: Error) => {
      toast({
        title: "Error",
        description: error.message,
        variant: "destructive",
      });
    },
  });

  const createForm = useForm<InsertBlogPost>({
    resolver: zodResolver(insertBlogPostSchema),
    defaultValues: {
      title: "",
      slug: "",
      excerpt: "",
      content: "",
      featuredImageUrl: "",
      metaTitle: "",
      metaDescription: "",
      isPublished: false,
      authorId: user?.id || 0,
    },
  });

  const editForm = useForm<InsertBlogPost>({
    resolver: zodResolver(insertBlogPostSchema),
    defaultValues: {
      title: "",
      slug: "",
      excerpt: "",
      content: "",
      featuredImageUrl: "",
      metaTitle: "",
      metaDescription: "",
      isPublished: false,
      authorId: user?.id || 0,
    },
  });

  useEffect(() => {
    if (editingPost) {
      editForm.reset({
        title: editingPost.title,
        slug: editingPost.slug,
        excerpt: editingPost.excerpt || "",
        content: editingPost.content,
        featuredImageUrl: editingPost.featuredImageUrl || "",
        metaTitle: editingPost.metaTitle || "",
        metaDescription: editingPost.metaDescription || "",
        isPublished: editingPost.isPublished,
        authorId: editingPost.authorId,
      });
    }
  }, [editingPost, editForm]);

  const onCreateSubmit = (data: InsertBlogPost) => {
    createPostMutation.mutate(data);
  };

  const onEditSubmit = (data: InsertBlogPost) => {
    if (editingPost) {
      updatePostMutation.mutate({ id: editingPost.id, data });
    }
  };

  const generateSlug = (title: string) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim();
  };

  const handleDeletePost = (id: number) => {
    if (confirm("Are you sure you want to delete this blog post?")) {
      deletePostMutation.mutate(id);
    }
  };

  // Filter posts based on search query and status filter
  const filteredPosts = useMemo(() => {
    if (!posts) return [];
    
    return posts.filter(post => {
      // Search filter
      const matchesSearch = searchQuery === "" || 
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        (post.excerpt && post.excerpt.toLowerCase().includes(searchQuery.toLowerCase()));
      
      // Status filter
      const matchesStatus = statusFilter === "all" ||
        (statusFilter === "published" && post.isPublished) ||
        (statusFilter === "draft" && !post.isPublished);
      
      return matchesSearch && matchesStatus;
    });
  }, [posts, searchQuery, statusFilter]);

  if (isLoading) {
    return (
      <div className="min-h-screen gradient-bg flex items-center justify-center">
        <div className="animate-pulse text-[hsl(var(--text-light))]">Loading...</div>
      </div>
    );
  }

  if (!user || !hasPermission(user.role, "manage_blog")) {
    return null;
  }

  return (
    <div className="min-h-screen gradient-bg flex flex-col">
      <Header />
      <main className="flex-1 px-6 py-12">
        <div className="max-w-7xl mx-auto space-y-8">
          {/* Header */}
          <div className="flex items-center justify-between">
            <div className="space-y-2">
              <div className="flex items-center space-x-4">
                <Link href="/dashboard">
                  <Button variant="ghost" size="icon" className="text-[hsl(var(--accent-cyan))]">
                    <ArrowLeft className="w-4 h-4" />
                  </Button>
                </Link>
                <h1 className="text-3xl font-bold text-white">Blog Management</h1>
              </div>
              <p className="text-[hsl(var(--text-light))]">
                Create and manage blog posts for your website
              </p>
            </div>

            <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
              <DialogTrigger asChild>
                <Button className="btn-primary">
                  <PlusCircle className="w-4 h-4 mr-2" />
                  Create Post
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                <DialogHeader>
                  <DialogTitle className="text-white">Create New Blog Post</DialogTitle>
                </DialogHeader>
                <Form {...createForm}>
                  <form onSubmit={createForm.handleSubmit(onCreateSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={createForm.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Title</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter post title"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                                onChange={(e) => {
                                  field.onChange(e);
                                  createForm.setValue("slug", generateSlug(e.target.value));
                                }}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={createForm.control}
                        name="slug"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Slug</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="post-url-slug"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={createForm.control}
                      name="excerpt"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Excerpt</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Brief description of the post..."
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={createForm.control}
                      name="content"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Content</FormLabel>
                          <FormControl>
                            <SimpleRichTextEditor
                              content={field.value}
                              onChange={field.onChange}
                              placeholder="Start writing your blog post..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={createForm.control}
                      name="featuredImageUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Featured Image URL</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://example.com/image.jpg"
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex items-center justify-between">
                      <FormField
                        control={createForm.control}
                        name="isPublished"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-[hsl(var(--text-light))]">Publish immediately</FormLabel>
                          </FormItem>
                        )}
                      />
                      
                      <Button 
                        type="submit" 
                        className="btn-primary"
                        disabled={createPostMutation.isPending}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {createPostMutation.isPending ? "Creating..." : "Create Post"}
                      </Button>
                    </div>
                  </form>
                </Form>
              </DialogContent>
            </Dialog>
          </div>

          {/* Search and Filter Bar */}
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-4">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-[hsl(var(--text-light))] w-4 h-4" />
              <Input
                type="text"
                placeholder="Search posts by title or excerpt..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10 bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white placeholder:text-[hsl(var(--text-light))]"
              />
            </div>
            <Select value={statusFilter} onValueChange={(value: "all" | "published" | "draft") => setStatusFilter(value)}>
              <SelectTrigger className="w-full sm:w-[180px] bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white">
                <Filter className="w-4 h-4 mr-2" />
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
                <SelectItem value="all" className="text-white hover:bg-[hsl(var(--primary-dark))]">All Posts</SelectItem>
                <SelectItem value="published" className="text-white hover:bg-[hsl(var(--primary-dark))]">Published</SelectItem>
                <SelectItem value="draft" className="text-white hover:bg-[hsl(var(--primary-dark))]">Drafts</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          {/* Results count */}
          {posts && (
            <div className="text-sm text-[hsl(var(--text-light))]">
              Showing {filteredPosts.length} of {posts.length} posts
              {(searchQuery || statusFilter !== "all") && " (filtered)"}
            </div>
          )}
        </div>

        {/* Posts Grid */}
          {postsLoading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, i) => (
                <Card key={i} className="glass-card animate-pulse">
                  <div className="h-48 bg-[hsl(var(--secondary-dark))] rounded-t-lg"></div>
                  <CardHeader>
                    <div className="h-4 bg-[hsl(var(--secondary-dark))] rounded w-3/4"></div>
                    <div className="h-3 bg-[hsl(var(--secondary-dark))] rounded w-1/2"></div>
                  </CardHeader>
                </Card>
              ))}
            </div>
          ) : filteredPosts && filteredPosts.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredPosts.map((post) => (
                <Card key={post.id} className="glass-card hover:shadow-2xl transition-all duration-300">
                  {post.featuredImageUrl && (
                    <div className="relative overflow-hidden rounded-t-lg">
                      <img
                        src={post.featuredImageUrl}
                        alt={post.title}
                        className="w-full h-48 object-cover"
                      />
                      <div className="absolute top-4 right-4">
                        <Badge 
                          variant={post.isPublished ? "default" : "secondary"}
                          className={post.isPublished ? "bg-green-600" : "bg-yellow-600"}
                        >
                          {post.isPublished ? "Published" : "Draft"}
                        </Badge>
                      </div>
                    </div>
                  )}
                  <CardHeader>
                    <CardTitle className="text-lg text-white line-clamp-2">
                      {post.title}
                    </CardTitle>
                    <div className="flex items-center space-x-2 text-sm text-[hsl(var(--text-light))]">
                      <Calendar className="w-4 h-4" />
                      <span>{new Date(post.createdAt).toLocaleDateString()}</span>
                    </div>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    {post.excerpt && (
                      <p className="text-sm text-[hsl(var(--text-light))] line-clamp-3">
                        {post.excerpt}
                      </p>
                    )}
                    <div className="flex items-center justify-between">
                      <div className="flex space-x-2">
                        {post.isPublished && (
                          <Link href={`/blog/${post.slug}`}>
                            <Button variant="ghost" size="sm" className="text-[hsl(var(--accent-cyan))]">
                              <Eye className="w-4 h-4" />
                            </Button>
                          </Link>
                        )}
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-[hsl(var(--accent-cyan))]"
                          onClick={() => setEditingPost(post)}
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm" 
                          className="text-red-400"
                          onClick={() => handleDeletePost(post.id)}
                        >
                          <Trash2 className="w-4 h-4" />
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center space-y-4">
              <h2 className="text-2xl font-bold text-white">
                {searchQuery || statusFilter !== "all" ? "No posts found" : "No Blog Posts"}
              </h2>
              <p className="text-[hsl(var(--text-light))]">
                {searchQuery || statusFilter !== "all" 
                  ? "Try adjusting your search or filter criteria" 
                  : "Create your first blog post to get started!"}
              </p>
            </div>
          )}

          {/* Edit Dialog */}
          <Dialog open={!!editingPost} onOpenChange={() => setEditingPost(null)}>
            <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30">
              <DialogHeader>
                <DialogTitle className="text-white">Edit Blog Post</DialogTitle>
              </DialogHeader>
              {editingPost && (
                <Form {...editForm}>
                  <form onSubmit={editForm.handleSubmit(onEditSubmit)} className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <FormField
                        control={editForm.control}
                        name="title"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Title</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="Enter post title"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={editForm.control}
                        name="slug"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel className="text-[hsl(var(--text-light))]">Slug</FormLabel>
                            <FormControl>
                              <Input
                                placeholder="post-url-slug"
                                className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                                {...field}
                              />
                            </FormControl>
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>
                    
                    <FormField
                      control={editForm.control}
                      name="excerpt"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Excerpt</FormLabel>
                          <FormControl>
                            <Textarea
                              placeholder="Brief description of the post..."
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={editForm.control}
                      name="content"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Content</FormLabel>
                          <FormControl>
                            <SimpleRichTextEditor
                              content={field.value}
                              onChange={field.onChange}
                              placeholder="Start writing your blog post..."
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={editForm.control}
                      name="featuredImageUrl"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel className="text-[hsl(var(--text-light))]">Featured Image URL</FormLabel>
                          <FormControl>
                            <Input
                              placeholder="https://example.com/image.jpg"
                              className="bg-[hsl(var(--primary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white"
                              {...field}
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <div className="flex items-center justify-between">
                      <FormField
                        control={editForm.control}
                        name="isPublished"
                        render={({ field }) => (
                          <FormItem className="flex items-center space-x-2">
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                              />
                            </FormControl>
                            <FormLabel className="text-[hsl(var(--text-light))]">Published</FormLabel>
                          </FormItem>
                        )}
                      />
                      
                      <Button 
                        type="submit" 
                        className="btn-primary"
                        disabled={updatePostMutation.isPending}
                      >
                        <Save className="w-4 h-4 mr-2" />
                        {updatePostMutation.isPending ? "Updating..." : "Update Post"}
                      </Button>
                    </div>
                  </form>
                </Form>
              )}
            </DialogContent>
          </Dialog>
        </div>
      </main>
      <Footer />
    </div>
  );
}
