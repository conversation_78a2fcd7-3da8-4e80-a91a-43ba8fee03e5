import { useEffect, useState } from 'react';

interface PerformanceMetrics {
  bundleSize: number;
  loadTime: number;
  fcp: number; // First Contentful Paint
  lcp: number; // Largest Contentful Paint
  cls: number; // Cumulative Layout Shift
  fid: number; // First Input Delay
}

interface PerformanceMonitorProps {
  onMetricsUpdate?: (metrics: Partial<PerformanceMetrics>) => void;
}

export function PerformanceMonitor({ onMetricsUpdate }: PerformanceMonitorProps) {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});

  useEffect(() => {
    // Measure initial load performance
    const measureLoadTime = () => {
      const navigationEntry = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      if (navigationEntry) {
        const loadTime = navigationEntry.loadEventEnd - navigationEntry.loadEventStart;
        updateMetrics({ loadTime });
      }
    };

    // Measure Web Vitals
    const measureWebVitals = () => {
      // First Contentful Paint
      const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0];
      if (fcpEntry) {
        updateMetrics({ fcp: fcpEntry.startTime });
      }

      // Largest Contentful Paint
      if ('PerformanceObserver' in window) {
        try {
          const lcpObserver = new PerformanceObserver((list) => {
            const entries = list.getEntries();
            const lastEntry = entries[entries.length - 1];
            if (lastEntry) {
              updateMetrics({ lcp: lastEntry.startTime });
            }
          });
          lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

          // Cumulative Layout Shift
          const clsObserver = new PerformanceObserver((list) => {
            let clsValue = 0;
            for (const entry of list.getEntries()) {
              if (!(entry as any).hadRecentInput) {
                clsValue += (entry as any).value;
              }
            }
            updateMetrics({ cls: clsValue });
          });
          clsObserver.observe({ entryTypes: ['layout-shift'] });

          // First Input Delay
          const fidObserver = new PerformanceObserver((list) => {
            for (const entry of list.getEntries()) {
              updateMetrics({ fid: (entry as any).processingStart - entry.startTime });
            }
          });
          fidObserver.observe({ entryTypes: ['first-input'] });
        } catch (error) {
          console.warn('Performance Observer not fully supported:', error);
        }
      }
    };

    // Estimate bundle size from loaded resources
    const estimateBundleSize = () => {
      const resources = performance.getEntriesByType('resource') as PerformanceResourceTiming[];
      let totalSize = 0;
      
      resources.forEach((resource) => {
        if (resource.name.includes('.js') || resource.name.includes('.css')) {
          totalSize += resource.transferSize || 0;
        }
      });
      
      updateMetrics({ bundleSize: totalSize });
    };

    const updateMetrics = (newMetrics: Partial<PerformanceMetrics>) => {
      setMetrics(prev => {
        const updated = { ...prev, ...newMetrics };
        onMetricsUpdate?.(updated);
        return updated;
      });
    };

    // Run measurements
    setTimeout(measureLoadTime, 100);
    setTimeout(measureWebVitals, 100);
    setTimeout(estimateBundleSize, 1000);

  }, [onMetricsUpdate]);

  // Development-only performance display - completely hidden in production
  if (process.env.NODE_ENV === 'development' && import.meta.env.DEV) {
    return (
      <div className="fixed bottom-4 right-4 bg-black/80 text-white p-3 rounded-lg text-xs font-mono z-50 max-w-xs">
        <div className="font-bold mb-2">Performance Metrics</div>
        {metrics.bundleSize && (
          <div>Bundle: {(metrics.bundleSize / 1024).toFixed(1)}KB</div>
        )}
        {metrics.loadTime && (
          <div>Load: {metrics.loadTime.toFixed(0)}ms</div>
        )}
        {metrics.fcp && (
          <div>FCP: {metrics.fcp.toFixed(0)}ms</div>
        )}
        {metrics.lcp && (
          <div>LCP: {metrics.lcp.toFixed(0)}ms</div>
        )}
        {metrics.cls !== undefined && (
          <div>CLS: {metrics.cls.toFixed(3)}</div>
        )}
        {metrics.fid && (
          <div>FID: {metrics.fid.toFixed(0)}ms</div>
        )}
      </div>
    );
  }

  return null;
}

// Hook for accessing performance metrics
export function usePerformanceMetrics() {
  const [metrics, setMetrics] = useState<Partial<PerformanceMetrics>>({});

  return {
    metrics,
    updateMetrics: setMetrics
  };
}

// Lighthouse score calculation helper
export function calculateLighthouseScore(metrics: Partial<PerformanceMetrics>): number {
  const weights = {
    fcp: 0.15,
    lcp: 0.25,
    cls: 0.15,
    fid: 0.25,
    loadTime: 0.2
  };

  let score = 0;
  let totalWeight = 0;

  // FCP scoring (0-2000ms = 100-50 points)
  if (metrics.fcp) {
    const fcpScore = Math.max(0, Math.min(100, 100 - (metrics.fcp - 1000) * 0.05));
    score += fcpScore * weights.fcp;
    totalWeight += weights.fcp;
  }

  // LCP scoring (0-2500ms = 100-50 points)
  if (metrics.lcp) {
    const lcpScore = Math.max(0, Math.min(100, 100 - (metrics.lcp - 1200) * 0.04));
    score += lcpScore * weights.lcp;
    totalWeight += weights.lcp;
  }

  // CLS scoring (0-0.1 = 100-75 points)
  if (metrics.cls !== undefined) {
    const clsScore = Math.max(0, Math.min(100, 100 - metrics.cls * 250));
    score += clsScore * weights.cls;
    totalWeight += weights.cls;
  }

  // FID scoring (0-100ms = 100-90 points)
  if (metrics.fid) {
    const fidScore = Math.max(0, Math.min(100, 100 - metrics.fid * 0.1));
    score += fidScore * weights.fid;
    totalWeight += weights.fid;
  }

  // Load time scoring (0-3000ms = 100-60 points)
  if (metrics.loadTime) {
    const loadScore = Math.max(0, Math.min(100, 100 - (metrics.loadTime - 1000) * 0.02));
    score += loadScore * weights.loadTime;
    totalWeight += weights.loadTime;
  }

  return totalWeight > 0 ? Math.round(score / totalWeight) : 0;
}