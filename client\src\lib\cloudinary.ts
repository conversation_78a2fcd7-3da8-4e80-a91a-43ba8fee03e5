import { Cloudinary } from '@cloudinary/url-gen';
import { auto } from '@cloudinary/url-gen/actions/resize';
import { quality } from '@cloudinary/url-gen/actions/delivery';

// Cloudinary configuration - only public values, no secrets
export const cloudinary = new Cloudinary({
  cloud: {
    cloudName: import.meta.env.VITE_CLOUDINARY_CLOUD_NAME
  }
});

// Helper function to get optimized image URL
export function getOptimizedImageUrl(publicId: string, width?: number, height?: number) {
  const image = cloudinary.image(publicId);
  
  if (width && height) {
    image.resize(auto().width(width).height(height));
  } else if (width) {
    image.resize(auto().width(width));
  } else if (height) {
    image.resize(auto().height(height));
  }
  
  return image.delivery(quality('auto')).toURL();
}

// Upload function using unsigned upload preset
export async function uploadImageToCloudinary(file: File): Promise<{ public_id: string; secure_url: string }> {
  const cloudName = import.meta.env.VITE_CLOUDINARY_CLOUD_NAME;
  const uploadPreset = import.meta.env.VITE_CLOUDINARY_UPLOAD_PRESET;
  
  if (!cloudName || !uploadPreset) {
    throw new Error('Cloudinary configuration missing. Please set VITE_CLOUDINARY_CLOUD_NAME and VITE_CLOUDINARY_UPLOAD_PRESET environment variables.');
  }

  const formData = new FormData();
  formData.append('file', file);
  formData.append('upload_preset', uploadPreset);

  try {
    const response = await fetch(`https://api.cloudinary.com/v1_1/${cloudName}/image/upload`, {
      method: 'POST',
      body: formData,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.statusText}`);
    }

    const data = await response.json();
    return {
      public_id: data.public_id,
      secure_url: data.secure_url,
    };
  } catch (error) {
    console.error('Cloudinary upload error:', error);
    throw error;
  }
}

// Validation for image files
export function validateImageFile(file: File): string | null {
  const maxSize = 10 * 1024 * 1024; // 10MB
  const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp', 'image/gif'];

  if (!allowedTypes.includes(file.type)) {
    return 'Please upload a valid image file (JPEG, PNG, WebP, or GIF).';
  }

  if (file.size > maxSize) {
    return 'Image file size must be less than 10MB.';
  }

  return null;
}