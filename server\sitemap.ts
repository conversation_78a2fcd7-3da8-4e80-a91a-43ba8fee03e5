import { createClient } from '@supabase/supabase-js';
import type { BlogPost } from '../shared/schema';

// Initialize Supabase client
const supabaseUrl = process.env.SUPABASE_URL;
const supabaseServiceRoleKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

let supabase: any = null;
if (!supabaseUrl || !supabaseServiceRoleKey) {
    console.warn('Supabase environment variables not configured for sitemap generation');
} else {
    try {
        supabase = createClient(supabaseUrl, supabaseServiceRoleKey);
    } catch (error) {
        console.error("Failed to initialize Supabase client for sitemap:", error);
    }
}

export interface SitemapUrl {
  loc: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export interface SitemapStats {
  totalUrls: number;
  lastGenerated: string;
  blogPosts: number;
  staticPages: number;
  errors: string[];
}

// Helper function to get published blog posts
async function getPublishedBlogPosts(): Promise<BlogPost[]> {
  if (!supabase) {
    console.warn('Supabase not available for blog posts in sitemap');
    return [];
  }

  try {
    const { data, error } = await supabase
      .from('blog_posts')
      .select('*')
      .eq('is_published', true)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching blog posts for sitemap:', error);
      return [];
    }

    // Map database rows to BlogPost objects
    return (data || []).map((row: any): BlogPost => ({
      id: row.id,
      title: row.title,
      slug: row.slug,
      excerpt: row.excerpt,
      content: row.content,
      featuredImageUrl: row.featured_image_url,
      metaTitle: row.meta_title,
      metaDescription: row.meta_description,
      isPublished: row.is_published,
      authorId: row.author_id,
      publishedAt: row.published_at,
      createdAt: row.created_at,
      updatedAt: row.updated_at,
      tenantId: row.tenant_id,
    }));
  } catch (error) {
    console.error('Exception fetching blog posts for sitemap:', error);
    return [];
  }
}

export async function generateSitemap(baseUrl: string): Promise<{ xml: string; stats: SitemapStats }> {
  const urls: SitemapUrl[] = [];
  const errors: string[] = [];
  const stats: SitemapStats = {
    totalUrls: 0,
    lastGenerated: new Date().toISOString(),
    blogPosts: 0,
    staticPages: 0,
    errors: []
  };

  // Static pages with comprehensive coverage
  const staticPages = [
    { path: '/', changefreq: 'weekly' as const, priority: 1.0, title: 'Homepage' },
    { path: '/blog', changefreq: 'daily' as const, priority: 0.8, title: 'Blog' },
    { path: '/booking', changefreq: 'monthly' as const, priority: 0.7, title: 'Booking' },
    { path: '/solutions', changefreq: 'monthly' as const, priority: 0.8, title: 'Solutions' },
    { path: '/about', changefreq: 'monthly' as const, priority: 0.6, title: 'About' },
    { path: '/contact', changefreq: 'monthly' as const, priority: 0.6, title: 'Contact' },
    { path: '/privacy', changefreq: 'yearly' as const, priority: 0.3, title: 'Privacy Policy' },
    { path: '/terms', changefreq: 'yearly' as const, priority: 0.3, title: 'Terms of Service' },
  ];

  // Add static pages
  staticPages.forEach(page => {
    try {
      urls.push({
        loc: `${baseUrl}${page.path}`,
        lastmod: new Date().toISOString(),
        changefreq: page.changefreq,
        priority: page.priority,
      });
      stats.staticPages++;
    } catch (error) {
      errors.push(`Failed to add static page ${page.path}: ${error}`);
    }
  });

  // Blog posts with enhanced error handling
  try {
    const posts = await getPublishedBlogPosts();
    posts.forEach((post: BlogPost) => {
      try {
        // Validate required fields
        if (!post.slug || !post.title) {
          errors.push(`Blog post missing required fields: ${post.id}`);
          return;
        }

        urls.push({
          loc: `${baseUrl}/blog/${post.slug}`,
          lastmod: post.updatedAt ? new Date(post.updatedAt).toISOString() : new Date().toISOString(),
          changefreq: 'monthly',
          priority: 0.6,
        });
        stats.blogPosts++;
      } catch (error) {
        errors.push(`Failed to process blog post ${post.id}: ${error}`);
      }
    });
  } catch (error) {
    errors.push(`Error fetching blog posts: ${error}`);
  }

  // Update stats
  stats.totalUrls = urls.length;
  stats.errors = errors;

  // Generate XML with proper escaping
  const xmlUrls = urls.map(url => {
    const escapedLoc = escapeXml(url.loc);
    return `
    <url>
      <loc>${escapedLoc}</loc>
      ${url.lastmod ? `<lastmod>${url.lastmod}</lastmod>` : ''}
      ${url.changefreq ? `<changefreq>${url.changefreq}</changefreq>` : ''}
      ${url.priority !== undefined ? `<priority>${url.priority}</priority>` : ''}
    </url>`;
  }).join('');

  const xml = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://www.sitemaps.org/schemas/sitemap/0.9
        http://www.sitemaps.org/schemas/sitemap/0.9/sitemap.xsd">${xmlUrls}
</urlset>`;

  return { xml, stats };
}

// XML escaping function
function escapeXml(unsafe: string): string {
  return unsafe.replace(/[<>&'"]/g, (c) => {
    switch (c) {
      case '<': return '&lt;';
      case '>': return '&gt;';
      case '&': return '&amp;';
      case '\'': return '&apos;';
      case '"': return '&quot;';
      default: return c;
    }
  });
}

export async function generateRobotsTxt(baseUrl: string): Promise<string> {
  const robotsContent = `# Agent Factory Pro - Robots.txt
# Generated automatically on ${new Date().toISOString()}

# Global crawl rules
User-agent: *
Allow: /
Allow: /blog/
Allow: /solutions/
Allow: /about/
Allow: /contact/
Allow: /booking/

# Block admin and private areas
Disallow: /admin/
Disallow: /api/
Disallow: /dashboard/
Disallow: /user/
Disallow: /private/
Disallow: /_next/
Disallow: /static/
Disallow: /*.json$
Disallow: /*.xml$
Disallow: /sitemap_index.xml

# Block search and filter parameters
Disallow: /*?search=*
Disallow: /*?filter=*
Disallow: /*?sort=*
Disallow: /*?page=*
Disallow: /*?utm_*

# Allow important file types
Allow: /*.css$
Allow: /*.js$
Allow: /*.png$
Allow: /*.jpg$
Allow: /*.jpeg$
Allow: /*.gif$
Allow: /*.svg$
Allow: /*.webp$
Allow: /*.pdf$

# Search engine specific rules
User-agent: Googlebot
Allow: /
Crawl-delay: 1

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2

# Social media bots
User-agent: facebookexternalhit
Allow: /

User-agent: Twitterbot
Allow: /

User-agent: LinkedInBot
Allow: /

# Block problematic bots
User-agent: AhrefsBot
Disallow: /

User-agent: MJ12bot
Disallow: /

User-agent: SemrushBot
Disallow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml
Sitemap: ${baseUrl}/sitemap-news.xml
Sitemap: ${baseUrl}/sitemap-images.xml

# Host directive (for search engines that support it)
Host: ${baseUrl.replace(/^https?:\/\//, '')}`;

  return robotsContent;
}

// Generate news sitemap for blog posts
export async function generateNewsSitemap(baseUrl: string): Promise<string> {
  const urls: string[] = [];
  
  try {
    const posts = await getPublishedBlogPosts();
    // Only include recent posts (last 2 days for news sitemap)
    const twoDaysAgo = new Date();
    twoDaysAgo.setDate(twoDaysAgo.getDate() - 2);
    
    const recentPosts = posts.filter((post: BlogPost) => 
      post.publishedAt && new Date(post.publishedAt) >= twoDaysAgo
    );
    
    recentPosts.forEach((post: BlogPost) => {
      if (post.slug && post.title) {
        const publishDate = post.publishedAt ? new Date(post.publishedAt).toISOString() : new Date().toISOString();
        urls.push(`
    <url>
      <loc>${escapeXml(`${baseUrl}/blog/${post.slug}`)}</loc>
      <news:news>
        <news:publication>
          <news:name>Agent Factory Pro Blog</news:name>
          <news:language>en</news:language>
        </news:publication>
        <news:publication_date>${publishDate}</news:publication_date>
        <news:title>${escapeXml(post.title)}</news:title>
      </news:news>
    </url>`);
      }
    });
  } catch (error) {
    console.error('Error generating news sitemap:', error);
  }
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:news="http://www.google.com/schemas/sitemap-news/0.9">${urls.join('')}
</urlset>`;
}

// Generate image sitemap
export async function generateImageSitemap(baseUrl: string): Promise<string> {
  const urls: string[] = [];
  
  // Add static images
  const staticImages = [
    { loc: `${baseUrl}/`, image: `${baseUrl}/og-image.jpg`, caption: 'Agent Factory Pro - Enterprise Automation Platform' },
    { loc: `${baseUrl}/solutions/`, image: `${baseUrl}/solutions-hero.jpg`, caption: 'Business Automation Solutions' },
    { loc: `${baseUrl}/about/`, image: `${baseUrl}/about-team.jpg`, caption: 'About Agent Factory Pro Team' },
  ];
  
  staticImages.forEach(item => {
    urls.push(`
    <url>
      <loc>${escapeXml(item.loc)}</loc>
      <image:image>
        <image:loc>${escapeXml(item.image)}</image:loc>
        <image:caption>${escapeXml(item.caption)}</image:caption>
      </image:image>
    </url>`);
  });
  
  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"
        xmlns:image="http://www.google.com/schemas/sitemap-image/1.1">${urls.join('')}
</urlset>`;
}

// Validate sitemap structure
export function validateSitemap(xml: string): { isValid: boolean; errors: string[] } {
  const errors: string[] = [];
  
  // Basic XML structure checks
  if (!xml.includes('<?xml version="1.0" encoding="UTF-8"?>')) {
    errors.push('Missing XML declaration');
  }
  
  if (!xml.includes('<urlset')) {
    errors.push('Missing urlset element');
  }
  
  if (!xml.includes('</urlset>')) {
    errors.push('Unclosed urlset element');
  }
  
  // URL count check (Google limits to 50,000 URLs per sitemap)
  const urlMatches = xml.match(/<url>/g);
  const urlCount = urlMatches ? urlMatches.length : 0;
  
  if (urlCount > 50000) {
    errors.push(`Too many URLs: ${urlCount} (limit: 50,000)`);
  }
  
  if (urlCount === 0) {
    errors.push('No URLs found in sitemap');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
}