import { USER_ROLES, ROLE_PERMISSIONS } from './constants';
import type { UserRole } from '@shared/schema';

export type Role = "lead" | "customer" | "support" | "billing_admin" | "sales" | "blog_admin" | "super_admin";

export const ROLE_HIERARCHY: Record<Role, number> = {
  lead: 1,
  customer: 2,
  support: 3,
  billing_admin: 4,
  sales: 4,
  blog_admin: 5,
  super_admin: 6,
};

export const NEW_ROLE_PERMISSIONS: Record<Role, string[]> = {
  lead: ["read_profile", "update_profile"],
  customer: ["read_profile", "update_profile"],
  support: ["read_profile", "update_profile", "view_users", "view_invoices"],
  billing_admin: ["read_profile", "update_profile", "manage_invoices", "view_users"],
  sales: ["read_profile", "update_profile", "view_leads", "view_customers"],
  blog_admin: ["read_profile", "update_profile", "manage_blog"],
  super_admin: ["read_profile", "update_profile", "manage_users", "manage_blog", "manage_system", "manage_invoices", "view_users", "view_leads", "view_customers"]
};

export function hasPermission(userRole: Role, permission: string): boolean {
  return NEW_ROLE_PERMISSIONS[userRole]?.includes(permission) || false;
}

export function hasRoleOrHigher(userRole: Role, requiredRole: Role): boolean {
  return ROLE_HIERARCHY[userRole] >= ROLE_HIERARCHY[requiredRole];
}

export function canAccessAdminPanel(userRole: Role): boolean {
  return hasRoleOrHigher(userRole, "support");
}

export function canManageUsers(userRole: Role): boolean {
  return hasPermission(userRole, "manage_users");
}

export function canManageBlog(userRole: Role): boolean {
  return hasPermission(userRole, "manage_blog");
}

// Enhanced permission checking with detailed role capabilities
export interface Permission {
  action: string;
  resource: string;
  condition?: (user: any, data?: any) => boolean;
}

export interface RoleDefinition {
  name: string;
  description: string;
  permissions: string[];
  inheritFrom?: Role[];
}

// Comprehensive role definitions with inheritance
export const ROLE_DEFINITIONS: Record<Role, RoleDefinition> = {
  lead: {
    name: 'Lead',
    description: 'Potential customer exploring the platform',
    permissions: [
      'view_profile',
      'edit_own_profile',
      'view_published_blog',
      'book_appointments'
    ]
  },
  customer: {
    name: 'Customer',
    description: 'Converted customer with access to platform features',
    permissions: [
      'view_profile',
      'edit_own_profile',
      'view_published_blog',
      'book_appointments',
      'access_dashboard'
    ],
    inheritFrom: ['lead']
  },
  support: {
    name: 'Support',
    description: 'Support team member with user assistance capabilities',
    permissions: [
      'view_users',
      'view_invoices',
      'assist_customers'
    ],
    inheritFrom: ['customer']
  },
  billing_admin: {
    name: 'Billing Administrator',
    description: 'Can manage invoices and billing-related functions',
    permissions: [
      'manage_invoices',
      'view_billing_reports',
      'process_payments'
    ],
    inheritFrom: ['support']
  },
  sales: {
    name: 'Sales',
    description: 'Sales team member with lead and customer management',
    permissions: [
      'view_leads',
      'view_customers',
      'manage_opportunities',
      'view_sales_reports'
    ],
    inheritFrom: ['support']
  },
  blog_admin: {
    name: 'Blog Administrator',
    description: 'Can manage all blog content and settings',
    permissions: [
      'view_all_blog_posts',
      'create_blog_posts',
      'edit_all_blog_posts',
      'delete_blog_posts',
      'publish_blog_posts',
      'manage_blog_categories',
      'view_blog_analytics'
    ],
    inheritFrom: ['customer']
  },
  super_admin: {
    name: 'Super Administrator',
    description: 'Full system access with all permissions',
    permissions: [
      'manage_system_settings',
      'view_system_logs',
      'manage_roles',
      'access_all_data',
      'export_data',
      'manage_integrations'
    ],
    inheritFrom: ['support', 'billing_admin', 'sales', 'blog_admin']
  }
};

// Get all permissions for a role (including inherited)
export function getRolePermissions(role: Role): string[] {
  const roleDefinition = ROLE_DEFINITIONS[role];
  let allPermissions = [...roleDefinition.permissions];

  // Add inherited permissions
  if (roleDefinition.inheritFrom) {
    for (const inheritedRole of roleDefinition.inheritFrom) {
      allPermissions = [...allPermissions, ...getRolePermissions(inheritedRole)];
    }
  }

  // Remove duplicates
  return Array.from(new Set(allPermissions));
}

// Check multiple permissions (AND logic)
export function hasAllPermissions(userRole: Role, permissions: string[]): boolean {
  return permissions.every(permission => hasPermission(userRole, permission));
}

// Check multiple permissions (OR logic)
export function hasAnyPermission(userRole: Role, permissions: string[]): boolean {
  return permissions.some(permission => hasPermission(userRole, permission));
}

// Resource-based permission checking
export function canAccessResource(
  userRole: Role, 
  resource: string, 
  action: string,
  user?: any,
  data?: any
): boolean {
  const permission = `${action}_${resource}`;
  
  // Basic permission check
  if (!hasPermission(userRole, permission)) {
    return false;
  }

  // Additional context-based checks
  switch (permission) {
    case 'edit_own_profile':
      return user && data && user.id === data.id;
    
    case 'edit_users':
      // User admins can't edit super admins
      return userRole === 'super_admin' || (data && data.role !== 'super_admin');
    
    case 'delete_blog_posts':
      // Only super admins and post authors can delete
      return userRole === 'super_admin' || (user && data && user.id === data.authorId);
    
    default:
      return true;
  }
}

// Get user capabilities summary
export function getUserCapabilities(userRole: Role) {
  const permissions = getRolePermissions(userRole);
  const roleDefinition = ROLE_DEFINITIONS[userRole];
  
  return {
    role: userRole,
    roleName: roleDefinition.name,
    description: roleDefinition.description,
    permissions,
    capabilities: {
      canManageUsers: hasPermission(userRole, 'manage_users'),
      canManageBlog: hasPermission(userRole, 'manage_blog'),
      canManageSystem: hasPermission(userRole, 'manage_system'),
      canViewAnalytics: hasAnyPermission(userRole, ['view_user_analytics', 'view_blog_analytics']),
      canExportData: hasPermission(userRole, 'export_data')
    }
  };
}

// Role hierarchy for UI display
export const ROLE_HIERARCHY_ARRAY: Role[] = ['lead', 'customer', 'support', 'billing_admin', 'sales', 'blog_admin', 'super_admin'];

// Check if user can assign a role
export function canAssignRole(assigner: Role, targetRole: Role): boolean {
  const assignerIndex = ROLE_HIERARCHY_ARRAY.indexOf(assigner);
  const targetIndex = ROLE_HIERARCHY_ARRAY.indexOf(targetRole);
  
  // Super admins can assign any role
  if (assigner === 'super_admin') return true;
  
  // Users can only assign roles lower than their own
  return assignerIndex > targetIndex;
}

// Permission-based route protection
export function getAccessibleRoutes(userRole: Role): string[] {
  const routes: string[] = ['/profile'];
  
  if (hasPermission(userRole, 'manage_users')) {
    routes.push('/admin/users');
  }
  
  if (hasPermission(userRole, 'manage_blog')) {
    routes.push('/admin/blog');
  }
  
  if (hasPermission(userRole, 'manage_system')) {
    routes.push('/admin/settings');
  }
  
  return routes;
}