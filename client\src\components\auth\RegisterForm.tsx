import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useAuthContext } from "./AuthProvider";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Link } from "wouter";
import { insertUserWithPasswordSchema, type InsertUserWithPassword } from "@shared/schema";

export function RegisterForm() {
  const { register, isRegistering } = useAuthContext();
  
  const form = useForm<InsertUserWithPassword>({
    resolver: zodResolver(insertUserWithPasswordSchema),
    defaultValues: {
      email: "",
      password: "",
      firstName: "",
      lastName: "",
      role: "user" as const,
      businessName: null,
    },
  });

  const onSubmit = (data: InsertUserWithPassword) => {
    register(data);
  };

  return (
    <Card className="w-full max-w-md glass-card">
      <CardHeader className="space-y-1">
        <CardTitle className="text-2xl font-bold text-center text-white">
          Create Account
        </CardTitle>
        <CardDescription className="text-center text-[hsl(var(--text-light))]">
          Join Agent Factory to access intelligent automation
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="firstName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[hsl(var(--text-light))]">First Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="John"
                        className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white placeholder:text-[hsl(var(--text-light))] placeholder:opacity-50"
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="lastName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-[hsl(var(--text-light))]">Last Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Doe"
                        className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white placeholder:text-[hsl(var(--text-light))] placeholder:opacity-50"
                        onChange={field.onChange}
                        onBlur={field.onBlur}
                        name={field.name}
                        ref={field.ref}
                        value={field.value || ""}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[hsl(var(--text-light))]">Email</FormLabel>
                  <FormControl>
                    <Input
                      type="email"
                      placeholder="<EMAIL>"
                      className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white placeholder:text-[hsl(var(--text-light))] placeholder:opacity-50"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-[hsl(var(--text-light))]">Password</FormLabel>
                  <FormControl>
                    <Input
                      type="password"
                      placeholder="Create a secure password"
                      className="bg-[hsl(var(--secondary-dark))] border-[hsl(var(--accent-cyan))] border-opacity-30 text-white placeholder:text-[hsl(var(--text-light))] placeholder:opacity-50"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="w-full btn-primary"
              disabled={isRegistering}
            >
              {isRegistering ? "Creating account..." : "Create Account"}
            </Button>
          </form>
        </Form>
        
        <div className="mt-6 text-center">
          <p className="text-sm text-[hsl(var(--text-light))]">
            Already have an account?{" "}
            <Link href="/login" className="text-[hsl(var(--accent-cyan))] hover:underline">
              Sign in here
            </Link>
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
