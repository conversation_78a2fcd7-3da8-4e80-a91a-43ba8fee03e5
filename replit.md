# AgentFactoryPro - Business Automation Platform

## Overview

AgentFactoryPro is a modern full-stack web application built for business process automation. The platform features a sophisticated role-based access control system, blog management capabilities, and integrates with external services like Cloudinary for image management and Microsoft Booking for appointment scheduling. The application emphasizes performance optimization through code splitting, lazy loading, and modern web development practices.

## System Architecture

The application follows a modern full-stack architecture with clear separation between frontend and backend concerns:

- **Frontend**: React 18 with TypeScript, utilizing Vite for build tooling and development
- **Backend**: Express.js server with session-based authentication
- **Database**: PostgreSQL with Drizzle ORM for type-safe database operations
- **Styling**: Tailwind CSS with Radix UI components for consistent design system
- **State Management**: TanStack Query for server state management
- **Routing**: Wouter for lightweight client-side routing

## Key Components

### Authentication & Authorization
- Session-based authentication using Express sessions with bcrypt for password hashing
- Comprehensive role-based access control (RBAC) with four user roles:
  - `user`: Basic access to profile and published content
  - `user_admin`: User management capabilities
  - `blog_admin`: Blog content management
  - `super_admin`: Full system access
- Permission inheritance system with granular access control

### Database Schema
The application uses a PostgreSQL database with the following main entities:
- **Users**: Complete user management with role assignment and profile information
- **Blog Posts**: Full blog functionality with SEO optimization features
- **Invoices**: Basic invoice tracking (prepared for future billing integration)
- **User Sessions**: Session management for authentication

### External Integrations
- **Cloudinary**: Image upload, optimization, and delivery with automatic format selection
- **SendGrid**: Email service integration for notifications and communications
- **Microsoft Booking**: Embedded booking system for appointment scheduling
- **Multiple AI Providers**: Support for Anthropic, OpenAI, Google, Mistral, xAI, and Azure OpenAI

### Performance Optimization
- Code splitting with React.lazy for route-based and component-level optimization
- Comprehensive lazy loading strategy for non-critical components
- Image optimization through Cloudinary integration
- Performance monitoring with Web Vitals tracking
- SEO optimization with structured data and meta tag management

## Data Flow

### Authentication Flow
1. User submits credentials via login form
2. Server validates credentials against database using bcrypt
3. Session is created and stored server-side
4. Client receives session cookie for subsequent requests
5. Protected routes check session validity via middleware

### Content Management Flow
1. Admin users access role-specific dashboards
2. Content creation/editing occurs through rich text editors
3. Images are uploaded directly to Cloudinary with automatic optimization
4. Blog posts support full CRUD operations with SEO metadata
5. Real-time updates through TanStack Query cache invalidation

### Public Content Flow
1. Public pages are optimized for performance with lazy loading
2. Blog content is served with proper SEO headers and structured data
3. Images are automatically optimized and served through Cloudinary CDN
4. Booking integration provides seamless appointment scheduling

## External Dependencies

### Core Framework Dependencies
- React 18 with TypeScript for type-safe component development
- Express.js for backend API and server-side logic
- PostgreSQL with Drizzle ORM for database operations
- Vite for modern build tooling and development experience

### UI and Styling
- Tailwind CSS for utility-first styling approach
- Radix UI for accessible, unstyled UI primitives
- Framer Motion for smooth animations and transitions
- Lucide React for consistent iconography

### Data and State Management
- TanStack Query for server state management and caching
- React Hook Form with Zod for form validation
- Wouter for lightweight client-side routing

### External Services
- Cloudinary for image management and optimization
- SendGrid for email delivery services
- Microsoft Booking for appointment scheduling
- Multiple AI API providers for future automation features

## Deployment Strategy

The application is configured for deployment on Replit with the following setup:

### Environment Configuration
- Node.js 20 runtime with PostgreSQL 16
- Automatic database provisioning through Replit's PostgreSQL service
- Environment variables for external service configuration
- Production build optimization with esbuild

### Build Process
- Frontend builds to static assets using Vite
- Backend bundles using esbuild with external package handling
- Automatic deployment through Replit's autoscale infrastructure
- Production server runs on port 5000 with external port 80

### Development Workflow
- Hot module replacement for frontend development
- Automatic server restart for backend changes
- Database migrations managed through Drizzle Kit
- Task management through custom CLI tools

## Domain Configuration

### Domain Configuration
- **Primary Domain**: www.agent-factory.io
- **Additional Domains**: agent-factory.io, agent-factory.co, www.agent-factory.co
- **Redirect Strategy**: All domains redirect to www.agent-factory.io using 301 permanent redirects
- **Status**: Implemented and active
- **Implementation**: Server-side redirect middleware in Express.js
- **Date Added**: June 17, 2025
- **Date Implemented**: June 17, 2025

## Changelog

Changelog:
- June 17, 2025. Initial setup
- June 17, 2025. Added domain configuration requirements (pending verification)
- June 17, 2025. Implemented domain redirects - all domains now redirect to www.agent-factory.io
- June 17, 2025. Created comprehensive Agent Factory Design System & Style Guide for brand consistency
- June 20, 2025. Successfully migrated authentication system from internal database to pure Supabase authentication
- June 20, 2025. Fixed CORS configuration to support all Replit domains for development and production
- June 21, 2025. Implemented complete workspace management system with admin interface, API endpoints, database schema, and proper role-based access control

## User Preferences

Preferred communication style: Simple, everyday language.

**CRITICAL RULE: NEVER MAKE ANY CHANGES WITHOUT EXPLICIT USER APPROVAL**
- Always ask for permission before making any code changes, fixes, or modifications
- This applies to all changes regardless of size or perceived importance
- User must explicitly approve each change before implementation