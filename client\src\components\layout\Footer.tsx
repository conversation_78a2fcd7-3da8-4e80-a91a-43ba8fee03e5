import { Link } from "wouter";
import { Twitter, Linkedin } from "lucide-react";

const footerLinks = [
  { label: "Privacy", href: "/privacy" },
  { label: "Terms", href: "/terms" },
  { label: "Contact", href: "/contact" },
];

const socialLinks = [
  {
    label: "Twitter",
    href: "https://twitter.com/agentfactory",
    icon: Twitter,
  },
  {
    label: "LinkedIn", 
    href: "https://linkedin.com/company/agentfactory",
    icon: Linkedin,
  },
];

export function Footer() {
  return (
    <footer className="py-12 px-6 border-t border-[hsl(var(--accent-cyan))] border-opacity-20">
      <div className="max-w-7xl mx-auto">
        <div className="flex flex-col md:flex-row items-center justify-between space-y-6 md:space-y-0">
          {/* Footer Links */}
          <div className="flex flex-wrap items-center gap-6 text-[hsl(var(--text-light))]">
            {footerLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className="hover:text-[hsl(var(--accent-cyan))] transition-colors duration-300"
              >
                {link.label}
              </Link>
            ))}
          </div>
          
          {/* Social Links */}
          <div className="flex items-center space-x-4">
            {socialLinks.map((social) => {
              const Icon = social.icon;
              return (
                <a
                  key={social.href}
                  href={social.href}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="p-2 rounded-lg border border-[hsl(var(--accent-cyan))] border-opacity-30 hover:border-[hsl(var(--accent-cyan))] hover:border-opacity-100 transition-colors duration-300 group"
                >
                  <Icon className="w-5 h-5 text-[hsl(var(--accent-cyan))] opacity-70 group-hover:opacity-100" />
                </a>
              );
            })}
          </div>
        </div>
        
        <div className="mt-8 pt-8 border-t border-[hsl(var(--accent-cyan))] border-opacity-10 text-center">
          <p className="text-[hsl(var(--text-light))] text-sm">
            © 2024 Agent Factory. All rights reserved. Empowering businesses with intelligent automation.
          </p>
        </div>
      </div>
    </footer>
  );
}
