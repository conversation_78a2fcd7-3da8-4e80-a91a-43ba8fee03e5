// Set environment variables BEFORE any imports
process.env.SUPABASE_URL = 'https://test.supabase.co';
process.env.SUPABASE_SERVICE_ROLE_KEY = 'test-service-role-key';

// Mock Supabase BEFORE any imports
const mockSupabaseAuth = { getUser: jest.fn() };
const mockSupabaseFrom = jest.fn();

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: mockSupabaseAuth,
    from: mockSupabaseFrom,
  })),
}));

import request from 'supertest';
import express from 'express';
import { registerRoutes } from './routes';
import { IStorage } from './storage';
import { InMemoryStorage } from './storage-memory';
import type { User, Tenant, TenantMembership } from '../shared/schema';
import { Server } from 'http';

// --- Test Suite ---

describe('API Routes', () => {
  let app: express.Express;
  let server: Server;
  let storage: IStorage;

  const setupApp = async () => {
    storage = new InMemoryStorage();
    app = express();
    app.use(express.json());
    
    // Setup comprehensive Supabase mocks
    mockSupabaseFrom.mockImplementation(() => ({
      select: () => ({
        eq: () => ({
          order: () => ({
            // Mock the final result for blog posts
            then: (resolve: any) => resolve({ data: [], error: null })
          }),
          single: () => Promise.resolve({ data: null, error: null })
        })
      })
    }));
    
    server = await registerRoutes(app, storage);
  };

  beforeEach(async () => {
    jest.clearAllMocks();
    await setupApp();
  });

  afterEach((done) => {
    if (server && typeof server.close === 'function') {
      server.close(done);
    } else {
      done();
    }
  });

  // --- Health Check ---
  describe('GET /api/health', () => {
    it('should return 200 OK', async () => {
      const res = await request(app).get('/api/health');
      expect(res.status).toBe(200);
      expect(res.body.status).toEqual('ok');
    });
  });

  // --- Authentication Tests ---
  describe('Authentication Middleware', () => {
    it('should return 500 when Supabase not configured', async () => {
      // This test verifies the current behavior when env vars are missing
      const res = await request(app).get('/api/admin/stats');
      expect(res.status).toBe(500);
      expect(res.body.message).toContain('Authentication service not available');
    });
  });

  // For now, let's focus on the tests that don't require Supabase auth
  // We can expand these later once the basic infrastructure is working
  
  describe('Public Routes', () => {
    it('should serve robots.txt', async () => {
      const res = await request(app).get('/robots.txt');
      expect(res.status).toBe(200);
      expect(res.headers['content-type']).toContain('text/plain');
    });

    it('should serve sitemap.xml', async () => {
      const res = await request(app).get('/sitemap.xml');
      expect(res.status).toBe(200);
      expect(res.headers['content-type']).toContain('application/xml');
    });

    it('should get blog posts', async () => {
      // Mock the storage method
      (storage as InMemoryStorage).getBlogPosts = jest.fn().mockResolvedValue([
        { id: 1, title: 'Test Post', slug: 'test-post', isPublished: true }
      ]);
      
      const res = await request(app).get('/api/blog/posts');
      expect(res.status).toBe(200);
      expect(Array.isArray(res.body)).toBe(true);
    });
  });
}); 