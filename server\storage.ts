import { 
  type User, 
  type InsertUser,
  type InsertUserWithPassword,
  type UpdateUser,
  type BlogPost,
  type InsertBlogPost,
  type UpdateBlogPost
} from "@shared/schema";
import { InMemoryStorage } from './storage-memory';

export interface IStorage {
  // User methods
  getUser(id: number): Promise<User | undefined>;
  getUserByEmail(email: string): Promise<User | undefined>;
  updateUser(id: number, updates: Partial<UpdateUser>, tenantId?: number): Promise<User>;
  deleteUser(id: number): Promise<void>;
  getAllUsers(tenantId?: number): Promise<User[]>;

  // Blog methods
  getBlogPosts(publishedOnly?: boolean): Promise<BlogPost[]>;
  getBlogPostBySlug(slug: string): Promise<BlogPost | undefined>;
  createBlogPost(post: InsertBlogPost): Promise<BlogPost>;
  updateBlogPost(id: number, updates: UpdateBlogPost): Promise<BlogPost>;
  deleteBlogPost(id: number): Promise<boolean>;

  // Admin stats
  getAdminStats(): Promise<{
    totalUsers: number;
    totalPosts: number;
    draftPosts: number;
  }>;

  // Workspace Management Methods
  listWorkspaceTemplates(): Promise<any[]>;
  listAllWorkspaces(): Promise<any[]>;
  listUserWorkspaces(userId: number): Promise<any[]>;
  createWorkspace(workspaceData: any): Promise<any>;
  getWorkspaceById(workspaceId: string, userId?: number): Promise<any | null>;
  updateWorkspace(workspaceId: string, updateData: any): Promise<any | null>;
  deleteWorkspace(workspaceId: string): Promise<boolean>;
  archiveWorkspace(workspaceId: string): Promise<any | null>;
}

// Export InMemoryStorage for testing
export { InMemoryStorage };
